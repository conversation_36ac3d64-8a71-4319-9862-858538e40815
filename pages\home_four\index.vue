<template>
  <view class="page-container">
    <custom-navbar title="我的" :showBackBtn="false"
      background="linear-gradient(6deg, rgba(255,255,255,0) 18%, rgba(222,249,255,1) 100%)" />
    <!-- 弹窗组件 -->
    <vipVersion :pageShow="page_show" @show_model="handleShowModel" @down_close="page_show = false"
      :fastPowerRange="fastPowerRange" source="me" :numberType="numberType"></vipVersion>
    <InsufficientPermissions v-if="page_model" @closeModel="handleCloseModel"></InsufficientPermissions>

    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-info">
        <image src="/static/home_four/user.png" @click="handleAvatarClick"></image>
        <view>
          <template v-if="isLoggedIn">
            <view>用户{{ userInfo.bizId }}</view>
            <view class="user-id">ID: {{ userInfo.bizId }}</view>
          </template>
          <view v-else class="not-login">未登录</view>
        </view>
      </view>
      <view v-if="!isLoggedIn" class="action-button" @click="handleLoginButtonClick">
        登录
      </view>
      <view v-if="isLoggedIn" class="action-button" @click="handleLogoutButtonClick">
        退出
      </view>
    </view>

    <!-- 算力卡片 -->
    <view class="power-card">
      <view v-if="!isLoggedIn">
        <view class="power-empty-title">登录后查看算力</view>
        <view class="power-empty-subtitle">登录即可查看您的算力余额</view>
      </view>
      <view v-else-if="!powerValue">
        <view class="power-empty-title">您还没有算力哦</view>
        <view class="power-empty-subtitle">请联系我们进行购买~</view>
      </view>
      <view v-else class="power-content">
        <!-- 算力信息 -->
        <view class="power-info">
          <view class="power-header">
            <text class="power-label">算力余额：{{ powerValue.power }}</text>
            <text class="power-total">/ {{ powerValue.totalPower }}</text>
          </view>

          <!-- 进度条 -->
          <u-line-progress class="power-progress"
            :percentage="Math.min(100 - Math.max((powerValue.power / powerValue.totalPower) * 100, 0), 100)" height="8"
            active-color="#fefefe" inactive-color="#abffe5" :show-text="false"></u-line-progress>

          <!-- 算力统计 -->
          <view class="power-stats">
            <text class="power-stat-item">总算力：{{ powerValue.totalPower }}</text>
            <text class="power-stat-item power-used">已用：{{ powerValue.totalPower - powerValue.power }}</text>
          </view>
        </view>

        <!-- 购买按钮 -->
        <view class="power-buy-btn" @click="navigateToBuy">
          <text class="buy-text">购买</text>
        </view>
      </view>
    </view>

    <!-- 常用功能区 -->
    <view class="common-functions">
      <view class="section-title">常用功能</view>
      <view class="function-list">
        <view v-for="(item, index) in functionList" :key="index" class="function-item"
          @click="isLoggedIn ? handleFunctionClick(item) : handleLoginButtonClick()">
          <image :src="iconBasePath + item.src"></image>
          <view class="function-name">{{ item.name }}</view>
        </view>
      </view>
    </view>

    <!-- 我的数字人 -->
    <view class="content-section">
      <view class="section-header">
        <view>
          我的
          <text class="gradient-underline">数字人</text>
        </view>

        <view class="view-all" @click="isLoggedIn ? navigateToDrList() : handleLoginButtonClick()">
          <view class="show-more">
            <text>查看全部</text>
            <u-icon name="arrow-right" size="26rpx"></u-icon>
          </view>
        </view>
      </view>
      <view>
        <view v-if="!isLoggedIn" class="content-empty">
          <view class="empty-title">您还未登录，请先去登录吧</view>
          <view class="action-button" @click="handleLoginButtonClick">
            去登录
          </view>
        </view>
        <view v-else-if="digitalHumanLoading" class="skeleton-container">
          <scroll-view class="horizontal-scroll" scroll-x="true">
            <view class="skeleton-card" v-for="i in 3" :key="'skeleton-' + i">
              <view class="skeleton-animation"></view>
            </view>
          </scroll-view>
        </view>
        <view class="content-empty" v-else-if="digitalHumanList.length <= 0">
          <view class="empty-title">您还没有数字人，快去定制一个吧</view>
          <view class="action-button" @click="addDigitalHuman">
            定制数字人
          </view>
        </view>
        <view v-else>
          <scroll-view class="horizontal-scroll" scroll-x="true">
            <view class="add-card" @click="addDigitalHuman">
              <view class="add-icon">+</view>
            </view>
            <text class="scroll-spacer"></text>
            <view v-for="(item, index) in digitalHumanList" :key="index" class="item-card"
              @click="handleCardClick(item)" :style="{ backgroundImage: `url(${item.cover})` }">
              <image v-if="item.type == 'pro'" mode="heightFix" src="/static/index/pro_vip.png" class="pro-badge">
              </image>

              <view class="item-loading" v-if="item.status == 'training'">
                <view class="status-container">
                  <u-loading-icon mode="circle"></u-loading-icon>
                  <view>训练中</view>
                </view>
              </view>

              <view class="item-loading" v-if="item.status == 'censoring'">
                <view class="status-container">
                  <u-loading-icon mode="circle"></u-loading-icon>
                  <view>审核中</view>
                </view>
              </view>

              <view class="item-error" v-if="item.status == 'fail'" @click.stop="showFailReason(item)">
                <view class="status-container">
                  <u-icon name="info-circle" color="white"></u-icon>
                  <view class="status-text">训练失败</view>
                  <view class="view-reason">查看原因</view>
                </view>
              </view>

              <!-- 成功状态下的播放按钮 -->
              <view v-if="item.status === 'success'" class="play-button" @click.stop="playDigitalHuman(item)">
                <image :src="ossPath + '/project1_video_play.png'" mode="aspectFit"></image>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>

    <!-- 我的声音 -->
    <view class="content-section">
      <view class="section-header">
        <view>
          我的
          <text class="gradient-underline">声音</text>
        </view>
        <view class="view-all" @click="isLoggedIn ? navigateToMusicList() : handleLoginButtonClick()">
          <view class="show-more">
            <text>查看全部</text>
            <u-icon name="arrow-right" size="30rpx"></u-icon>
          </view>
        </view>
      </view>
      <view v-if="!isLoggedIn" class="content-empty">
        <view class="empty-title">您还未登录，请先去登录吧</view>
        <view class="action-button" @click="handleLoginButtonClick">
          去登录
        </view>
      </view>
      <view v-else-if="voiceLoading" class="skeleton-container">
        <scroll-view class="horizontal-scroll" scroll-x="true">
          <view class="skeleton-voice-card" v-for="i in 3" :key="'voice-skeleton-' + i">
            <view class="skeleton-animation"></view>
          </view>
        </scroll-view>
      </view>
      <view v-else-if="voiceList.length > 0">
        <scroll-view class="horizontal-scroll" scroll-x="true">
          <view class="add-card voice-add" @click="addVoice">
            <view class="add-icon">+</view>
          </view>
          <text class="scroll-spacer"></text>

          <view v-for="(item, index) in voiceList" :key="index" class="voice-card">
            <view class="voice-bg"></view>
            <image v-if="item.type == 'pro'" mode="heightFix" class="pro-badge-small" src="/static/index/pro_vip.png">
            </image>
            <view @click="stopAudio" v-if="currentPlayingId == item.outId" class="play-icon">
              <wave-loading color="#ffffff" barWidth="6rpx" />
            </view>

            <image @click="playAudio(item)" v-else class="play-icon" :src="ossPath + '/project1_video_play.png'">
            </image>

            <view class="voice-info">
              <view class="voice-name-container">
                <view class="voice-name ellipsis">{{ item.name }}</view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      <view v-else class="content-empty" @click="isLoggedIn ? addVoice() : handleLoginButtonClick()">
        <view class="empty-title">您还没有声音，快去定制吧</view>
        <view class="action-button">
          {{ isLoggedIn ? '定制声音' : '去登录' }}
        </view>
      </view>
    </view>

    <!-- 我的模版 -->
    <view class="content-section">
      <view class="section-header">
        <view>
          我的
          <text class="gradient-underline">模版</text>
        </view>
        <view class="view-all" @click="isLoggedIn ? navigateToTemplateList() : handleLoginButtonClick()">
          <view class="show-more">
            <text>查看全部</text>
            <u-icon name="arrow-right" size="30rpx"></u-icon>
          </view>
        </view>
      </view>
      <view v-if="!isLoggedIn" class="content-empty">
        <view class="empty-title">您还未登录，请先去登录吧</view>
        <view class="action-button" @click="handleLoginButtonClick">
          去登录
        </view>
      </view>
      <view v-else-if="templateLoading" class="skeleton-container">
        <scroll-view class="horizontal-scroll" scroll-x="true">
          <view class="skeleton-card" v-for="i in 3" :key="'template-skeleton-' + i">
            <view class="skeleton-animation"></view>
          </view>
        </scroll-view>
      </view>
      <view v-else-if="templateList.length > 0">
        <scroll-view class="horizontal-scroll" scroll-x="true">
          <view class="add-card" @click="addTemplate">
            <view class="add-icon">+</view>
          </view>
          <text class="scroll-spacer"></text>
          <view v-for="(item, index) in templateList" :key="index" class="item-card" @click="handleTemplateClick(item)">
            <image :src="item.preUrl" class="item-image"></image>
          </view>
        </scroll-view>
      </view>
      <view v-else class="content-empty" @click="isLoggedIn ? addTemplate() : handleLoginButtonClick()">
        <view class="empty-title">您还没有模版哦，立即创建一个吧</view>
        <view class="action-button">
          {{ isLoggedIn ? '定制模版' : '去登录' }}
        </view>
      </view>
    </view>

    <!-- 我的素材 -->
    <view class="content-section">
      <view class="section-header">
        <view>
          我的
          <text class="gradient-underline">素材</text>
        </view>
        <view class="view-all" @click="isLoggedIn ? navigateToMaterialsList() : handleLoginButtonClick()">
          <view class="show-more">
            <text>查看全部</text>
            <u-icon name="arrow-right" size="30rpx"></u-icon>
          </view>
        </view>
      </view>
      <view v-if="!isLoggedIn" class="content-empty">
        <view class="empty-title">您还未登录，请先去登录吧</view>
        <view class="action-button" @click="handleLoginButtonClick">
          去登录
        </view>
      </view>
      <view v-else-if="materialsLoading" class="skeleton-container">
        <scroll-view class="horizontal-scroll" scroll-x="true">
          <view class="skeleton-voice-card" v-for="i in 3" :key="'material-skeleton-' + i">
            <view class="skeleton-animation"></view>
          </view>
        </scroll-view>
      </view>
      <view v-else-if="materialsList.length > 0">
        <scroll-view class="horizontal-scroll" scroll-x="true">
          <view class="add-card voice-add" @click="addMaterial">
            <view class="add-icon">+</view>
          </view>
          <text class="scroll-spacer"></text>

          <view v-for="(item, index) in materialsList" :key="index" class="voice-card">
            <!-- {{ item.type }} -->
            <!-- 视频类型 -->
            <view v-if="item.type === 'video'" class="material-preview video-preview" @click="playMaterialVideo(item)">
              <image :src="`${item.fileUrl || item.outUrl}?x-oss-process=video/snapshot,t_1000,f_jpg,w_800,h_450`"
                class="material-image"></image>
              <!-- 播放按钮 -->
              <view class="play-button material-play">
                <image :src="ossPath + '/project1_video_play.png'" mode="aspectFit"></image>
              </view>
            </view>

            <!-- 音频类型 -->
            <view v-else-if="item.type === 'audio'" class="material-preview audio-preview">
              <image class="voice-bg" src="/static/music-background.png" />
              <!-- 播放/暂停按钮 -->
              <image @click="stopAudio" v-if="currentPlayingId == item.id || currentPlayingId == item.outId"
                class="play-icon" :src="ossPath + '/project1_home4_music_playing1.gif'"></image>
              <image @click="playMaterialAudio(item)" v-else class="play-icon"
                :src="ossPath + '/project1_video_play.png'">
              </image>
            </view>

            <!-- 图片类型 -->
            <view v-else class="material-preview">
              <image :src="item.fileUrl || item.outUrl" class="material-image"></image>
            </view>

            <!--						<view class="material-info">-->
            <!--							<view class="voice-name-container">-->
            <!--								<view class="voice-name ellipsis">{{ item.name || '素材' }}</view>-->
            <!--							</view>-->
            <!--						</view>-->
          </view>
        </scroll-view>
      </view>
      <view v-else class="content-empty" @click="isLoggedIn ? addMaterial() : handleLoginButtonClick()">
        <view class="empty-title">您还没有素材，快去添加吧</view>
        <view class="action-button">
          {{ isLoggedIn ? '添加素材' : '去登录' }}
        </view>
      </view>
    </view>

    <view class="page-bottom-space"></view>

    <!-- 弹出模态框 -->
    <projectModel v-if="failMessage" title="失败原因" :content="failReason" :btn="false">
      <view class="action-button dialog-button" @click="failMessage = false">
        知道了
      </view>
    </projectModel>
    <projectModel v-if="censoringShow" title="温馨提示" content="您的数字人定制正在训练中，如有疑问请联系我们。" save="删除" :btn="false">
      <view class="action-button dialog-button" @click="censoringShow = false">
        知道了
      </view>
    </projectModel>

    <projectModel v-if="trainingShow" title="温馨提示" content="您的数字人定制正在训练中，如有疑问请联系我们。" save="删除" :btn="false">
      <view class="action-button dialog-button" @click="trainingShow = false">
        知道了
      </view>
    </projectModel>
    <TabBar :index="3" active-color="black" custom-style="background:white" />
    <globalLoginModal />
    <!-- 通知栏目 -->
    <pageTitle />
  </view>
</template>

<script>
import { page_img_statr } from '../../api/getData.js'
import {
  getUserType,
  getVoiceList,
  getTemplateList,
  getPower
} from '../../api/numberUser/userType.js'
import { getMaterialsList } from '../../api/myMaterials/index.js'
import { mapGetters, mapActions } from 'vuex'
import { logout } from "@/api/user/user";
import TabBar from '@/components/finalTabBar/index.vue'
import WaveLoading from "@/components/wave-loader/index.vue";

export default {
  data() {
    return {
      // 登录检查相关
      refreshInterval: null,

      // 头像点击计数器
      avatarClickCount: 0,
      avatarClickTimer: null,

      // 状态控制
      failMessage: false,
      failReason: '',
      censoringShow: false,
      trainingShow: false,
      page_show: false,
      page_model: false,

      // 加载状态
      digitalHumanLoading: false,
      voiceLoading: false,
      templateLoading: false,
      materialsLoading: false,

      // 数据
      ossPath: this.$appConfig.appInfo.ossPath,
      iconBasePath: page_img_statr,
      powerValue: '',
      digitalHumanList: [],
      voiceList: [],
      templateList: [],
      materialsList: [],
      currentPlayingId: '',

      // 参数
      numberType: null,
      fastPowerRange: false,

      // 功能列表
      functionList: [{
        src: 'project1_home4_dhsl.png',
        name: '兑换算力'
      },
      {
        src: 'project1_home4_wdsl.png',
        name: '我的算力'
      },
      {
        src: 'project1_home4_xsjc.png',
        name: '新手教程'
      },
      {
        src: 'project1_home4_lxwm.png',
        name: '联系我们'
      },
      {
        src: 'project1_home4_gy.png',
        name: '关于'
      }],

      // 音频对象
      audioContext: null,
    }
  },
  computed: {
    ...mapGetters('user', ['isLogin', 'userInfo']),
    isLoggedIn() {
      return this.isLogin;
    }
  },
  components: {
    WaveLoading,
    TabBar
  },
  onLoad() {

    uni.hideTabBar()
    // 创建音频上下文
    this.audioContext = uni.createInnerAudioContext({
      useWebAudioImplement: true
    });
    // 监听音频结束
    this.audioContext.onEnded(() => {
      this.currentPlayingId = '';
    });

    // 加载数据
    if (this.isLoggedIn) {
      this.loadUserData()
    }
  },
  onShow() {
    // 设置当前选中索引
    this.$store.commit('fitment/SET_CURRENT_TAB_INDEX', 3)
    clearInterval(this.refreshInterval);
    this.page_show = false;

    if (this.isLoggedIn) {
      this.fetchDigitalHumanList();
      this.fetchVoiceList();
      this.fetchTemplateList();
      this.fetchMaterialsList();
      this.getUserInfo()
      this.fetchPowerData()
    }
  },
  onHide() {
    this.currentPlayingId = '';
    if (this.audioContext) {
      this.audioContext.stop();
    }
    clearInterval(this.refreshInterval);
  },
  onUnload() {
    // 页面卸载时销毁音频
    if (this.audioContext) {
      this.audioContext.destroy();
    }
  },
  beforeDestroy() {
    clearInterval(this.refreshInterval);
  },
  methods: {
    ...mapActions('user', ['getUserInfo']),
    /**
     * 处理头像点击事件
     */
    handleAvatarClick() {
      // 增加点击计数
      this.avatarClickCount++;

      // 清除之前的计时器
      if (this.avatarClickTimer) {
        clearTimeout(this.avatarClickTimer);
      }

      // 如果达到30次点击，跳转到开发工具页面
      if (this.avatarClickCount >= 30) {
        this.avatarClickCount = 0;
        uni.navigateTo({
          url: '/devTools/page/index'
        });
        return;
      }

      // 设置3秒后重置计数器的计时器
      this.avatarClickTimer = setTimeout(() => {
        this.avatarClickCount = 0;
      }, 3000);
    },
    /**
     * 处理登录按钮点击
     */
    handleLoginButtonClick() {
      this.$showLogin({
        success: (userData) => {
          // 更新用户信息
          this.user = userData;
          // 加载数据
          this.loadUserData();
        }
      });
    },
    handleLogoutButtonClick() {
      try {
        logout()
      } catch (e) {
        console.log(e)
      } finally {
        this.$store.dispatch('user/logout');
      }
    },

    /**
     * 加载用户数据
     */
    loadUserData() {
      this.fetchPowerData();
      this.fetchDigitalHumanList();
      this.fetchVoiceList();
      this.fetchTemplateList();
    },

    /**
     * 获取算力数据
     */
    fetchPowerData() {
      getPower().then(res => {
        this.powerValue = res.data;
      });
    },

    /**
     * 导航到购买页面
     */
    navigateToBuy() {
      uni.navigateTo({
        url: '/subpkg/home_four/phoneMe'
      });
    },

    /**
     * 添加声音
     */
    addVoice() {
      this.numberType = 'sound'
      this.checkUserTypeForClone('/subpkg/index/get_sound/index?type=pro');
    },

    /**
     * 添加模板
     */
    addTemplate() {
      uni.navigateTo({
        url: '/subpkg/index/get_radio/add_stencil'
      });
    },

    /**
     * 导航到数字人列表
     */
    navigateToDrList() {
      uni.navigateTo({
        url: '/subpkg/home_four/dr_list'
      });
    },

    /**
     * 导航到声音列表
     */
    navigateToMusicList() {
      uni.navigateTo({
        url: '/subpkg/home_four/music_list'
      });
    },

    /**
     * 导航到模板列表
     */
    navigateToTemplateList() {
      uni.navigateTo({
        url: '/subpkg/home_four/template_list'
      });
    },

    /**
     * 播放音频
     */
    playAudio(item) {
      this.currentPlayingId = item.outId;
      this.audioContext.src = item.demoUrl;
      this.audioContext.play();
    },

    /**
     * 停止音频
     */
    stopAudio() {
      this.currentPlayingId = '';
      this.audioContext.stop();
    },

    /**
     * 处理关闭模态框
     */
    handleCloseModel(value) {
      this.page_model = value;
    },

    /**
     * 处理显示模态框
     */
    handleShowModel(value) {
      this.page_show = false;
      this.page_model = value;
    },

    /**
     * 添加数字人
     */
    addDigitalHuman() {
      this.numberType = null;
      this.checkUserTypeForClone('/subpkg/index/numbe_user/index?type=pro');
    },

    /**
     * 检查用户类型是否可克隆
     */
    checkUserTypeForClone(redirectUrl) {
      getUserType().then(res => {
        if (res.data.enableFastClone == '0') {
          this.page_show = true;
          this.fastPowerRange = res.data.fastPowerRange;
        } else {
          uni.navigateTo({
            url: redirectUrl
          });
        }
      });
    },

    /**
     * 处理卡片点击
     */
    handleCardClick(item) {
      if (item.status == 'training') this.trainingShow = true;
      if (item.status == 'censoring') this.censoringShow = true;
    },

    /**
     * 处理模板点击
     */
    handleTemplateClick(item) {
      // 占位函数，后续可能会有具体实现
    },

    /**
     * 获取数字人列表
     */
    fetchDigitalHumanList() {
      this.digitalHumanLoading = true;
      this.$api.get_drList({
        pageNum: 1,
        pageSize: 5
      }).then((res) => {
        this.digitalHumanList = res.rows;
        // 检查是否有训练中或审核中的数字人
        const pendingItems = this.digitalHumanList.filter((item) => {
          return (item.status == 'training' || item.status == 'censoring');
        });

        // 如果有待处理的项目，设置轮询刷新
        if (pendingItems.length) {
          clearInterval(this.refreshInterval);
          this.refreshInterval = setInterval(() => {
            this.fetchDigitalHumanList();
          }, 20000); // 20秒刷新一次
        } else {
          clearInterval(this.refreshInterval);
        }
      }).catch((error) => {
        console.error('获取数字人列表失败:', error);
      }).finally(() => {
        this.digitalHumanLoading = false;
      });
    },

    /**
     * 获取声音列表
     */
    fetchVoiceList() {
      this.voiceLoading = true;
      getVoiceList({
        pageNum: 1,
        pageSize: 5
      }).then(res => {
        console.log("声音" + res)
        this.voiceList = res.rows;
      }).catch((error) => {
        console.error('获取声音列表失败:', error);
      }).finally(() => {
        this.voiceLoading = false;
      });
    },

    /**
     * 获取模板列表
     */
    fetchTemplateList() {
      this.templateLoading = true;
      getTemplateList({
        pageNum: 1,
        pageSize: 5
      }).then(res => {
        this.templateList = res.rows;
      }).catch((error) => {
        console.error('获取模板列表失败:', error);
      }).finally(() => {
        this.templateLoading = false;
      });
    },

    /**
     * 显示失败原因
     */
    showFailReason(item) {
      this.failMessage = true;
      this.failReason = item.reason;
    },

    /**
     * 处理功能列表点击
     */
    handleFunctionClick(item) {
      switch (item.name) {
        case '联系我们':
          uni.navigateTo({ url: '/subpkg/home_four/phoneMe' });
          break;
        case '兑换算力':
          uni.navigateTo({ url: '/subpkg/home_four/exchange' });
          break;
        case '我的算力':
          uni.navigateTo({ url: '/subpkg/home_four/myPower?power=' + this.powerValue.power });
          break;
        case '关于':
          uni.navigateTo({ url: '/subpkg/home_four/about' });
          break;
        case '新手教程':
          uni.navigateTo({ url: '/subpkg/home_four/tutorial' });
          break;
      }
    },

    /**
     * 添加播放数字人的方法
     */
    playDigitalHuman(item) {
      if (item.status === 'success' && item.demoUrl) {
        uni.navigateTo({
          url: `/subpkg/index/get_radio/look_my_video?url=${encodeURIComponent(item.demoUrl)}&type=Human`
        });
      }
    },

    /**
     * 添加素材
     */
    addMaterial() {
      uni.navigateTo({
        url: '/subpkg/myMaterials/index'
      });
    },

    /**
     * 导航到素材列表
     */
    navigateToMaterialsList() {
      uni.navigateTo({
        url: '/subpkg/myMaterials/index'
      });
    },

    /**
     * 获取素材列表
     */
    fetchMaterialsList() {
      this.materialsLoading = true;
      // 调用获取素材列表API
      getMaterialsList({
        pageNum: 1,
        pageSize: 5
      }).then(res => {
        this.materialsList = res.rows;
        console.log(this.materialsList, 'this.materialsList');

      }).catch(error => {
        console.error('获取素材列表失败:', error);
      }).finally(() => {
        this.materialsLoading = false;
      });
    },

    /**
     * 播放素材视频
     * @param {Object} item - 视频素材对象
     */
    playMaterialVideo(item) {
      const videoUrl = item.fileUrl || item.outUrl;
      if (videoUrl) {
        // 跳转到视频播放页面
        uni.navigateTo({
          url: `/subpkg/index/get_radio/look_my_video?url=${encodeURIComponent(videoUrl)}&name=${encodeURIComponent(item.name || '素材视频')}`
        });
      } else {
        uni.showToast({
          title: '视频链接无效',
          icon: 'none'
        });
      }
    },

    /**
     * 播放素材音频
     * @param {Object} item - 音频素材对象
     */
    playMaterialAudio(item) {
      // 如果有正在播放的音频，先停止它
      if (this.currentPlayingId) {
        this.audioContext.stop();
      }

      // 设置当前播放的音频ID
      this.currentPlayingId = item.id || item.outId;

      const audioUrl = item.fileUrl || item.outUrl || item.demoUrl;

      // 设置音频源并播放
      if (audioUrl) {
        this.audioContext.src = audioUrl;
        this.audioContext.play();

        // 监听播放结束事件
        this.audioContext.onEnded(() => {
          this.currentPlayingId = '';
        });

        // 监听播放错误事件
        this.audioContext.onError((err) => {
          console.error('音频播放错误:', err);
          this.currentPlayingId = '';
          // uni.showToast({
          //   title: '音频播放失败',
          //   icon: 'none'
          // });
        });
      } else {
        uni.showToast({
          title: '音频链接无效',
          icon: 'none'
        });
        this.currentPlayingId = '';
      }
    },
  }
}
</script>

<style scoped lang="scss">
.page-container {
  padding: 3vw;
  background-color: $project_1_bg_color;
  min-height: 100vh;
}

.action-button {
  color: white;
  background: linear-gradient(135deg, #22232C 0%, #0F0F0F 100%);
  border-radius: 16rpx;
  padding: 14rpx 30rpx;
  font-size: 28rpx;
  display: inline-block;
  height: 64rpx;
  line-height: 36rpx;
  text-align: center;

  &:active {
    opacity: 0.9;
  }
}

.dialog-button {
  margin: auto;
  display: block;
  width: 200rpx;
}

/* 用户信息卡片 */
.user-card {
  display: flex;
  justify-content: space-between;
  padding: 32rpx;
  align-items: center;
  background: white;
  border-radius: 20rpx;

  .user-info {
    display: flex;
    align-items: center;

    image {
      width: 80rpx;
      height: 80rpx;
      margin-right: 30rpx;
    }

    .user-id {
      font-size: 24rpx;
      color: #919191;
    }
  }
}

/* 算力卡片 */
.power-card {
  margin-top: 40rpx;
  background: linear-gradient(135deg, #def8d8 0%, #2CEFB4 100%);
  border-radius: 24rpx;
  padding: 32rpx 48rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 160rpx;

  .power-empty-title {
    font-weight: 600;
    font-size: 28rpx;
    color: #333333;
  }

  .power-empty-subtitle {
    margin-top: 10rpx;
    font-weight: 500;
    font-size: 24rpx;
    color: #999999;
  }

  .power-content {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
  }

  .power-info {
    width: 430rpx;
    height: 96rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .power-header {
      display: flex;
      align-items: center;

      .power-label {
        font-size: 28rpx;
        font-weight: bold;
        color: #333333;
        line-height: 40rpx;
      }

      .power-total {
        font-size: 24rpx;
        font-weight: bold;
        color: #333333;
        margin-top: 4rpx;
      }
    }

    .power-progress {
      width: 430rpx;
      height: 8rpx;
      margin-top: 8rpx;
    }

    .power-stats {
      display: flex;
      justify-content: space-between;
      margin-top: 8rpx;

      .power-stat-item {
        font-size: 24rpx;
        color: #666666;
        line-height: 32rpx;
      }

      .power-used {
        text-align: right;
      }
    }
  }

  .power-buy-btn {
    // background: url(/static/home_four/buy_button_bg.png) 100% no-repeat;
    background: linear-gradient(135deg, #22232C 0%, #0F0F0F 100%);
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    background-size: 100% 100%;
    width: 112rpx;
    height: 64rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .buy-text {
      font-size: 24rpx;
      color: #ffffff;
      line-height: 36rpx;
    }
  }
}

/* 常用功能区域 */
.common-functions {
  padding: 32rpx;
  background: white;
  margin-top: 32rpx;
  border-radius: 20rpx;

  .section-title {
    font-weight: 600;
    margin-bottom: 24rpx;
    font-size: 28rpx;
  }
}

.function-list {
  display: flex;
  justify-content: space-around;
  align-items: center;

  .function-item {
    text-align: center;

    image {
      width: 46rpx;
      height: 46rpx;
    }

    .function-name {
      font-size: 24rpx;
      color: #919191;
      margin-top: 10rpx;
    }
  }
}

/* 内容为空状态 */
.content-empty {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;

  .empty-title {
    margin-top: 10rpx;
    margin-bottom: 20rpx;
    font-size: 24rpx;
    color: #919191;
  }
}

/* 区域标题 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 32rpx;
  font-weight: 600;
  margin: 10rpx 0;

  .view-all {
    font-size: 24rpx;
    color: #666666;
    display: flex;
    align-items: center;

    .show-more {
      display: flex;
      align-items: center;
      border-radius: 32rpx;
      padding: 10rpx;
      font-size: 24rpx;
      color: #999999;

      text {
        margin-right: 10rpx;
      }
    }
  }
}

/* 横向滚动容器 */
.horizontal-scroll {
  white-space: nowrap;
  width: 100%;
  position: relative;
  margin-top: 25rpx;

  .add-card {
    width: 160rpx;
    height: 228rpx;
    margin-right: 10rpx;
    border-radius: 20rpx;
    background: #EFEFEF;
    display: inline-block;
    display: flex;
    justify-content: space-around;
    align-items: center;
    position: absolute;
    box-sizing: border-box;

    .add-icon {
      color: rgb(153, 153, 153);
      font-size: 80rpx;
    }
  }

  .voice-add {
    width: 150rpx;
    height: 150rpx;
  }

  .scroll-spacer {
    width: 170rpx;
    display: inline-block;
  }

  .item-card {
    display: inline-block;
    position: relative;
    width: 160rpx;
    height: 228rpx;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 20rpx;
    margin-right: 10rpx;


    .item-image {
      width: 150rpx;
      height: 220rpx;
      margin-right: 10rpx;
      border-radius: 20rpx;
    }

    .pro-badge {
      height: 30rpx;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 99;
    }

    .play-button {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      padding: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100rpx;
      height: 100rpx;
      z-index: 2;

      image {
        width: 70rpx;
        height: 70rpx;
      }

      &:active {
        transform: translate(-50%, -50%) scale(0.95);
        opacity: 1;
      }
    }
  }

  .item-loading,
  .item-error {
    width: 100%;
    height: 100%;
    font-size: 24rpx;
    color: white;
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 20rpx;
    backface-visibility: hidden;

    .status-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      will-change: transform;
      transform: translateZ(0);
    }

    .status-text {
      margin: 6rpx 0;
    }

    .view-reason {
      padding: 5rpx;
      border: 2rpx solid white;
      border-radius: 10rpx;
      font-size: 22rpx;
    }
  }

  /* 音频卡片 */
  .voice-card {
    position: relative;
    width: 150rpx;
    height: 150rpx;
    display: inline-block;
    margin-right: 10rpx;
    border-radius: 20rpx;

    .pro-badge-small {
      height: 30rpx;
      position: absolute;
      top: 0;
      left: 0;
    }

    .voice-bg {
      width: 150rpx;
      height: 150rpx;
      border-radius: 15rpx;
      background-repeat: no-repeat;
      background-size: cover;
      // background: linear-gradient(135deg, #98f4e9, #33f3b7, #49f399);
    }

    .play-icon {
      width: 70rpx;
      height: 70rpx;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .voice-info {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;

      .voice-name-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 0 0 16rpx 16rpx;
        background: rgba(0, 0, 0, 0.3);
        color: white;
        font-size: 22rpx;
        padding: 4rpx;
      }
    }
  }
}

/* 内容区域 */
.content-section {
  margin: 32rpx 0;
  background-color: white;
  padding: 10rpx 5rpx 25rpx 15rpx;
  border-radius: 20rpx;
}

/* 通用底部间距 */
.page-bottom-space {
  padding-top: 100rpx;
}

/* 下划线样式 */
.gradient-underline {
  position: relative;
  display: inline;

  &::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -10rpx;
    width: 100%;
    height: 6rpx;
    background: $project_1_bg;
  }
}

/* 未登录提示 */
.login-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 200rpx;

  .login-prompt-image {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 30rpx;
  }

  .login-prompt-text {
    font-size: 30rpx;
    color: #666;
    margin-bottom: 40rpx;
  }

  .login-btn {
    width: 300rpx;
    height: 80rpx;
    line-height: 80rpx;
    background-color: #000;
    color: #fff;
    border-radius: 40rpx;
    font-size: 28rpx;
  }
}

/* 辅助类 */
.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.not-login {
  color: #999;
}

/* 骨架屏样式 */
.skeleton-container {
  margin-top: 25rpx;
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

.skeleton-animation {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
  border-radius: 20rpx;
}

/* 数字人和模板骨架屏卡片 */
.skeleton-card {
  display: inline-block;
  width: 160rpx;
  height: 228rpx;
  margin-right: 10rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

/* 声音骨架屏卡片 */
.skeleton-voice-card {
  display: inline-block;
  width: 150rpx;
  height: 150rpx;
  margin-right: 10rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

/* 素材卡片样式 */
.material-preview {
  width: 150rpx;
  height: 150rpx;
  border-radius: 15rpx;
  overflow: hidden;
  position: relative;
}

.material-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.material-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #e8f8ff, #c6f3ff, #a4eeff);
  color: #333;
  font-size: 24rpx;
}

.material-info {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
}

/* 新增样式 */
.video-preview {
  position: relative;
}

.play-button.material-play {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 100rpx;
  z-index: 2;

  image {
    width: 60rpx;
    height: 60rpx;
  }

  &:active {
    transform: translate(-50%, -50%) scale(0.95);
    opacity: 1;
  }
}

.audio-preview {
  position: relative;
}

.voice-bg {
  width: 150rpx;
  height: 150rpx;
  border-radius: 15rpx;
  background: linear-gradient(135deg, #98f4e9, #33f3b7, #49f399);
}

.play-icon {
  width: 50rpx;
  height: 50rpx;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
