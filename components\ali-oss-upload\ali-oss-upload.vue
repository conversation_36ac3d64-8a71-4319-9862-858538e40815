<template>
  <view class="container">
    <button @click="handleUploadImage">上传图片</button>
    <button @click="handleUploadVideo">上传视频</button>
    <button @click="handleUploadBase64">上传Base64</button>
    <button @click="handleUploadCanvas">上传Canvas</button>
    <button @click="handleVideoToAudio">本地视频提取音频</button>
    <button @click="handleVideoUrlToAudio">视频文件路径提取音频</button>
    <button @click="handleAudio">上传音频文件</button>

  </view>
</template>

<script>
import {
  chooseAndUploadImage,
  chooseAndUploadVideo,
  chooseAndUploadAudio,
  uploadBase64ToOss,
  uploadCanvasToOss,
} from 'utils/ali-oss.js';
import { handleVideoFileToAudio, handleVideoToAudio,} from "../../utils/video-to-audio";

export default {
  methods: {
    async handleAudio() {
      try {
        // 上传音频
        const url = await chooseAndUploadAudio();

        uni.showToast({
          title: '上传成功',
          icon: 'success'
        });

        console.log('音频URL:', url);
      } catch (error) {
        uni.showToast({
          title: error.message || '上传失败',
          icon: 'none'
        });
      }
    },
   async  handleVideoUrlToAudio(){
     await handleVideoFileToAudio("https://xiao-dr.oss-cn-beijing.aliyuncs.com/temp-video/1746512659420-BBhYMcKSlaXddf6ed4bbc93613c68c8525e21bbddf98.mp4")

    },
    async handleVideoToAudio(){
      await handleVideoToAudio()
    },
    async handleUploadImage() {
      try {
        // 上传图片
        const url = await chooseAndUploadImage({
          dir: 'images', // 上传目录
          extension: ['.jpg', '.jpeg', '.png'], // 允许的文件类型
          maxSize: 5 * 1024 * 1024 // 最大5MB
        });

        uni.showToast({
          title: '上传成功',
          icon: 'success'
        });

        console.log('图片URL:', url);
      } catch (error) {
        uni.showToast({
          title: error.message || '上传失败',
          icon: 'none'
        });
      }
    },

    async handleUploadVideo() {
      try {
        // 上传视频
        const url = await chooseAndUploadVideo({
          dir: 'videos', // 上传目录
          maxSize: 100 * 1024 * 1024, // 最大100MB
          maxDuration: 60 // 最大60秒,
        });

        uni.showToast({
          title: '上传成功',
          icon: 'success'
        });

        console.log('视频URL:', url);
      } catch (error) {
        uni.showToast({
          title: error.message || '上传失败',
          icon: 'none'
        });
      }
    },

    // 上传base64字符串
    async handleUploadBase64() {
      try {
        // 示例base64字符串
        const base64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...';

        const url = await uploadBase64ToOss(base64, 'dr_template', 'test.png');

        uni.showToast({
          title: '上传成功',
          icon: 'success'
        });

        console.log('文件URL:', url);
      } catch (error) {
        uni.showToast({
          title: error.message || '上传失败',
          icon: 'none'
        });
      }
    },

    // 上传canvas内容
    async handleUploadCanvas() {
      try {
        const url = await uploadCanvasToOss({
          dir: 'images',
          fileName: 'canvas.png',
          fileType: 'png',
          quality: 0.8
        });

        uni.showToast({
          title: '上传成功',
          icon: 'success'
        });

        console.log('文件URL:', url);
      } catch (error) {
        uni.showToast({
          title: error.message || '上传失败',
          icon: 'none'
        });
      }
    }
  }
}
</script>
