// export const projectUrl = 'http://frp.server.xiaoai.world/app'
// export const projectUrl = 'http://************:8081/app';
export const projectUrl = 'https://xiaoa.co/prod-api/app';
// export const projectUrl = 'http://245ofna21147.vicp.fun/app';
// export const projectUrl = 'http://localhost:8080/app';
import store from '@/store'

export const requests = (options) => {
    // let token = 'eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxNTI0MzkwOTcwNCIsImxvZ2luX3VzZXJfa2V5IjoiNWYyOWVkMzgtMmViZi00N2QyLTk5ODUtMjI1ZmVlOGVmOWY5In0.sgwjEKQd8evJn6BWgRhEDnnZg4erwNK5PzSvLrGxka4xiun0t044tZVhFBp-Tn7wp4UACexQUYLvB8c_cyXcmg'
    let token = store.state.user.token
    // 设置默认请求头
    options.header = options.header || {};
    if (token) {
        options.header['Authorization'] = token
    }
    options.url = projectUrl + options.url


    return new Promise((resolve, reject) => {
        // uni.showLoading({
        // 	mask:true
        // })
        uni.request({
            ...options,
            success: (res) => {
                const { code, msg } = res.data;
                //网络请求异常
                if (res.statusCode == 404) {
                    uni.showToast({ title: '系统升级中,请稍等~', icon: 'none' })
                    reject(res.data || new Error("系统升级中,请稍后重试~"));
                    return;
                }
                //业务请求异常
                switch (code) {
                    case 200:
                        resolve(res.data);
                        uni.hideLoading();
                        break;
                    case 401:
                    case 403:
                        // 清空用户信息
                        uni.setStorageSync('userToken', '')
                        uni.setStorageSync('user', null)

                        // 重置登录状态 - 使用Vuex中的方法替代直接commit
                        store.dispatch('user/logout').then(() => {
                            // 强制返回登录页
                            uni.showToast({
                                title: '登录已过期，请重新登录',
                                icon: 'none',
                                duration: 1500,
                                complete: () => {
                                    // 延迟跳转，让用户看到提示
                                    setTimeout(() => {
                                        uni.switchTab({
                                            url: '/pages/index/index'
                                        });
                                    }, 500);
                                }
                            });
                        }).catch(error => {
                            console.error('登出处理失败:', error);
                        });

                        reject(new Error('登录过期，请重新登录'));
                        break;

                    case 500:
                        //fuck!，加个定时器 避免业务层很多hide隐藏该提示
                        setTimeout(() => {
                            uni.showToast({ title: '网络异常,请稍后重试', icon: 'none' });
                        }, 100);
                        reject(res.data || new Error("服务器错误"));
                        break;

                    case 10010:
                        uni.showModal({
                            title: '温馨提示',
                            content: msg + ' 是否前往充值算力？',
                            success(modalRes) {
                                if (modalRes.confirm) {
                                    uni.navigateTo({
                                        url: '/subpkg/home_four/exchange'
                                    });
                                }
                            }
                        });
                        reject(new Error(msg)); // 重要：不要忘了 reject
                        break;
                    default:
                        uni.showToast({
                            title: msg || '请求失败 请稍后重试~',
                            icon: 'none'
                        });
                        reject(new Error(msg || '请求失败'));
                        break;
                }
            },
            fail: (err) => {
                uni.hideLoading();
                reject(err);
            }
        });

    });
};
