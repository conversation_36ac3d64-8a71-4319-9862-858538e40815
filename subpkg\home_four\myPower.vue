<template>
  <view class="page">
    <!-- 顶部切换标签 -->
    <view class="tab-section">
      <view class="text-wrapper_1" :class="{ active: current === 0 }" @click="change(0)">
        <view class="tab-text">我的算力</view>
      </view>
      <view class="text-wrapper_2" :class="{ active: current === 1 }" @click="change(1)">
        <view class="tab-text">消耗明细</view>
      </view>
    </view>

    <!-- 算力余额展示 -->
    <view v-if="current === 0" class="power_box">
      <!-- 算力余额总计卡片 -->
      <view class="power_header">
        <view class="section_2">
          <view class="section_3">
            <image class="power-icon" src="/static/算力.png"></image>
            <view class="power_title">{{ totalPower }}</view>
          </view>
          <view class="power_sub">算力余额总计</view>
        </view>
        <view class="rule-btn" @click="toRule">
          <image class="rule-icon" src="/static/问号.png"></image>
          <view class="rule-text">算力规则</view>
        </view>
        <image class="tab-icon" src="/static/铜币.png"></image>
      </view>

      <!-- 算力年包信息卡片 (默认隐藏) -->
      <view class="power-package" v-if="showPowerPackage">
        <view class="package-header">
          <view class="package-title">
            <image class="package-icon"
              src="https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/2d72f940f27849e6b14ed8d25799bdad_mergeImage.png">
            </image>
            <view class="package-name">3,000算力年包</view>
          </view>
          <view class="package-expiry">到期时间：{{ expiryDate }}</view>
        </view>

        <view class="balance-info">
          <view class="balance-text">算力余额：{{ remainingPower }}</view>
          <view class="total-text">/ 3,000</view>
        </view>

        <view class="progress-bar">
          <view class="progress-inner" :style="{ width: (remainingPower / 3000 * 100) + '%' }"></view>
        </view>

        <view class="package-footer">
          <view class="total-power">总算力：2,000</view>
          <view class="used-power">已用：1,865</view>
        </view>
      </view>
    </view>

    <!-- 消耗明细列表 -->
    <view v-else class="consumption_list" :style="{ height: listHeight + 'px' }">
      <!-- 添加loading骨架屏 -->
      <view v-if="isLoading" class="loading-skeleton">
        <view class="skeleton-item" v-for="i in 3" :key="i">
          <view class="skeleton-left">
            <view class="skeleton-title"></view>
            <view class="skeleton-time"></view>
          </view>
          <view class="skeleton-amount"></view>
        </view>
      </view>

      <!-- 空状态提示 -->
      <view v-else-if="consumptionDetails.length === 0 && !isLoading" class="empty-state">
        <image src="/static/empty.png" class="empty-icon"></image>
        <view class="empty-text">暂无消耗记录</view>
      </view>

      <!-- 数据列表 -->
      <scroll-view v-else scroll-y="true" :style="{ height: listHeight + 'px' }" @scrolltolower="handleScrollToBottom"
        @refresherrefresh="handleRefresh" refresher-enabled="true" :refresher-triggered="isRefreshing">
        <view v-for="(item, index) in consumptionDetails" :key="index" class="consumption_item">
          <view class="consumption_left">
            <view class="consumption_title">{{ item.remark }}</view>
            <view class="consumption_time">{{ item.createTime }}</view>
          </view>
          <text class="consumption_power" :class="{ increase: item.amount < 0 }">{{ item.amount }}</text>
        </view>

        <!-- 加载更多提示 -->
        <view class="load-more-section">
          <view v-if="loadMoreStatus === 'loading'" class="loading-more">
            <view class="loading-spinner"></view>
            <text>加载中...</text>
          </view>
          <view v-else-if="loadMoreStatus === 'nomore'" class="no-more">已加载全部数据</view>
          <view v-else-if="hasMore" class="tap-more" @click="loadMore">点击加载更多</view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import { getPowerLog } from "@/api/user/user";
import {getPower} from "@/api/numberUser/userType";

export default {
  data() {
    return {
      current: 0,
      title_list: ['我的算力', '消耗明细'],
      totalPower: 0,
      remainingPower: 1350,
      expiryDate: "2026-04-08 23:59:59",
      consumptionDetails: [],
      queryParams: {
        pageNumber: 1,
        pageSize: 50
      },
      loadMoreStatus: 'loadmore', // 加载状态：loadmore-可加载更多, loading-加载中, nomore-没有更多
      hasMore: true,
      showPowerPackage: false, // 默认隐藏算力年包信息
      isLoading: false, // 是否正在加载数据
      isRefreshing: false, // 是否正在刷新
      listHeight: 500, // 默认列表高度
      windowHeight: 0, // 窗口高度
      headerHeight: 0, // 头部元素高度
    };
  },
  created() {
    // 获取系统信息
    const systemInfo = uni.getSystemInfoSync();
    this.windowHeight = systemInfo.windowHeight;

    // 首次计算列表高度
    this.$nextTick(() => {
      this.calculateListHeight();
    });

    // 监听窗口大小变化(在支持的平台)
    uni.onWindowResize(() => {
      setTimeout(() => {
        this.calculateListHeight();
      }, 100);
    });
  },
  onReady() {
    // 组件挂载完成后再次计算以保证准确性
    this.$nextTick(() => {
      this.calculateListHeight();
    });
  },
  onLoad(options) {
    if(options.power){
      this.totalPower = options.power
    }else {
      getPower().then(res => {
        this.totalPower  = res.data.power;
      });
    }
  },
  // 添加下拉刷新支持
  onPullDownRefresh() {
    if (this.current === 1) {
      this.refreshConsumptionList();
    } else {
      uni.stopPullDownRefresh();
    }
  },
  // 触底加载更多
  onReachBottom() {
    if (this.current === 1 && this.hasMore && this.loadMoreStatus !== 'loading') {
      this.loadMore();
    }
  },
  methods: {
    // 计算列表高度
    calculateListHeight() {
      const query = uni.createSelectorQuery().in(this);

      // 获取页面容器、顶部切换标签和安全区域信息
      query.select('.page').boundingClientRect();
      query.select('.tab-section').boundingClientRect();
      query.exec((res) => {
        if (res && res[0] && res[1]) {
          const pageRect = res[0];
          const tabRect = res[1];

          // 获取系统信息以计算安全区域
          const systemInfo = uni.getSystemInfoSync();
          const safeAreaBottom = systemInfo.safeAreaInsets ? systemInfo.safeAreaInsets.bottom : 0;
          const paddingBottom = 32; // 页面下方内边距rpx

          // 计算列表高度
          this.listHeight = pageRect - tabRect.height - uni.upx2px(paddingBottom) - safeAreaBottom;
        }
      });
    },
    // 处理滚动到底部事件
    handleScrollToBottom() {
      if (this.hasMore && this.loadMoreStatus !== 'loading') {
        this.loadMore();
      }
    },
    // 处理下拉刷新
    handleRefresh() {
      this.isRefreshing = true;
      this.refreshConsumptionList();
    },
    toRule() {
      uni.navigateTo({
        url: '/subpkg/home_four/power_rule'
      });
    },
    change(index) {
      this.current = index;
      if (index === 1) {
        this.refreshConsumptionList();
        // 切换到明细列表时，重新计算高度
        this.$nextTick(() => {
          this.calculateListHeight();
        });
      }
    },
    // 刷新消耗列表
    refreshConsumptionList() {
      this.queryParams.pageNumber = 1;
      this.consumptionDetails = [];
      this.hasMore = true;
      this.fetchConsumptionDetails(true);
    },
    fetchConsumptionDetails(isRefresh = false) {
      this.isLoading = this.consumptionDetails.length === 0; // 仅在首次加载时显示骨架屏
      this.loadMoreStatus = 'loading';

      getPowerLog(this.queryParams).then(res => {
        console.log(res);
        this.isLoading = false;

        if (!res.rows || res.rows.length === 0) {
          this.hasMore = false;
          this.loadMoreStatus = 'nomore';
        } else {
          this.consumptionDetails = [...this.consumptionDetails, ...res.rows];
          this.loadMoreStatus = 'loadmore';
          this.hasMore = res.rows.length >= this.queryParams.pageSize;
        }

        // 如果是下拉刷新，停止刷新动画
        if (isRefresh) {
          uni.stopPullDownRefresh();
          this.isRefreshing = false;
        }
      }).catch(err => {
        console.error("获取消耗明细失败:", err);
        this.isLoading = false;
        this.loadMoreStatus = 'loadmore';

        if (isRefresh) {
          uni.stopPullDownRefresh();
          this.isRefreshing = false;
        }

        // 显示错误提示
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
    },
    loadMore() {
      if (this.hasMore && this.loadMoreStatus !== 'loading') {
        this.queryParams.pageNumber += 1;
        this.fetchConsumptionDetails();
      }
    }
  }
};
</script>

<style scoped lang="scss">
.page {
  padding: 32rpx;
  background-color: #F3F5F8;
  min-height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  /* 顶部切换标签样式 */
  .tab-section {
    background-color: #ffffff;
    border-radius: 40rpx;
    width: 304rpx;
    height: 72rpx;
    display: flex;
    justify-content: space-between;
    margin-bottom: 32rpx;

    .text-wrapper_1,
    .text-wrapper_2 {
      border-radius: 32rpx;
      height: 56rpx;
      width: 144rpx;
      margin: 8rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      &.active {
        background-image: linear-gradient(135deg, rgba(34, 35, 44, 1) 0, rgba(15, 15, 15, 1) 100%);

        .tab-text {
          color: #ffffff;
          font-family: MiSans-Demibold;
        }
      }

      .tab-text {
        font-size: 24rpx;
        font-family: MiSans-Medium;
        font-weight: 500;
        color: #999999;
        line-height: 32rpx;
      }
    }
  }

  /* 算力总计卡片样式 */
  .power_box {
    margin-top: 15rpx;
    flex: 1;
    display: flex;
    flex-direction: column;

    .power_header {
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx 48rpx;
      height: 160rpx;
      background: linear-gradient(135deg, #def8d8 0%, #2CEFB4 100%);
      border-radius: 24rpx;
      border: 2rpx solid #FFFFFF;

      .section_2 {
        width: 204rpx;
        height: 96rpx;

        .section_3 {
          display: flex;
          align-items: center;

          .power-icon {
            width: 40rpx;
            height: 40rpx;
            margin-right: 8rpx;
          }

          .power_title {
            font-size: 40rpx;
            font-family: Chill Round Gothic-Bold;
            font-weight: 700;
            color: #333333;
            line-height: 64rpx;
          }
        }

        .power_sub {
          font-size: 24rpx;
          font-family: MiSans-Demibold;
          color: #666666;
          line-height: 32rpx;
        }
      }

      .rule-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(255, 255, 255, 0.5);
        border-radius: 12rpx;
        padding: 14rpx 24rpx;

        .rule-icon {
          width: 24rpx;
          height: 24rpx;
          margin-right: 8rpx;
        }

        .rule-text {
          font-size: 20rpx;
          font-family: MiSans-Demibold;
          color: #333333;
          line-height: 28rpx;
        }
      }

      .tab-icon {
        position: absolute;
        right: 160rpx;
        top: 8rpx;
        width: 144rpx;
        height: 144rpx;
        // 整体调淡
        opacity: 0.5;
      }
    }

    /* 算力年包卡片样式 */
    .power-package {
      background-color: #FFFFFF;
      border-radius: 24rpx;
      padding: 32rpx;
      margin-top: 40rpx;
      flex: 1;

      .package-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 48rpx;

        .package-title {
          display: flex;
          align-items: center;

          .package-icon {
            width: 32rpx;
            height: 32rpx;
            margin-right: 8rpx;
          }

          .package-name {
            font-size: 32rpx;
            font-family: MiSans-Demibold;
            color: #21BD74;
            line-height: 48rpx;
          }
        }

        .package-expiry {
          font-size: 20rpx;
          font-family: MiSans-Medium;
          color: #999999;
          line-height: 32rpx;
        }
      }

      .balance-info {
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;

        .balance-text {
          font-size: 28rpx;
          font-family: MiSans-Demibold;
          color: #333333;
          line-height: 40rpx;
        }

        .total-text {
          font-size: 24rpx;
          font-family: MiSans-Demibold;
          color: #999999;
          line-height: 32rpx;
          margin-left: 8rpx;
        }
      }

      .progress-bar {
        width: 100%;
        height: 8rpx;
        background-color: #f0f0f0;
        border-radius: 4rpx;
        margin-bottom: 8rpx;
        overflow: hidden;

        .progress-inner {
          height: 100%;
          background-image: linear-gradient(135deg, #93FF78 0%, #2CEFB4 100%);
          border-radius: 4rpx;
        }
      }

      .package-footer {
        display: flex;
        justify-content: space-between;
        margin-top: 8rpx;

        .total-power,
        .used-power {
          font-size: 24rpx;
          font-family: MiSans-Medium;
          color: #666666;
          line-height: 32rpx;
        }
      }
    }
  }

  /* 消耗明细列表样式 */
  .consumption_list {
    background-color: #ffffff;
    padding: 20px;
    border-radius: 20px;
    margin-top: 15rpx;
    overflow: hidden;
    box-sizing: border-box;
    flex: 1;
    display: flex;
    flex-direction: column;

    // 骨架屏样式
    .loading-skeleton {
      flex: 1;

      .skeleton-item {
        display: flex;
        justify-content: space-between;
        padding: 20rpx 0;
        border-bottom: 1px solid #eee;

        .skeleton-left {
          display: flex;
          flex-direction: column;

          .skeleton-title {
            width: 200rpx;
            height: 30rpx;
            background: #f0f0f0;
            border-radius: 4rpx;
            animation: skeleton-loading 1.5s infinite ease-in-out;
          }

          .skeleton-time {
            width: 150rpx;
            height: 24rpx;
            background: #f0f0f0;
            border-radius: 4rpx;
            margin-top: 10rpx;
            animation: skeleton-loading 1.5s infinite ease-in-out;
          }
        }

        .skeleton-amount {
          width: 80rpx;
          height: 30rpx;
          background: #f0f0f0;
          border-radius: 4rpx;
          animation: skeleton-loading 1.5s infinite ease-in-out;
        }
      }
    }

    // 空状态样式
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60rpx 0;
      height: 100%;
      flex: 1;

      .empty-icon {
        width: 180rpx;
        height: 180rpx;
        opacity: 0.5;
      }

      .empty-text {
        font-size: 28rpx;
        color: #999999;
        margin-top: 20rpx;
      }
    }

    // 加载更多区域样式
    .load-more-section {
      padding: 20rpx 0;
      text-align: center;

      .loading-more {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        color: #999999;

        .loading-spinner {
          width: 30rpx;
          height: 30rpx;
          border: 4rpx solid #f3f3f3;
          border-top: 4rpx solid #21BD74;
          border-radius: 50%;
          margin-right: 10rpx;
          animation: spin 1s linear infinite;
        }
      }

      .no-more,
      .tap-more {
        font-size: 24rpx;
        color: #999999;
      }

      .tap-more {
        color: #21BD74;
      }
    }

    // 列表项样式保持不变
    .consumption_item {
      display: flex;
      justify-content: space-between;
      padding: 20rpx 0;
      border-bottom: 1px solid #eee;

      .consumption_left {
        display: flex;
        flex-direction: column;

        .consumption_title {
          font-size: 30rpx;
          font-weight: bold;
          color: #333333;
        }

        .consumption_time {
          font-size: 24rpx;
          margin-top: 10rpx;
          color: #999999;
        }
      }

      .consumption_power {
        color: #21BD74;
        font-weight: bold;

        &.increase {
          color: #ff6b6b;
        }
      }
    }
  }
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    opacity: 0.6;
  }

  50% {
    opacity: 0.8;
  }

  100% {
    opacity: 0.6;
  }
}

/* 加载图标旋转动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
