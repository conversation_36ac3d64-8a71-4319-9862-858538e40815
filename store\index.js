import Vue from 'vue'
import Vuex from 'vuex'
import user from './modules/user'
import fitment from './modules/fitment'
import createPersistedState from 'vuex-persistedstate'

Vue.use(Vuex)

const store = new Vuex.Store({
    modules: {
        user,
        fitment
    },
    state: {
		//公共的变量，这里的变量不能随便修改，只能通过触发mutations的方法才能改变
		vuex_unReadmessage:0,

		message_save:false,
		aiMessage:{},

		numberUserVideo:'',
		numberUserMusic:'',
		numberLicensedVideos:'',
		musicData:'',//name,id,volume,url

		drVideoCropInfo:{}, //专业版数字人裁剪参数
		
		// 登录弹窗状态
		loginModalState: {
			visible: false,
			successCallback: null,
			cancelCallback: null,
			failCallback: null
		},
		
		project_imag_url:'https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/app_tab/'
	},
    mutations: {
		setDrVideoCropInfo(state,value){
			state.drVideoCropInfo = value
		},
		setMessage_save(state,value){
			state.message_save = value
		},
		setAiMessage(state,value){
			state.aiMessage = value
		},
		setunReadmessage(state,number){
			state.vuex_unReadmessage = number
		},
		SetNumberUserVideo(state,url){
			state.numberUserVideo = url
		},
		SetNumberUserMusic(state,url){
			state.numberUserMusic = url
		},
		setNumberLicensedVideos(state,url){
			state.numberLicensedVideos = url
		},
		setMusicData(state,data){
			state.musicData  = data
		},
		// 设置登录弹窗状态
		setLoginModalState(state, payload) {
			state.loginModalState = { ...state.loginModalState, ...payload }
		}
	},
    actions: {
		//相当于异步的操作,不能直接改变state的值，只能通过触发mutations的方法才能改变
	},
    plugins: [
        createPersistedState({
            storage: {
                getItem: key => uni.getStorageSync(key),
                setItem: (key, value) => uni.setStorageSync(key, value),
                removeItem: key => uni.removeStorageSync(key)
            },
            key: 'xiaoa-store', // 存储的键名
            paths: [  // 指定要持久化的状态路径
                'user.userInfo',
                'user.token',
                'user.isLoggedIn',
                'user.channelBadgeCount',
                'musicData',
                'message_save',
                'aiMessage',
                'fitment.fitmentData',
                'fitment.currentTabIndex'
            ]
        })
    ]
})
export default store
