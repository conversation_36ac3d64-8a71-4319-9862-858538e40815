<template>
  <view>
    <fixedTabBar v-if="!isCustomTemplate && !loading" :index="index" :customStyle="customStyle"
      :activeColor="activeColor" :inactiveColor="inactiveColor" />
    <customTabBar v-if="isCustomTemplate && !loading" />
  </view>
</template>

<script>
import fixedTabBar from '../fixedTabBar/index.vue'
import customTabBar from '../customTabBar/index.vue'
import { mapGetters, mapState } from 'vuex'
export default {
  name: 'finalTabBar',
  components: {
    fixedTabBar,
    customTabBar
  },
  props: {
    index: {
      type: [String, Number],
      default: 0
    },
    activeColor: {
      type: String,
      default: 'white'
    },
    inactiveColor: {
      type: String,
      default: 'black'
    },
    customStyle: {
      type: String,
      default: 'background:rgb(29,36,41);height:100rpx;'
    },
    imagePrefix: {
      type: String,
      default: 'project1_home'
    },
    tabItems: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapGetters('fitment', [
      'isCustomTemplate'
    ]),
    ...mapState('fitment', ['loading']),
  }
}
</script>

<style lang="scss" scoped></style>
