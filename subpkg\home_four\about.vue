<template>
  <view class="about-container">
    <view class="version-box">
      <view>版本号：1.0.0</view>
    </view>
    <view class="main">
      <view @click="toAgreement('userAgreement')" class="agreement">用户协议</view>
      <view @click="toAgreement('policy')" class="policy">隐私政策</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {};
  },
  methods: {
    toAgreement(type) {
      uni.navigateTo({
        url: `/subpkg/home_four/agreement?type=${type}`
      });
    },
  }
};
</script>

<style lang="scss">
.about-container {
  .version-box {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100rpx;
    font-size: 28rpx;
    color: #666666;
  }

  .main {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .agreement,
    .policy {
      width: 100%;
      height: 100rpx;
      line-height: 100rpx;
      text-align: center;
      font-size: 32rpx;
      color: #333333;
      border-top: 1rpx solid #eee;
    }
  }
}
</style>
