<template>
  <view class="rule-container">
    <!-- 图片展示卡片 -->
    <view class="card rule-image-card">
      <image :src="powerRule" style="width: 100%;" mode="widthFix"></image>
    </view>
    
    <!-- 规则说明卡片 -->
    <view class="card rule-content-card">
      <!-- 问题1 -->
      <view class="qa-item">
        <view class="question">1. 数字人视频和智能剪辑视频有什么差别？</view>
        <view class="answer">
          数字人视频是指不带标题、字幕、音乐、音效、素材和剪辑效果的纯口播画面视频，便于下载后用于二次加工；而智能剪辑视频是带有标题、字幕、音乐、音效、素材和剪辑效果的视频，完成度较高，可直接发布分享。
        </view>
      </view>
      
      <!-- 问题2 -->
      <view class="qa-item">
        <view class="question">2. 为什么每次生成视频后，总会退款一部分算力？</view>
        <view class="answer">
          当您开始生成视频时，系统会按照对应计费标准来预估该视频消耗的算力值（可能存在细微偏差）。当视频生成完成后，系统会退回实际消耗算力与预估消耗算力的偏差部分，即多扣的算力值。
        </view>
      </view>
      
      <!-- 问题3 -->
      <view class="qa-item">
        <view class="question">3. 视频生成失败了，还会扣我的算力吗？</view>
        <view class="answer">
          生成失败的视频，会退回对应预扣的算力。
        </view>
      </view>
      
      <!-- 问题4 -->
      <view class="qa-item">
        <view class="question">4. 声音克隆什么时候扣费？</view>
        <view class="answer">
          <view>如果是单独克隆声音，只有在最终保存声音时才会扣费。</view>
          <view class="mt-10">在过程中试听效果或重新录制，均不会产生扣费。保存后的声音将可以在数字人视频制作中使用。</view>
          <view class="mt-10">对于数字人定制，则会在提交所有内容时统一扣费。</view>
        </view>
      </view>
      
      <!-- 问题5 -->
      <view class="qa-item">
        <view class="question">5. 为什么数字人定制失败后只退回部分算力？</view>
        <view class="answer">
          <view>数字人定制包括形象定制和声音定制。如果形象定制失败但声音定制成功，仅退回与形象定制相关的算力。</view>
          <view class="mt-10">重新提交时，只需重新训练形象，无需再次训练声音，系统默认会选中"不克隆的声音"。</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getPageConfigInfo } from '../../api/app/config'
export default {
  data() {
    return {
      powerRule: ''
    };
  },
  onLoad() {
    getPageConfigInfo({
      type: 'powerRule'
    }).then(res => {
      this.powerRule = JSON.parse(res.data).descImg
    })
  }
}
</script>

<style lang="scss">
.rule-container {
  min-height: 100vh;
  padding: 30rpx;
  font-weight: normal;
  background: #f8f9fc;
}

.card {
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 30rpx;
  transition: all 0.3s ease;
}

.rule-image-card {
  padding: 20rpx;
}

.rule-content-card {
  padding: 30rpx;
}

.qa-item {
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 1px dashed #eee;
  
  &:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }
}

.question {
  font-size: 32rpx;
  font-weight: 600;
  // color: #01f675;
  margin-bottom: 20rpx;
}

.answer {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

.mt-10 {
  margin-top: 20rpx;
}
</style>
