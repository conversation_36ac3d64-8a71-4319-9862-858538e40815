<template>
  <view class="materials-page">
    <!-- 页面标题区域 -->
    <custom-navbar :title="selectionMode ? '选择素材' : '我的素材'" :isBack="true" :isBlack="true"
      backgroundColor="#f3f5f8"></custom-navbar>

    <!-- 工具栏区域 -->
    <view class="toolbar">
      <!-- 展示模式切换 -->
      <view class="display-mode">
        <view class="display-mode__item display-mode__item--active">
          <text>全部展示</text>
        </view>
        <!-- <view class="display-mode__item">
          <text>分组展示</text>
        </view> -->
      </view>

      <!-- 上传按钮 -->
      <view class="upload-btn" @click="handleUpload">
        <text>上传素材</text>
      </view>
    </view>

    <!-- 分类选项卡 -->
    <view class="material-tabs">
      <view class="material-tabs__list">
        <text v-for="(type, name) in typeMap" :key="name"
              :class="['material-tabs__item', selectedCategory === name ? 'material-tabs__item--active' : '']"
              @click="switchCategory(name)">{{ name }}</text>
        <text :class="['material-tabs__item', selectedCategory === '音乐' ? 'material-tabs__item--active' : '']"
              @click="switchCategory('音乐')" v-if="!selectionMode">音乐</text>
      </view>
      <view v-if="!selectionMode" class="material-tabs__select-btn" @click="enterSelectionDelMode">
        <text>选择</text>
      </view>
    </view>

    <!-- 素材网格 -->
    <scroll-view scroll-y class="material-grid"
      :style="selectionMode ? 'height: calc(100vh - 450rpx)' : 'height: calc(100vh - 350rpx)'"
      @scrolltolower="loadMore">
      <!-- 骨架屏 -->
      <view v-if="loading" class="material-grid__content">
        <view v-for="i in 8" :key="'skeleton-' + i" class="material-item skeleton-item">
          <view class="skeleton-image skeleton-animate"></view>
          <view class="skeleton-name skeleton-animate"></view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else-if="materialData.length === 0" class="empty-state">
        <image src="/static/materials/empty.png" class="empty-image"></image>
        <text class="empty-text">暂无素材，快去上传吧～</text>
      </view>

      <!-- 素材网格 -->
      <view v-else class="material-grid__content">
        <view v-for="(item, index) in materialData" :key="item.id || index" class="material-item" :class="{
          'material-item--image': item.type === 'image',
          'material-item--video': item.type === 'video',
          'material-item--audio': item.type === 'audio',
          'material-item--selected': selectionMode && isSelected(item)
        }"
          :style="{ backgroundImage: item.outUrl ? `url(${item.outUrl.includes('.mp4') ? item.fileUrl + '?x-oss-process=video/snapshot,t_1000,f_jpg,w_800,h_450' : item.outUrl})` : '' }"
          @click="selectionMode ? toggleSelectMaterial(item) : previewMaterial(item)">
          <!-- 视频素材 -->
          <view class="material-item__overlay" v-if="item.type === 'video'" >
            <image class="play-icon"
              src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/app_tab/project1_video_play.png"></image>
          </view>

          <!-- 音频素材 -->
          <view class="material-item__audio" v-if="item.type === 'audio'">
            <!-- 播放/暂停按钮 -->
            <image @click.stop="stopAudio" v-if="currentPlayingId == item.id || currentPlayingId == item.outId"
              class="music-play-icon" :src="ossPath + '/project1_home4_music_playing1.gif'"></image>
            <image  v-else class="music-play-icon"
              :src="ossPath + '/project1_video_play.png'"></image>
          </view>

          <!-- 选择按钮 -->
          <view v-if="selectionMode" class="material-item__select"
            :class="{ 'material-item__select--active': isSelected(item) }">
            <image class="select-icon"
              :src="isSelected(item) ? '/static/goxuanzhong.png' : '/static/materials/checkbox.png'"></image>
          </view>

          <!-- 材料名称 -->
          <view class="material-item__name">
            <text class="name-text">{{ item.name }}</text>
          </view>

          <!-- 加载中状态 -->
          <view class="loading-overlay" v-if="item.loading">
            <u-loading-icon mode="circle" size="30"></u-loading-icon>
          </view>
        </view>
      </view>

      <!-- 加载更多提示 -->
      <view class="loading-more" v-if="loading && materialData.length > 0">
        <u-loading-icon mode="circle" size="28"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 无更多数据提示 -->
      <view class="no-more" v-if="noMoreData && materialData.length > 0">
        <text class="no-more-text">— 没有更多数据了 —</text>
      </view>
    </scroll-view>

    <!-- 选择模式下的底部操作栏 -->
    <view class="selection-bar" v-if="selectionMode">
      <view class="selection-info">
        <text>已选择 {{ selectedItems.length }} 项</text>
      </view>
      <view class="selection-actions">
        <view class="selection-btn selection-btn--cancel" @click="cancelSelection">
          <text>取消</text>
        </view>
        <view v-if="showDeleteBtn" class="selection-btn selection-btn--delete" :class="{disabled: selectedItems.length === 0}" @click="deleteSelectedMaterials">
          <text>删除</text>
        </view>
        <view v-else class="selection-btn selection-btn--confirm" @click="confirmSelection">
          <text>确定</text>
        </view>
      </view>
    </view>

    <!-- 底部存储信息 -->
    <!-- <view class="storage-info">
      <text class="storage-info__text">已用容量：{{storageInfo.used}}GB / {{storageInfo.total}}GB</text>
      <text class="storage-info__expand" @click="handleExpand">扩容</text>
    </view> -->
  </view>
</template>

<script>
import { getMaterialsList, uploadMaterial,deleteMaterial } from '@/api/myMaterials/index.js'
import { handleMediaUpload } from "@/utils/upload-media.js"
import { getMeterialResult } from "@/api/myMaterials"

export default {
  data() {
    return {
      materialData: [], // 素材列表数据
      selectedCategory: '全部素材', // 当前选中的分类
      displayMode: '全部展示',
      showDeleteBtn:false,
      // 分页参数
      pageParams: {
        pageNum: 1,
        pageSize: 30,
        type: '', // 默认为空，表示全部
      },
      totalPages: 0,
      loading: false, // 加载状态
      noMoreData: false, // 是否还有更多数据
      // 类型映射
      typeMap: {
        '全部素材': '',
        '视频': 'video',
        '图片': 'image'
      },
      // 选择模式相关
      selectionMode: false,
      selectedItems: [], // 已选素材列表
      maxSelect: 1, // 最大可选数量，默认单选
      returnSource: null, // 返回数据的来源页面
      // 轮询计时器
      pollTimers: {},
      // 最大轮询次数
      maxPollCount: 30,
      // 音频播放相关
      audioContext: null,
      currentPlayingId: '', // 当前正在播放的音频ID
      ossPath: ''
    };
  },
  onLoad(options) {
    // 判断是否为选择模式
    if (options && options.select) {
      this.selectionMode = true;
      if (options.maxSelect) {
        this.maxSelect = parseInt(options.maxSelect);
      }
      if (options.returnSource) {
        this.returnSource = options.returnSource;
      }
    }

    // 初始化音频上下文
    this.audioContext = uni.createInnerAudioContext({
      useWebAudioImplement: true
    });
    // 监听音频结束
    this.audioContext.onEnded(() => {
      this.currentPlayingId = '';
    });

    // 设置OSS路径
    this.ossPath = this.$appConfig.appInfo.ossPath;

    // 加载素材数据
    this.loadMaterials();
  },
  onReachBottom() {
    // 触底加载更多
    if (!this.loading && !this.noMoreData) {
      this.loadMore();
    }
  },
  onPullDownRefresh() {
    // 下拉刷新
    this.loadMaterials();
  },
  onHide() {
    // 隐藏页面时停止音频播放
    if (this.audioContext) {
      this.audioContext.stop();
      this.currentPlayingId = '';
    }
  },
  onUnload() {
    // 清除所有轮询计时器
    Object.keys(this.pollTimers).forEach(id => {
      clearInterval(this.pollTimers[id]);
    });
    this.pollTimers = {};

    // 销毁音频上下文
    if (this.audioContext) {
      this.audioContext.destroy();
    }
  },
  onShow() {
    // 确保OSS路径正确设置
    if (this.$appConfig && this.$appConfig.appInfo) {
      this.ossPath = this.$appConfig.appInfo.ossPath;
    }

    // 停止任何可能正在播放的音频
    if (this.audioContext && this.currentPlayingId) {
      this.audioContext.stop();
      this.currentPlayingId = '';
    }
  },
  methods: {
    enterSelectionDelMode() {
      this.selectionMode = true;
      this.showDeleteBtn = true;
      this.maxSelect=9;
      this.selectedItems = [];
      this.loadMaterials(); //不要问为什么，因为删除没有ID
    },
    async deleteSelectedMaterials() {
      if (this.selectedItems.length === 0) {
        uni.showToast({ title: '请选择要删除的素材', icon: 'none' });
        return;
      }
      uni.showModal({
        title: '提示',
        content: `确定要删除选中的${this.selectedItems.length}项素材吗？`,
        success: async (res) => {
          if (res.confirm) {
            const ids = this.selectedItems.map(item => item.systemId).join(',');
            await deleteMaterial(ids)
            uni.showToast({ title: '删除成功', icon: 'none' });
            this.selectedItems = [];
            this.showDeleteBtn =false;
            this.selectionMode =false;
            this.loadMaterials(); // 重新加载
          }
        }
      });
    },
    // 加载素材数据
    loadMaterials(refresh = true) {
      console.log('加载素材列表');

      if (refresh) {
        this.materialData = [];
        this.pageParams.pageNum = 1;
        this.noMoreData = false;
      }

      if (this.loading) return;
      this.loading = true;

      // 根据选中的类型设置请求参数
      // this.pageParams.type = this.typeMap[this.selectedCategory];
      if (this.pageParams.type === '' && this.returnSource === 'selectMaterial') {
        //来源为选择素材页面,查询全部素材的时候不查询音频
        this.pageParams.remark = 'excludeAudio';
      } else {
        this.pageParams.remark = ''
      }
      // 调用API请求函数获取素材列表
      getMaterialsList(this.pageParams).then(res => {
        console.log(res, 'res');

        if (res.code === 200) {
          const { rows, total } = res;

          // 处理返回的数据
          if (refresh) {
            this.materialData = rows || [];
          } else {
            this.materialData = [...this.materialData, ...(rows || [])];
            console.log(this.materialData, 'this.materialData');

          }

          this.totalPages = Math.ceil(total / this.pageParams.pageSize);
          this.noMoreData = this.pageParams.pageNum >= this.totalPages;
        } else {
          uni.showToast({
            title: res.msg || '获取素材列表失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
        console.error('获取素材列表失败:', err);
      }).finally(() => {
        this.loading = false;
        uni.stopPullDownRefresh();
      });
    },

    // 加载更多
    loadMore() {
      if (this.noMoreData) return;

      this.pageParams.pageNum++;
      this.loadMaterials(false);
    },

    // 切换分类
    switchCategory(category) {
      console.log("音乐:"+category);
      if (this.selectedCategory === category) return;

      this.selectedCategory = category;

      // 如果切换到音乐类别，需要特殊处理
      if (category === '音乐') {
        console.log("3");
        this.pageParams.type = 'audio';
      } else {
        // 否则使用typeMap获取类型
        this.pageParams.type = this.typeMap[category];
      }

      // 更新素材列表
      this.loadMaterials(true);
    },

    // 处理上传
    handleUpload() {
      this.uploadMedia();
    },

    // 上传媒体文件
    async uploadMedia() {
      try {
        uni.showLoading({
          title: '文件上传中...'
        });

        // 调用上传方法
        const { fileUrl, materialId } = await handleMediaUpload();

        if (fileUrl) {
          // 创建新素材并添加到列表
          const newMaterial = {
            id: materialId,
            outId: materialId,
            outUrl: fileUrl,
            fileUrl: fileUrl,
            name: fileUrl.substring(fileUrl.lastIndexOf('/') + 1),
            type: fileUrl.toLowerCase().endsWith('.mp4') ? 'video' : 'image',
            loading: true
          };

          // 添加到素材列表
          this.materialData.unshift(newMaterial);

          // 开始轮询检查上传结果
          this.startPolling(materialId);

          uni.hideLoading();
          uni.showToast({
            title: '上传成功',
            icon: 'success'
          });
        }
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '上传失败，请重试',
          icon: 'none'
        });
        console.error('上传失败:', error);
      }
    },

    // 开始轮询检查保存结果
    startPolling(materialId) {
      let pollCount = 0;

      // 创建轮询定时器
      this.pollTimers[materialId] = setInterval(async () => {
        try {
          // 轮询次数+1
          pollCount++;

          // 超过最大轮询次数，显示失败
          if (pollCount > this.maxPollCount) {
            clearInterval(this.pollTimers[materialId]);
            delete this.pollTimers[materialId];

            // 查找素材并标记失败
            const materialIndex = this.materialData.findIndex(item => item.id === materialId);
            if (materialIndex !== -1) {
              this.materialData[materialIndex].loading = false;
              this.materialData[materialIndex].failed = true;

              uni.showToast({
                title: '素材处理失败',
                icon: 'none'
              });
            }
            return;
          }

          // 获取素材保存结果
          const result = await getMeterialResult(materialId);
          console.log('轮询素材结果:', result);

          // 如果处理完成
          if (result.data) {
            clearInterval(this.pollTimers[materialId]);
            delete this.pollTimers[materialId];

            // 查找素材并更新状态
            const materialIndex = this.materialData.findIndex(item => item.id === materialId);
            if (materialIndex !== -1) {
              this.materialData[materialIndex].loading = false;

              // 更新素材信息
              if (result.data.outUrl) {
                this.materialData[materialIndex].outUrl = result.data.outUrl;
                this.materialData[materialIndex].id = result.data.outId;
                this.materialData[materialIndex].outId = result.data.outId;
              }
              if (result.data.fileUrl) {
                this.materialData[materialIndex].fileUrl = result.data.fileUrl;
              }
              if (result.data.name) {
                this.materialData[materialIndex].name = result.data.name;
              }
              if (result.data.type) {
                this.materialData[materialIndex].type = result.data.type;
              }
            }
          }
        } catch (error) {
          console.error('轮询素材状态失败:', error);
        }
      }, 3000); // 每3秒轮询一次
    },

    // 切换显示模式
    switchDisplayMode(mode) {
      this.displayMode = mode;
      // 更新显示方式
    },

    // 获取素材类型图标
    getMaterialTypeIcon(type) {
      switch (type) {
        case 'video': return '/static/materials/play.png';
        case 'audio': return '/static/materials/music.png';
        default: return '';
      }
    },

    // 预览素材
    previewMaterial(item) {
      console.log('previewMaterial');

      console.log(item, 'item');

      switch (item.type) {
        case 'image':
          // 预览图片
          uni.previewImage({
            urls: [item.outUrl]
          });
          break;
        case 'video':
          // 播放视频
          this.playMaterialVideo(item);
          break;
        case 'audio':
          // 播放音频
          this.playMaterialAudio(item);
          break;
      }
    },

    // 播放素材视频
    playMaterialVideo(item) {
      const videoUrl = item.fileUrl || item.outUrl;
      if (videoUrl) {
        // 跳转到视频播放页面
        uni.navigateTo({
          url: `/subpkg/index/get_radio/look_my_video?url=${encodeURIComponent(videoUrl)}&name=${encodeURIComponent(item.name || '素材视频')}`
        });
      } else {
        uni.showToast({
          title: '视频链接无效',
          icon: 'none'
        });
      }
    },

    // 播放素材音频
    playMaterialAudio(item) {
      // 如果有正在播放的音频，先停止它
      if (this.currentPlayingId) {
        this.audioContext.stop();
      }

      // 如果点击的是正在播放的音频，则停止播放
      if (this.currentPlayingId === item.outId || this.currentPlayingId === item.id) {
        this.currentPlayingId = '';
        return;
      }

      // 设置当前播放的音频ID
      this.currentPlayingId = item.id || item.outId;

      const audioUrl = item.fileUrl || item.outUrl || item.demoUrl;

      // 设置音频源并播放
      if (audioUrl) {
        this.audioContext.src = audioUrl;
        this.audioContext.play();

        // 监听播放错误事件
        this.audioContext.onError((err) => {
          console.error('音频播放错误:', err);
          this.currentPlayingId = '';
          uni.showToast({
            title: '音频播放失败',
            icon: 'none'
          });
        });
      } else {
        uni.showToast({
          title: '音频链接无效',
          icon: 'none'
        });
        this.currentPlayingId = '';
      }
    },

    // 停止音频播放
    stopAudio() {
      if (this.audioContext) {
        this.audioContext.stop();
        this.currentPlayingId = '';
      }
    },

    // 选择模式相关方法
    // 判断某个素材是否被选中
    isSelected(item) {
      if(this.showDeleteBtn){
        return this.selectedItems.some(selectedItem => selectedItem.systemId === item.id);
      }else {
        return this.selectedItems.some(selectedItem => selectedItem.id === item.outId);
      }
    },

    // 切换素材选择状态
    toggleSelectMaterial(item) {
      let index;
      if(this.showDeleteBtn){
        index = this.selectedItems.findIndex(selectedItem => selectedItem.systemId === item.id);
      }else {
        index = this.selectedItems.findIndex(selectedItem => selectedItem.id === item.outId);
      }
      if (index === -1) {
        // 添加到选择列表
        if (this.selectedItems.length >= this.maxSelect) {
          // 如果达到最大选择数量，替换之前的选择
          if (this.maxSelect === 1) {
            this.selectedItems = [];
          } else {
            uni.showToast({
              title: `最多只能选择${this.maxSelect}项`,
              icon: 'none'
            });
            return;
          }
        }

        // 构建要传递的素材对象
        const materialInfo = {
          id: item.outId,
          coverUrl: item.fileUrl,
          type: item.type,
          systemId:item.id
        };

        this.selectedItems.push(materialInfo);
      } else {
        // 从选择列表中移除
        this.selectedItems.splice(index, 1);
      }
    },

    // 取消选择
    cancelSelection() {
      if(this.showDeleteBtn){
        this.selectedItems = []
        this.showDeleteBtn =false;
        this.selectionMode =false;
      }else {
        uni.navigateBack();
      }

    },

    // 确认选择
    confirmSelection() {
      if (this.selectedItems.length === 0) {
        uni.showToast({
          title: '请至少选择一项素材',
          icon: 'none'
        });
        return;
      }

      console.log('选中的素材:', this.selectedItems);

      // 返回到selectMaterial页面，并传递选中的素材数据
      const userMaterialInfo = this.selectedItems;

      // 使用事件总线通知
      if (this.returnSource === 'selectMaterial') {
        // 传递给selectMaterial.vue
        uni.$emit('user-material-selected', userMaterialInfo);
      }

      // 返回上一页
      uni.navigateBack({
        delta: 1
      });
    }
  }
};
</script>

<style lang="scss">
.materials-page {
  background-color: #f3f5f8;
  min-height: 100vh;
  padding: 0 32rpx;
  position: relative;

  // 工具栏区域
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 32rpx;
    height: 72rpx;

    // 显示模式切换
    .display-mode {
      display: flex;
      background-color: #ffffff;
      border-radius: 40rpx;
      // width: 304rpx;
      height: 72rpx;
      overflow: hidden;

      &__item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 144rpx;
        height: 56rpx;
        margin: 8rpx;
        border-radius: 32rpx;

        text {
          font-size: 24rpx;
          color: #999999;
        }

        &--active {
          background-image: linear-gradient(135deg, #222328 0%, #0f0f0f 100%);

          text {
            color: #ffffff;
          }
        }
      }
    }

    // 上传按钮
    .upload-btn {
      background-image: linear-gradient(135deg, #07e3d2 0%, #01f770 100%);

      border-radius: 32rpx;
      width: 160rpx;
      height: 64rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      text {
       color: #f6f6f6;
        font-size: 24rpx;
        font-weight: bold;
      }
    }
  }

  // 分类选项卡
  .material-tabs {
    display: flex;
    position: relative;
    margin-top: 24rpx;
    margin-bottom: 36rpx;

    &__list {
      display: flex;
      flex: 1;
      align-items: center;
    }
    &__item {
      font-size: 28rpx;
      color: #999999;
      margin-right: 32rpx;
      line-height: 48rpx;
      position: relative;

      &--active {
        color: #333333;
        font-size: 32rpx;
        font-weight: bold;

        &::after {
          content: '';
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          bottom: -6rpx;
          width: 40rpx;
          height: 6rpx;
          background-image: linear-gradient(135deg, #222328 19.84%, #0f0f0f 100%);
          border-radius: 4rpx 2rpx 2rpx 4rpx;
        }
      }
    }
    &__select-btn{
      margin-left: auto;
      color: #04da79;
      font-size: 30rpx;
      padding: 0 16rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      cursor: pointer;
    }
  }

  // 素材网格
  .material-grid {
    height: calc(100vh - 350rpx);

    // 空状态
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding-top: 200rpx;

      .empty-image {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 20rpx;
      }

      .empty-text {
        font-size: 28rpx;
        color: #999999;
      }
    }

    // 素材内容布局
    &__content {
      display: grid;
      grid-template-columns: repeat(auto-fill, 159rpx);
      gap: 16rpx;
      justify-content: start;
    }
  }

  // 素材项
  .material-item {
    width: 160rpx;
    height: 160rpx;
    border-radius: 16rpx;
    position: relative;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    margin-bottom: 16rpx;

    &--image {
      // 图片项默认背景
      background-color: #f5f5f5;
    }

    &--video {
      // 视频项默认背景
      background-color: #f5f5f5;

      .material-item__overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;

        .play-icon {
          width: 60rpx;
          height: 60rpx;
        }
      }
    }

    &--audio {
      // 音频项背景
      background-image: url('/static/音乐背景.png');
      background-size: cover;
      background-repeat: no-repeat;
      position: relative;

      .material-item__audio {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .music-play-icon {
        width: 70rpx;
        height: 70rpx;
      }
    }

    // 选中样式
    &--selected {
      border: 4rpx solid #21BD74;
      box-sizing: border-box;
    }

    // 选择按钮
    &__select {
      position: absolute;
      top: 8rpx;
      right: 8rpx;
      width: 32rpx;
      height: 32rpx;
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2;

      .select-icon {
        width: 32rpx;
        height: 32rpx;
      }

      &--active {
        background-color: #21BD74;
      }
    }

    // 素材名称
    &__name {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      padding: 4rpx 8rpx;
      box-sizing: border-box;

      .name-text {
        color: #fff;
        font-size: 20rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: block;
      }
    }

    // 加载中遮罩层
    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 16rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9;
    }
  }

  // 骨架屏项
  .skeleton-item {
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .skeleton-image {
      width: 100%;
      height: 130rpx;
      background-color: #EEEEEE;
    }

    .skeleton-name {
      height: 30rpx;
      background-color: #EEEEEE;
      margin-top: auto;
    }
  }

  // 骨架屏动画
  .skeleton-animate {
    position: relative;
    overflow: hidden;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      transform: translateX(-100%);
      background: linear-gradient(90deg,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 255, 255, 0.3) 50%,
          rgba(255, 255, 255, 0) 100%);
      animation: shimmer 1.5s infinite;
    }
  }

  @keyframes shimmer {
    100% {
      transform: translateX(100%);
    }
  }

  // 加载更多提示
  .loading-more {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80rpx;

    .loading-text {
      font-size: 24rpx;
      color: #999;
      margin-left: 10rpx;
    }
  }

  // 无更多数据提示
  .no-more {
    text-align: center;
    padding: 20rpx 0;

    .no-more-text {
      font-size: 24rpx;
      color: #999;
    }
  }

  // 底部存储信息
  .storage-info {
    position: fixed;
    bottom: 42rpx;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;

    &__text {
      color: #333333;
      font-size: 28rpx;
      font-weight: bold;
      margin-right: 16rpx;
    }

    &__expand {
      color: #21bd74;
      font-size: 28rpx;
      font-weight: bold;
      text-decoration: underline;
    }
  }

  // 选择模式底部操作栏
  .selection-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 999;
    // height: 100rpx;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 32rpx 20rpx 32rpx;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);

    .selection-info {
      font-size: 28rpx;
      color: #333;
    }

    .selection-actions {
      display: flex;

      .selection-btn {
        width: 160rpx;
        height: 72rpx;
        border-radius: 36rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 20rpx;

        text {
          font-size: 28rpx;
          font-weight: 500;
        }

        &--cancel {
          background-color: #f5f5f5;

          text {
            color: #666;
          }
        }
        &--delete {
          background-color: #f5f5f5;
          text {
            color: #f60000;
          }
        }
        &--confirm {
          background-image: linear-gradient(135deg, #222328 0%, #0f0f0f 100%);

          text {
            color: #fff;
          }
        }
      }
    }
  }
}
</style>