
/**
 * todo 暂时就一个通知，写死在这里。
 * @returns {Promise<unknown>}
 */
export async function requestSubscribeRecord(templateId) {
    console.log("使用模板ID"+templateId)
    const tmplIds = [templateId];
    return new Promise((resolve, reject) => {
        wx.requestSubscribeMessage({
            tmplIds,
            success(res) {
                console.log('订阅消息授权结果：', res);
                const accepted = Object.keys(res).every(id => res[id] === 'accept');
                if (accepted) {
                    // 授权成功，调用后端记录接口（只记录，不发送）
                    resolve('用户已授权');
                } else {
                    reject('用户未授权订阅');
                }
            },
            fail(err) {
                reject('订阅授权失败');
            }
        });
    });
}