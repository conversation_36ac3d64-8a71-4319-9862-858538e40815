import { chooseAndUploadFile, uploadToOss } from "@/utils/ali-oss";
import { saveMaterial } from "@/api/myMaterials";

/**
 * 上传媒体文件并保存到素材库
 * @returns {Promise<{fileUrl: string, materialId: string}>} 返回文件URL和素材ID
 */
export const handleMediaUpload = async () => {
    //上传文件到阿里oss
    const fileUrl = await chooseAndUploadFile()
    console.log("上传完成：" + fileUrl)
    // 显示上传进度
    uni.showLoading({
        title: '上传中...'
    });
    // 保存文件到素材库
    const res = await saveMaterial(fileUrl)
    uni.hideLoading();
    console.log(res.data)

    return {
        fileUrl,
        materialId: res.data
    }
}