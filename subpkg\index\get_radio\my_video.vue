<template>
	<view class="page">
		<view style="height: 200rpx;">
			<view class="header_box">
				<image src="/static/home/<USER>/my_video_user.png"></image>
				<view class="text_box">
					<view class="title">数字人视频生成</view>
					<view class="message">简单快速生成您的数字人口播视频</view>
				</view>
			</view>
			
			<view class="btn_box">
				<view class="btn_1" @click="go_video">
					
					<image :src="page_img_statr + 'project1_messsage_add.png'"></image>
					
					<text >创建视频</text>
				</view>
			
				<view class="btn_2" @click="changeing = true" v-if="!changeing">
					<image src='/static/home/<USER>/my_video_xiazai.png'></image>
					<text>批量下载</text>
				</view>
				<view class="btn_2" @click="close_down" v-else>
					<text style="color: #000000;">取消</text>
				</view>
			</view>
		</view>

		<scroll-view scroll-y="true" class="scroll-y" @scrolltolower="get_again">
			<view class="video_box">
				<view v-for="item in list" class="box_image">
					<image @click="toggleSelect(item)" :lazy-load="true" class="page_video"
						:class="item.status != 'success'?'bg_blur':''"
						:src="item.thumbnailUrl?item.thumbnailUrl:'https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/video_loading_bg.png'">
					</image>

					<!-- 已生成的视频 -->
					<view v-if="item.status == 'success'" @click="toggleSelect(item)">
						<view class="top_text">
							{{item.all_time}}
						</view>

						<!-- 添加选择框 -->
						<view v-if="changeing" class="checkbox-wrapper">
							
							<view class="check_1" v-if="!item.selected">
							</view>
							<image class="check_2" v-else src="/static/index/changed.png">
							</image>
							
						</view>


						<view class="bottom_text">
							<view class="ellipsis2">
								{{item.videoText}}
							</view>
						</view>
					</view>

					<!-- 生成中的视频 -->
					<view v-else-if="item.status == 'generating'">
						<view class="content_center">
							<view>
								<image class="rotate-animation" src="/static/project_loding.png"></image>
							</view>
							<view>生成中</view>
							<view>(正在加速生成中...)</view>
						</view>
					</view>

          <view v-else>
            <view class="content_center">
              <view>
                <image  src="/static/home/<USER>/close_circle.png"></image>
              </view>
              <view>生成失败</view>
              <view>({{item.message}})</view>
            </view>
          </view>

				</view>
			</view>
		</scroll-view>


		<view class="bottom_btn" v-if="changeing">
			<view class="project_btn" @click="Download_video">
				<view>开始批量下载</view>
				<view style="font-size: 24rpx;opacity: 0.7;">(已选择{{page_count}}个视频)</view>
			</view>
		</view>

	</view>
</template>

<script>
	import {
		getCreationList
	} from '../../../api/numberUser/userType.js'
	import {
		formatSecondsToHMS
	} from '../../../utils/file.js'
	
	import {page_img_statr}  from '../../../api/getData.js'
	
	export default {
		data() {
			return {
				url_end: '?ci-process=snapshot&time=0&format=jpg',
				list: [],
				page: 1,
				data_length: '',
				changeing: false,
				page_count:0,
				page_img_statr:''
			}
		},
		onLoad() {
			this.page_img_statr = page_img_statr
			this.get_videolist()
		},
		methods: {
			
			go_video(){
				uni.navigateTo({
					url:'/subpkg/index/get_radio/index'
				})
			},

			close_down() {
				this.changeing = false
				this.list.map(item => {
					item.selected = false
				})
			},
			toggleSelect(item) {
				if (!this.changeing) {
					uni.navigateTo({
						url:`/subpkg/index/get_radio/look_my_video?url=${encodeURIComponent(item.fileUrl)}`
					})
				}
				else{
					this.$set(item, 'selected', !item.selected)
					this.page_count = this.list.filter(r => r.selected).length;
				}
				
			},

			Download_video() {
				const selectedVideos = this.list.filter(item => item.selected && item.status === 'success')
				const videoUrls = selectedVideos.map(item => item.fileUrl) // 假设视频URL存储在videoUrl属性中
				this.batchDownloadVideos(videoUrls).then(results => {
					console.log('下载结果:', results);
				});
			},

			// 批量下载视频到相册
			async batchDownloadVideos(videoUrls) {
				// 检查是否数组
				if (!Array.isArray(videoUrls) || videoUrls.length === 0) {
					uni.showToast({
						title: '视频列表为空',
						icon: 'none'
					});
					return;
				}

				// 检查下载权限
				try {
					const authSetting = await this.checkAuth();
					if (!authSetting['scope.writePhotosAlbum']) {
						await this.requestAuth();
					}
				} catch (err) {
					console.error('权限检查失败:', err);
					return;
				}

				// 开始批量下载
				const results = [];
				for (let i = 0; i < videoUrls.length; i++) {
					try {
						uni.showLoading({
							title: `正在下载第 ${i + 1}/${videoUrls.length} 个视频`,
							mask: true
						});

						const result = await this.downloadAndSaveVideo(videoUrls[i]);
						results.push(result);

						// 短暂延迟，避免频繁请求
						await new Promise(resolve => setTimeout(resolve, 300));
					} catch (err) {
						console.error(`第 ${i + 1} 个视频下载失败:`, err);
						results.push({
							success: false,
							url: videoUrls[i],
							errMsg: err.message || err.errMsg || '下载失败'
						});
					}
				}

				uni.hideLoading();

				// 统计结果
				const successCount = results.filter(r => r.success).length;
				uni.showToast({
					title: `下载完成，成功 ${successCount}/${videoUrls.length}`,
					icon: 'none',
					duration: 3000
				});

				return results;
			},

			// 检查相册权限
			checkAuth() {
				return new Promise((resolve, reject) => {
					uni.getSetting({
						success(res) {
							resolve(res.authSetting);
						},
						fail(err) {
							reject(err);
						}
					});
				});
			},

			// 请求相册权限
			requestAuth() {
				return new Promise((resolve, reject) => {
					uni.authorize({
						scope: 'scope.writePhotosAlbum',
						success() {
							resolve();
						},
						fail(err) {
							// 用户拒绝了授权
							if (err.errMsg.includes('auth deny')) {
								uni.showModal({
									title: '提示',
									content: '需要相册权限才能保存视频，请重新授权',
									confirmText: '去设置',
									success(res) {
										if (res.confirm) {
											uni.openSetting();
										}
									}
								});
							}
							reject(err);
						}
					});
				});
			},

			// 下载并保存单个视频
			downloadAndSaveVideo(url) {
				return new Promise((resolve, reject) => {
					// 1. 下载视频文件
					uni.downloadFile({
						url: url,
						success(downloadRes) {
							if (downloadRes.statusCode !== 200) {
								reject(new Error('下载失败'));
								return;
							}

							// 2. 保存到相册
							uni.saveVideoToPhotosAlbum({
								filePath: downloadRes.tempFilePath,
								success() {
									resolve({
										success: true,
										url: url,
										savedPath: downloadRes.tempFilePath
									});
								},
								fail(saveErr) {
									reject(saveErr);
								}
							});
						},
						fail(downloadErr) {
							reject(downloadErr);
						}
					});
				});
			},



			get_again() {
				if (this.loading || this.noMore) return
				this.page++
				if (this.list.length < this.data_length) this.get_videolist()
			},
			async get_videolist() {
				uni.showLoading()
				try {
					const res = await getCreationList({
						pageNum: this.page,
						pageSize: 10
					})
					res.rows.map(item => {
						item.all_time = formatSecondsToHMS(item.duration)
						item.selected = false // 初始化选择状态
					})

					if (this.page == 1) {
						this.list = res.rows
					} else {
						this.list = [...this.list, ...res.rows]
					}
					this.data_length = res.total
					uni.hideLoading()
				} finally {
					uni.hideLoading()
				}
			},
		}
	}
</script>

<style scoped lang="scss">
	.page {
		padding: 32rpx;
    background: #F3F5F8;

		.bottom_btn {
			width: 100%;
			height: 160rpx;
			position: fixed;
			bottom: 0;
			left: 0;
			background: white;

			.project_btn {
				background: black;
				padding: 10rpx;
				margin-top: 2rpx;
			}
		}

		.header_box {
			display: flex;
			align-items: center;
			image {
				width: 96rpx;
				height: 96rpx;
			}

			.text_box {
				margin-left: 20rpx;

				.title {
					font-weight: 600;
					font-size: 32rpx;
				}

				.message {
					color: #999999;
					font-size: 24rpx;
					margin-top: 10rpx;
				}
			}
		}

		.btn_box {
			display: flex;
			justify-content: space-between;
			margin-top: 20rpx;

			.btn_1 {
				display: flex;
				align-items: center;
				justify-content: space-around;
				padding: 0 120rpx;
				width: 420rpx;
				height: 80rpx;
				line-height: 80rpx;
        background: linear-gradient( 135deg, #07E3D2 0%, #01F770 100%);
        border-radius: 16rpx 16rpx 16rpx 16rpx;
				image {
					width: 32rpx;
					height: 32rpx;
					line-height: 80rpx;
				}
        text{
          font-family: MiSans, MiSans;
          font-weight: 600;
          font-size: 28rpx;
          color: #333333;
          line-height: 40rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
			}

			.btn_2 {
				display: flex;
				align-items: center;
				justify-content: space-around;
				padding: 0 40rpx;
				color: #666666;
				width: 250rpx;
				height: 80rpx;
				background: #EFEFEF;
				border-radius: 16rpx 16rpx 16rpx 16rpx;

				image {
					width: 32rpx;
					height: 32rpx;
				}
        text{
          font-family: MiSans, MiSans;
          font-weight: 600;
          font-size: 28rpx;
          color: #666666;
          line-height: 40rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
			}
		}

		.scroll-y {
			margin-top: 20rpx;
			height: calc(100vh - 250rpx);
		}

		.video_box {
			display: flex;
			justify-content: space-between;
			flex-wrap: wrap;

			.box_image {
				width: 330rpx;
				height: 500rpx;
				margin-top: 20rpx;
				position: relative;

				.bottom_text {
					position: absolute;
					bottom: 0;
					left: 0;
					width: 100%;
					background: rgba(0, 0, 0, 0.2);
					border-radius: 16rpx 16rpx 16rpx 16rpx;
					font-size: 24rpx;
					color: #FFFFFF;
					padding: 20rpx 10rpx;
				}

				.top_text {
					position: absolute;
					top: 16rpx;
					left: 16rpx;
					background: rgba(0, 0, 0, 0.2);
					border-radius: 12rpx 12rpx 12rpx 12rpx;
					padding: 12rpx 26rpx;
					font-size: 24rpx;
					color: #FFFFFF;
					display: flex;
					align-items: center;

				}

				.checkbox-wrapper {
					position: absolute;
					top: 16rpx;
					right: 16rpx;

					.checkbox {
						transform: scale(0.8);
						border-radius: 50%;
					}
				}


				.check_1 {
					width: 40rpx;
					height: 40rpx;
					background: rgba(255, 255, 255, 0.3);
					border: 2rpx solid #FFFFFF;
					border-radius: 50%;
				}


				.check_2 {
					width: 42rpx;
					height: 42rpx;
				}

				.content_center {
					width: 100%;
					position: absolute;
					top: 50%;
					left: 0;
					transform: translate(0, -50%);
					text-align: center;
					font-size: 24rpx;
					color: white;
				}
			}

			.page_video {
				width: 330rpx;
				height: 500rpx;
				border-radius: 20rpx;
			}
		}
	}

	.rotate-animation {
		animation: rotate 1s linear infinite;
	}

	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
		}
	}

	.bg_blur {
		filter: blur(10rpx);
	}
</style>