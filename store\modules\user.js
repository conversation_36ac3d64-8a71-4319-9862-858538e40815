// 用户相关状态管理模块
export default {
  namespaced: true,
  state: {
    // 用户信息
    userInfo: null,
    // 用户token
    token: '',
    // 是否已登录
    isLoggedIn: false,
    // 登录弹窗状态
    loginModal: {
      visible: false,
      successCallback: null,
      cancelCallback: null,
      failCallback: null
    },
    // 频道小红点消息ID数组
    channelBadgeCount: [],
    // WebSocket相关状态
    socketTask: null,
    heartbeatInterval: null,

    // 数字人视频生成成功通知弹窗
    videoGeneratedNotification: {
      show: false,
      message: '作品生成成功，请前往"动态"查看'
    }
  },

  mutations: {
    // 设置用户信息
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo;
    },

    // 设置token
    SET_TOKEN(state, token) {
      state.token = token;
      state.isLoggedIn = !!token;
    },

    // 清除用户信息
    CLEAR_USER(state) {
      // 如果有WebSocket连接，先断开并销毁
      if (state.socketTask) {
        console.log('用户清除时断开WebSocket连接');

        // 关闭WebSocket连接
        state.socketTask.close({
          success: () => {
            console.log('WebSocket连接已关闭');
          }
        });

        // 清除心跳定时器
        if (state.heartbeatInterval) {
          clearInterval(state.heartbeatInterval);
          state.heartbeatInterval = null;
        }

        // 清除WebSocket实例
        state.socketTask = null;
      }

      // 清除用户相关数据
      state.userInfo = null;
      state.token = '';
      state.isLoggedIn = false;

      // 清空频道小红点
      state.channelBadgeCount = [];
    },

    // 设置登录弹窗状态
    SET_LOGIN_MODAL(state, payload) {
      state.loginModal = { ...state.loginModal, ...payload };
    },

    // 添加频道小红点消息ID
    ADD_CHANNEL_BADGE_COUNT(state, messageId) {
      console.log(state.channelBadgeCount, 'state.channelBadgeCount');

      // 检查ID是否已存在，如果不存在则添加
      if (messageId && !state.channelBadgeCount.includes(messageId)) {
        state.channelBadgeCount.push(messageId)
      }
    },

    // 清空频道小红点消息ID数组
    CLEAR_CHANNEL_BADGE_COUNT(state) {
      state.channelBadgeCount = []
    },

    // 设置WebSocket连接
    SET_SOCKET_TASK(state, socketTask) {
      state.socketTask = socketTask
    },



    // 设置心跳定时器
    SET_HEARTBEAT_INTERVAL(state, interval) {
      state.heartbeatInterval = interval
    },

    // 显示数字人视频生成成功通知
    SHOW_VIDEO_GENERATED_NOTIFICATION(state) {
      state.videoGeneratedNotification.show = true
    },

    // 隐藏数字人视频生成成功通知
    HIDE_VIDEO_GENERATED_NOTIFICATION(state) {
      state.videoGeneratedNotification.show = false
    }
  },

  actions: {
    // 登录操作
    login({ commit }, { token, userInfo }) {
      commit('SET_TOKEN', token);
      if (userInfo) {
        commit('SET_USER_INFO', userInfo);
      }
      return Promise.resolve();
    },

    // 退出登录
    logout({ commit }) {
      commit('CLEAR_USER');
      return Promise.resolve();
    },

    // 获取用户信息
    getUserInfo({ commit, state, dispatch }, force = false) {
      console.log('用户信息获取成功，初始化WebSocketsadasd');
      // if (state.userInfo && !force) {
      //   return Promise.resolve(state.userInfo);
      // }

      return this._vm.$api.get_userInfo().then(res => {
        commit('SET_USER_INFO', res.data);


        // 获取用户信息成功后初始化WebSocket
        if (state.isLoggedIn && state.token) {
          console.log('用户信息获取成功，初始化WebSocket');
          dispatch('initWebSocket');
        }

        return res.data;
      });
    },

    // 显示登录弹窗
    showLoginModal({ commit }, callbacks = {}) {
      commit('SET_LOGIN_MODAL', {
        visible: true,
        successCallback: callbacks.success || null,
        cancelCallback: callbacks.cancel || null,
        failCallback: callbacks.fail || null
      });
    },

    // 隐藏登录弹窗
    hideLoginModal({ commit }) {
      commit('SET_LOGIN_MODAL', { visible: false });
    },

    // 初始化WebSocket连接
    initWebSocket({ commit, state, dispatch }) {
      if (!state.isLoggedIn || !state.token) {
        console.log('用户未登录，不创建WebSocket连接');
        return;
      }

      // 创建WebSocket连接
      const socketTask = uni.connectSocket({
        url: `wss://xiaoa.co/ws?token=${state.token}`,
        success: () => {
          console.log('WebSocket连接创建成功');
        },
        fail: (err) => {
          console.error('WebSocket连接创建失败', err);
        }
      });

      commit('SET_SOCKET_TASK', socketTask);

      // WebSocket连接成功
      socketTask.onOpen(() => {
        console.log('WebSocket连接已打开');
      });

      // 接收消息
      socketTask.onMessage((res) => {
        const data = JSON.parse(res.data);
        dispatch('handleMessage', data);
      });

      // 监听WebSocket错误事件
      socketTask.onError((err) => {
        console.error('WebSocket错误：', err);
      });

      // 监听WebSocket关闭事件
      socketTask.onClose((res) => {
        console.log('WebSocket连接已关闭');
      });

      // 设置心跳
      const heartbeatInterval = setInterval(() => {
        if (socketTask && socketTask.readyState === 1) {
          socketTask.send({
            data: JSON.stringify({
              type: 'heartbeat'
            }),
            success: () => {
              console.log('心跳发送成功');
            }
          });
        }
      }, 30000); // 30秒一次

      commit('SET_HEARTBEAT_INTERVAL', heartbeatInterval);
    },

    // 处理消息
    handleMessage({ commit }, data) {
      // 处理数字人视频生成成功通知
      if (data.feedType === 'tool' && JSON.parse(data.content).status === 'success') {
        console.log('数字人视频生成成功，显示通知弹窗');
        commit('SHOW_VIDEO_GENERATED_NOTIFICATION');
      }

      // 只处理特定类型
      if (['group', 'system'].includes(data.feedType) || (data.feedType === 'tool' && JSON.parse(data.content).status === 'success')) {
        // 增加频道小红点消息ID
        commit('ADD_CHANNEL_BADGE_COUNT', data.id);
      }
    },

    // 关闭WebSocket连接
    closeWebSocket({ commit, state }) {
      if (state.socketTask) {
        state.socketTask.close({
          success: () => {
            console.log('WebSocket连接已关闭');
          }
        });

        if (state.heartbeatInterval) {
          clearInterval(state.heartbeatInterval);
          commit('SET_HEARTBEAT_INTERVAL', null);
        }

        commit('SET_SOCKET_TASK', null);
      }
    },


  },

  getters: {
    // 是否已登录
    isLogin: state => state.isLoggedIn,
    // 获取用户信息
    userInfo: state => state.userInfo,
    // 获取token
    token: state => state.token,
    // 获取登录弹窗状态
    loginModal: state => state.loginModal
  }
} 