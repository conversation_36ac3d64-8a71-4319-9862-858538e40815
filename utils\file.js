export const util = {
	tempPathToArrayBuffer(tempFilePath) {
		return new Promise((resolve, reject) => {
			wx.getFileSystemManager().readFile({
				filePath: tempFilePath,
				// encoding: '', // 不指定编码，返回ArrayBuffer
				success(res) {
					resolve(res.data)
				},
				fail(err) {
					reject(err)
				}
			})
		})
	}
}

export const Copywriting = (content) => {
	let title = "";
	let arr = content.includes('#@#')
		? content.split('#@#')[1].split('\n\n')
		: content.split('\n\n');

	let arr1 = [];
	arr.map(item => {
		item.split('\n').map(it => {
			if (it != "  ") {
				arr1.push(it)
			}
		})
	})

	return {
		title: content.includes('#@#') ? content.split('#@#')[0] : "",
		content_list: arr1
	}
}


export const formatRelativeTime = (dateString) => {
	const now = new Date();
	const inputDate = new Date(dateString);

	// 检查是否是有效的日期
	if (isNaN(inputDate.getTime())) {
		return '无效日期';
	}

	const diffInSeconds = Math.floor((now - inputDate) / 1000);
	const diffInMinutes = Math.floor(diffInSeconds / 60);
	const diffInHours = Math.floor(diffInMinutes / 60);
	const diffInDays = Math.floor(diffInHours / 24);

	// 获取时间部分
	const hours = inputDate.getHours();
	let timeOfDay = '';

	if (hours >= 0 && hours < 6) {
		timeOfDay = '凌晨';
	} else if (hours >= 6 && hours < 12) {
		timeOfDay = '上午';
	} else if (hours >= 12 && hours < 14) {
		timeOfDay = '中午';
	} else if (hours >= 14 && hours < 18) {
		timeOfDay = '下午';
	} else {
		timeOfDay = '晚上';
	}

	// 今天
	if (inputDate.toDateString() === now.toDateString()) {
		if (diffInMinutes < 1) {
			return '刚刚';
		} else if (diffInHours < 1) {
			return `${diffInMinutes}分钟前`;
		} else if (diffInHours < 4) {
			return `${diffInHours}小时前`;
		} else {
			return `今天${timeOfDay}`;
		}
	}

	// 昨天
	const yesterday = new Date(now);
	yesterday.setDate(yesterday.getDate() - 1);
	if (inputDate.toDateString() === yesterday.toDateString()) {
		return `昨天${timeOfDay}`;
	}

	// 前天
	const dayBeforeYesterday = new Date(now);
	dayBeforeYesterday.setDate(dayBeforeYesterday.getDate() - 2);
	if (inputDate.toDateString() === dayBeforeYesterday.toDateString()) {
		return `前天${timeOfDay}`;
	}

	// 一周内
	// if (diffInDays <= 7) {
	// 	const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
	// 	return `${days[inputDate.getDay()]}${timeOfDay}`;
	// }

	// 超过一周，显示具体日期
	const month = inputDate.getMonth() + 1;
	const day = inputDate.getDate();
	return `${month}月${day}日${timeOfDay}`;
}




// 传输数字 转成时分秒格式
export const formatSecondsToHMS = (seconds)=> {
  let h = Math.floor(seconds / 3600)
  let m = Math.floor((seconds % 3600) / 60)
  let s = Math.floor(seconds % 60)
  
  h = h < 10 ? '0' + h : h
  m = m < 10 ? '0' + m : m
  s = s < 10 ? '0' + s : s
  
  return `${h}:${m}:${s}`
}
