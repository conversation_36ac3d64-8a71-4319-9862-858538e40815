<template>
	<view class="results-page">
		<!-- 文案内容展示区 -->
		<view class="border-container" :style="{ height: scrollHeight + 'px' }">
			<!-- 内容区域 -->
			<view class="content-card">
				<!-- 有内容时显示 -->
				<view v-if="page_title || content_list" class="content-area">
					<view class="content-title">{{ page_title }}</view>
					<view class="content-body">
						<view v-for="(item, index) in content_list" :key="index" class="content-paragraph">
							{{ item }}
						</view>
					</view>
				</view>
				<!-- 加载状态显示 -->
				<view v-else class="loading-area">
					<view class="loading-text">文案生成中</view>
					<u-skeleton rows="6" loading :animate="true"></u-skeleton>
				</view>
			</view>
			<!-- 复制按钮 -->
			<view class="copy-button" @click="copyContent">
				<u-icon name="file-text" color="#666" size="16"></u-icon>
				<text class="copy-text">复制</text>
			</view>
		</view>

		<!-- 底部按钮 -->
		<view class="action-buttons">
			<view class="action-btn reload-btn" hover-class="btn-hover" @click="regenerateContent">
				<text>重新生成</text>
			</view>
			<view class="action-btn confirm-btn" hover-class="btn-hover" @click="useContent">
				<text>使用文案</text>
			</view>
		</view>
	</view>
</template>

<script>
import { postGenText } from '../../api/numberUser/copy.js'
import { Copywriting } from '../../utils/file.js'

export default {
	data() {
		return {
			source: undefined,
			page_message: '',
			page_title: '',
			content_list: '',
			option: '',
			isLoading: false,
			scrollHeight: 0,
		}
	},

	onLoad(options) {
		const query = uni.createSelectorQuery()
		query.select('.results-page').boundingClientRect()
		query.select('.border-container').boundingClientRect()
		query.select('.action-buttons').boundingClientRect()
		let app = uni.getSystemInfoSync();
		let bottomHight = app.safeAreaInsets.bottom
		query.exec((res) => {
			if (res[0] && res[1]) {
				const pageHeight = res[0].height
				const borderHeight = res[1].height
				const actionButtonsHeight = res[2].height
				this.scrollHeight = pageHeight - borderHeight - bottomHight - actionButtonsHeight - uni.upx2px(26) - uni.upx2px(62)
			}
		})


		this.source = options.source
		this.getOpenerEventChannel().on('get_message', (option) => {
			this.option = option
			this.fetchContent()
		})
	},

	methods: {
		/**
		 * 获取生成内容
		 */
		fetchContent() {
			this.isLoading = true

			postGenText(this.option).then(res => {
				this.page_message = res.data
				const parsedData = Copywriting(res.data)
				this.page_title = parsedData.title
				this.content_list = parsedData.content_list
			}).finally(() => {
				this.isLoading = false
			})
		},

		/**
		 * 复制文案内容（不包含标题）
		 */
		copyContent() {
			if (!this.content_list || this.content_list.length === 0) return

			// 只复制内容，不复制标题
			const contentText = this.content_list.join('\n\n')

			uni.setClipboardData({
				data: contentText,
				success: () => {
					uni.showToast({
						title: '复制成功',
						icon: 'none'
					})
				},
				fail: () => {
					uni.showToast({
						title: '复制失败',
						icon: 'none'
					})
				}
			})
		},

		/**
		 * 重新生成内容
		 */
		regenerateContent() {
			this.page_title = ''
			this.content_list = ''
			this.fetchContent()
		},

		/**
		 * 使用当前内容并导航
		 */
		useContent() {
			// 如果是从批量视频配置的selectWriter页面来的，发送数据回去
			if (this.source === 'selectWriter') {
				// 发送数据给selectWriter组件
				uni.$emit('return-writer-data', {
					title: this.page_title || '生成文案',
					content: this.content_list.join('\n')
				});
			} else {
				// 保存内容到全局状态
				this.$store.commit('setMessage_save', true)
				this.$store.commit('setAiMessage', {
					content: this.content_list,
					title: this.page_title
				})
			}

			// 处理导航逻辑
			this.handleNavigation()
		},

		/**
		 * 处理导航逻辑
		 */
		handleNavigation() {
			// 从批量视频配置的selectWriter页面来
			if (this.source === 'selectWriter') {
				uni.navigateBack({
					delta: 2 // 返回两级，跳过page_copy页面直接回到selectWriter页面
				});
				return;
			}

			// 从频道来源
			if (this.source === 'group') {
				uni.navigateTo({
					url: '/subpkg/index/get_radio/index'
				})
				return
			}

			// 从主页来源
			if (this.source === 'home') {
        uni.navigateTo({
          url: '/subpkg/index/get_radio/index'
        })
				return
			}

			// 其他情况，处理返回逻辑
			const pages = getCurrentPages()
			if (pages.length >= 3) {
				const prevPrevPage = pages[pages.length - 3]

				if (prevPrevPage.route === 'pages/index/index') {
					uni.navigateBack({
						delta: 1,
						success: () => {
							setTimeout(() => {
								uni.navigateTo({
									url: '/subpkg/index/get_radio/index'
								})
							}, 300)
						}
					})
				} else {
					uni.navigateBack({
						delta: 2
					})
				}
			}
		}
	}
}
</script>

<style scoped lang="scss">
.results-page {
	padding: 60rpx 40rpx;
	width: 100%;
	height: 100vh;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	background-color: #f8f9fc;
}

/* 边框容器 */
.border-container {
	flex: 1;
	position: relative;
	border-radius: 24rpx;
	padding: 2rpx;
	background: linear-gradient(45deg,
			#ff0066,
			#ffaa00,
			#00ff66,
			#00aaff,
			#aa00ff);
	background-size: 300% 300%;
	animation: gradientAnimation 8s ease infinite;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.content-card {
	height: 100%;
	width: 100%;
	border-radius: 20rpx;
	background-color: white;
	overflow: hidden;
	padding-bottom: 100rpx;
}

.content-area,
.loading-area {
	height: 100%;
	padding: 30rpx;
	overflow: scroll;
}

.content-title {
	font-size: 34rpx;
	font-weight: 600;
	text-align: center;
	margin-bottom: 30rpx;
	color: #333;
}

.content-body {
	font-size: 30rpx;
	line-height: 1.6;
	color: #444;
	padding-bottom: 50rpx;
	/* 为复制按钮留出空间 */
}

.content-paragraph {
	margin-bottom: 20rpx;
}

/* 复制按钮样式 */
.copy-button {
	position: absolute;
	left: 30rpx;
	bottom: 30rpx;
	display: flex;
	align-items: center;
	background-color: rgba(245, 245, 245, 0.9);
	padding: 5rpx 5rpx;
	border-radius: 10rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	z-index: 9;
}

.copy-text {
	font-size: 24rpx;
	color: #666;
	margin-left: 8rpx;
}

.loading-text {
	font-size: 30rpx;
	font-weight: 500;
	margin-bottom: 20rpx;
	color: #666;
}

.action-buttons {
	display: flex;
	justify-content: space-between;
	width: 100%;
	margin-top: 50rpx;
	padding: 0 40rpx;
}

.action-btn {
	padding: 20rpx 40rpx;
	border-radius: 20rpx;
	text-align: center;
	width: 240rpx;
	transition: all 0.2s;

	&.reload-btn {
		background-color: #ffffff;
		color: #333;
		border: 2rpx solid #eee;
	}

	&.confirm-btn {
		background-color: #000000;
		color: white;
	}
}

.btn-hover {
	transform: scale(0.98);
	opacity: 0.9;
}

@keyframes gradientAnimation {
	0% {
		background-position: 0% 50%;
	}

	50% {
		background-position: 100% 50%;
	}

	100% {
		background-position: 0% 50%;
	}
}
</style>
