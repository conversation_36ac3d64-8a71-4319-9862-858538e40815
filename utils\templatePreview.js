/**
 * 模板预览图生成工具
 * 用于将模板画布转换为预览图片
 */

/**
 * 绘制Canvas生成预览图
 * @param {Object} canvasData - 画布数据
 * @param {Function} uploadCallback - 上传成功回调函数
 * @param {Number} canvasScale - 画布缩放比例
 * @param {Object} elements - 各类元素数据
 * @param {Object} textData - 身份栏文本数据
 * @param {Function} formatTextFn - 格式化文本的函数
 */
export function drawTemplatePreview(options) {
  const {
    canvasData,
    uploadCallback,
    canvasScale = 0.4,
    backgroundElements = [],
    identityName = '',
    identityDesc = '',
    formatTextFn = (text) => [text]
  } = options;

  uni.showLoading({
    title: '模版保存中' // 显示加载提示
  });

  // 创建画布上下文
  const ctx = uni.createCanvasContext('myCanvasBlack');

  // 设置背景色为黑色
  ctx.setFillStyle('#000000');
  ctx.fillRect(0, 0, 1080 * canvasScale, 1920 * canvasScale); // 填充整个画布

  // 提取图片数据
  const {
    url1, url2, url3, url4, bgUrl, // 所有元素的URL
    anchor1, anchor2, anchor3, anchor4, // 元素锚点
    size1, size2, size3, size4, // 元素尺寸
    position1, position2, position3, position4, // 元素位置
    scale1, scale2, scale3, scale4 // 元素缩放比例
  } = extractImageData(canvasData, canvasScale, backgroundElements);

  console.log('预览图数据已提取完成，开始绘制'); // 输出调试信息

  // 下载并绘制图片
  drawAllElements(
    ctx,
    url1, url2, url3, url4, bgUrl, // 所有元素的URL
    size1, size2, size3, size4, // 元素尺寸
    anchor1, anchor2, anchor3, anchor4, // 元素锚点
    position1, position2, position3, position4, // 元素位置
    scale1, scale2, scale3, scale4, // 元素缩放比例
    canvasScale,
    identityName,
    identityDesc,
    formatTextFn,
    canvasData // 传递原始canvasData
  )
    .then(() => {
      console.log('图片绘制完成，准备导出画布'); // 输出调试信息
      // 绘制完成后转换为图片
      setTimeout(() => {
        canvasToTempFilePath(uploadCallback, canvasScale); // 将Canvas转为临时图片文件
      }, 500); // 延迟500ms确保绘制完成
    })
    .catch(err => {
      console.error('图片绘制失败:', err); // 输出错误信息
      uni.hideLoading(); // 隐藏加载提示
      uni.showToast({
        title: '图片生成失败', // 显示错误提示
        icon: 'none'
      });
    });
}

/**
 * 提取图片数据的方法
 * @param {Array} canvasData - 画布数据
 * @param {Number} canvasScale - 画布缩放比例
 * @param {Array} backgroundElements - 背景元素数据
 * @returns {Object} 提取的所有元素数据
 */
function extractImageData(canvasData, canvasScale, backgroundElements) {
  // 各元素的URL
  let url1 = ''; // 数字人图片
  let url2 = ''; // 标题图片
  let url3 = ''; // 字幕图片
  let url4 = ''; // 身份栏标识
  let bgUrl = ''; // 背景图

  // 添加anchor1变量初始化
  let anchor1 = [0, 0, 0]; // 数字人默认anchor
  let anchor2 = [0, 0]; // 标题锚点 [x, y]
  let anchor3 = [0, 0]; // 字幕锚点
  let anchor4 = [0, 0]; // 身份栏锚点

  // 各元素的位置和尺寸
  let size1 = [0, 0]; // 数字人尺寸 [宽, 高]
  let size2 = [0, 0]; // 标题尺寸 [宽, 高]
  let size3 = [0, 0]; // 字幕尺寸
  let size4 = [0, 0]; // 身份栏尺寸

  // 位置 - 与视图计算方式保持一致
  let position1 = [0, 0]
  let position2 = [0, 0]; // 标题位置 [x, y]
  let position3 = [0, 0]; // 字幕位置
  let position4 = [0, 0]; // 身份栏位置

  // 各元素的缩放比例
  let scale1 = [1, 1]; // 数字人缩放比例
  let scale2 = [1, 1]; // 标题缩放比例
  let scale3 = [1, 1]; // 字幕缩放比例
  let scale4 = [1, 1]; // 身份栏缩放比例

  console.log('正在提取图片数据，元素总数:', canvasData.length); // 输出调试信息

  // 遍历当前数据提取各元素信息
  canvasData.forEach((item, index) => {
    // 处理数字人元素
    if (item.type === 'virtualman') {
      url1 = item.cover; // 获取数字人封面图URL
      // 提取数字人anchor信息
      if (item.define && item.define.transform && item.define.transform.anchor) {
        anchor1 = [item.define.transform.anchor[0], item.define.transform.anchor[1], 0];
        
        // 获取数字人缩放比例
        if (item.define.transform && item.define.transform.scalar) {
          // 将百分比缩放值转换为实际比例（100=>1.0）
          scale1 = [
            item.define.transform.scalar[0] / 100,
            item.define.transform.scalar[1] / 100
          ];
        }
        
        // 修改：将缩放比例应用到尺寸计算中
        const scaleRatioX = scale1[0];
        const scaleRatioY = scale1[1];
        
        // 计算考虑了缩放比例后的尺寸
        size1 = [
          item.extra.width * item.scale * canvasScale * scaleRatioX, 
          item.extra.height * item.scale * canvasScale * scaleRatioY
        ]
        
        // 使用原始position值，不需要在这里调整
        position1 = [
          item.define.transform.position[0],
          item.define.transform.position[1]
        ];
        
        console.log(`数字人位置数据: position=(${position1[0]},${position1[1]}), anchor=(${anchor1[0]},${anchor1[1]}), scale=(${scale1[0]},${scale1[1]})`);
      } else {
        // 使用默认anchor值
        anchor1 = [112.5, 200, 0];
      }
      console.log(`[${index}] 找到数字人元素:`, item.cover ? '有封面' : '无封面', `anchor: ${JSON.stringify(anchor1)}`); // 输出调试信息
      console.log(`数字人元素缩放: ${scale1[0]}x${scale1[1]}, 尺寸: ${size1[0]}x${size1[1]}`);
    }

    // 处理背景元素
    if (item.type === 'mask' && item.id) {
      bgUrl = getBackgroundUrl(item.id, backgroundElements); // 获取背景图URL
      console.log(`[${index}] 找到背景元素:`, item.url); // 输出调试信息
    }

    // 处理标题元素
    if (item.type === 'title' && item.url) {
      url2 = item.url; // 获取标题图URL

      // 保存元素的尺寸
      size2 = [item.define.width, item.define.height]; // 获取标题宽高

      // 保存锚点和位置 - 这些将用于计算Canvas中的位置
      if (item.define.transform) {
        // 获取标题锚点，如果不存在则默认为[0,0]
        anchor2 = item.define.transform.anchor ?
          [item.define.transform.anchor[0], item.define.transform.anchor[1]] : [0, 0];
        // 获取标题位置，如果不存在则默认为[0,0]
        position2 = item.define.transform.position ?
          [item.define.transform.position[0], item.define.transform.position[1]] : [0, 0];
      }

      // 获取缩放比例
      if (item.define.transform && item.define.transform.scalar) {
        // 将百分比缩放值转换为实际比例（100=>1.0）
        scale2 = [
          item.define.transform.scalar[0] / 100,
          item.define.transform.scalar[1] / 100
        ];
      }

      // 输出调试信息
      console.log(`[${index}] 找到标题元素:`, url2);
      console.log(`标题元素尺寸: ${size2[0]}x${size2[1]}`);
      console.log(`标题元素锚点: ${anchor2[0]}, ${anchor2[1]}`);
      console.log(`标题元素位置: ${position2[0]}, ${position2[1]}`);
      console.log(`标题元素缩放: ${scale2[0]}x${scale2[1]}`);
    }

    // 处理字幕元素
    if (item.type === 'caption' && item.url) {
      url3 = item.url; // 获取字幕图URL

      // 保存元素的尺寸
      size3 = [item.define.width, item.define.height]; // 获取字幕宽高

      // 保存锚点和位置
      if (item.define.transform) {
        // 获取字幕锚点，如果不存在则默认为[0,0]
        anchor3 = item.define.transform.anchor ?
          [item.define.transform.anchor[0], item.define.transform.anchor[1]] : [0, 0];
        // 获取字幕位置，如果不存在则默认为[0,0]
        position3 = item.define.transform.position ?
          [item.define.transform.position[0], item.define.transform.position[1]] : [0, 0];
      }

      // 字幕不应用缩放，固定为1:1
      scale3 = [1, 1];

      // 输出调试信息
      console.log(`[${index}] 找到字幕元素:`, url3);
      console.log(`字幕元素尺寸: ${size3[0]}x${size3[1]}`);
      console.log(`字幕元素锚点: ${anchor3[0]}, ${anchor3[1]}`);
      console.log(`字幕元素位置: ${position3[0]}, ${position3[1]}`);
    }

    // 处理身份栏元素
    if (item.type === 'introduceCard') {
      console.log(`[${index}] 找到身份栏元素:`, JSON.stringify(item.define)); // 输出调试信息
      url4 = 'introduceCard'; // 使用特殊标识表示身份栏

      // 计算实际的身份栏宽高
      if (item.define && item.define.width && item.define.height) {
        // 如果元素定义了宽高，使用定义的值
        size4 = [item.define.width, item.define.height];
      }

      // 保存锚点和位置
      if (item.define.transform) {
        // 获取身份栏锚点，如果不存在则默认为[0,0]
        anchor4 = item.define.transform.anchor ?
          [item.define.transform.anchor[0], item.define.transform.anchor[1]] : [0, 0];
        // 获取身份栏位置，如果不存在则默认为[0,0]
        position4 = item.define.transform.position ?
          [item.define.transform.position[0], item.define.transform.position[1]] : [0, 0];
      }

      // 身份栏不应用缩放，固定为1:1
      scale4 = [1, 1];

      // 输出调试信息
      console.log(`身份栏尺寸: ${size4[0]}x${size4[1]}`);
      console.log(`身份栏锚点: ${anchor4[0]}, ${anchor4[1]}`);
      console.log(`身份栏位置: ${position4[0]}, ${position4[1]}`);
    }
  });

  // 返回提取的所有元素数据
  return {
    url1, url2, url3, url4, bgUrl, // 所有元素的URL
    anchor1, anchor2, anchor3, anchor4, // 添加anchor1到返回值
    size1, size2, size3, size4, // 元素尺寸
    position1, position2, position3, position4, // 元素位置
    scale1, scale2, scale3, scale4 // 元素缩放比例
  };
}

/**
 * 获取背景URL
 * @param {String} id - 背景ID
 * @param {Array} backgroundElements - 背景元素数据
 * @returns {String} 背景图URL
 */
function getBackgroundUrl(id, backgroundElements) {
  return backgroundElements.find((item) => item.id == id)?.coverUrl;
}

/**
 * 绘制所有元素
 * @param {Object} ctx - Canvas上下文
 * @param {String} url1 - 数字人图片URL
 * @param {String} url2 - 标题图片URL
 * @param {String} url3 - 字幕图片URL
 * @param {String} url4 - 身份栏标识
 * @param {String} bgUrl - 背景图URL
 * @param {Array} size1 - 数字人尺寸
 * @param {Array} size2 - 标题尺寸
 * @param {Array} size3 - 字幕尺寸
 * @param {Array} size4 - 身份栏尺寸
 * @param {Array} anchor1 - 数字人锚点
 * @param {Array} anchor2 - 标题锚点
 * @param {Array} anchor3 - 字幕锚点
 * @param {Array} anchor4 - 身份栏锚点
 * @param {Array} position1 - 数字人位置
 * @param {Array} position2 - 标题位置
 * @param {Array} position3 - 字幕位置
 * @param {Array} position4 - 身份栏位置
 * @param {Array} scale1 - 数字人缩放比例
 * @param {Array} scale2 - 标题缩放比例
 * @param {Array} scale3 - 字幕缩放比例
 * @param {Array} scale4 - 身份栏缩放比例
 * @param {Number} canvasScale - 画布缩放比例
 * @param {String} identityName - 人物名称
 * @param {String} identityDesc - 人物介绍
 * @param {Function} formatTextFn - 格式化文本的函数
 * @param {Array} canvasData - 原始画布数据
 * @returns {Promise} Promise对象
 */
function drawAllElements(
  ctx,
  url1, url2, url3, url4, bgUrl,
  size1, size2, size3, size4,
  anchor1, anchor2, anchor3, anchor4,
  position1, position2, position3, position4,
  scale1, scale2, scale3, scale4,
  canvasScale,
  identityName,
  identityDesc,
  formatTextFn,
  canvasData
) {
  return new Promise(async (resolve, reject) => {
    try {
      console.log('开始按顺序绘制所有元素');

      // 1. 先绘制背景颜色 (黑色背景)
      ctx.setFillStyle('#000000');
      ctx.fillRect(0, 0, 1080, 1920);
      console.log('背景颜色绘制完成');

      // 2. 绘制背景图片（如果有）
      if (bgUrl) {
        await drawImage(ctx, bgUrl, 0, 0, 1080 * canvasScale, 1920 * canvasScale, [1, 1]);
        console.log('背景图绘制完成');
      }

      // 3. 绘制数字人图片（如果有）
      if (url1) {
        // 获取数字人图片尺寸信息
        const figureWidth = size1[0];
        const figureHeight = size1[1];
        
        // 修改：计算绘制位置时考虑缩放比例对锚点的影响
        // 与编辑器中计算方式保持一致
        const scaleRatioX = scale1[0];
        const scaleRatioY = scale1[1];
        const x = (position1[0] - anchor1[0] * scaleRatioX);
        const y = (position1[1] - anchor1[1] * scaleRatioY);

        // 由于在尺寸计算中已经应用了缩放，这里传入[1,1]避免重复缩放
        await drawImage(ctx, url1, x, y, figureWidth, figureHeight, [1, 1]);
        console.log(`数字人图片绘制完成，位置:(${x},${y}),尺寸:(${figureWidth},${figureHeight})`);
      }

      // 4. 绘制标题元素（如果有）
      if (url2) {
        const x = (position2[0] - anchor2[0]);
        const y = (position2[1] - anchor2[1]);
        await drawImage(ctx, url2, x, y, size2[0], size2[1], scale2);
        console.log(`标题元素绘制完成，位置:(${x},${y}),尺寸:(${size2[0]},${size2[1]})`);
      }

      // 5. 绘制字幕元素（如果有）
      if (url3) {
        const x = (position3[0] - anchor3[0]);
        const y = (position3[1] - anchor3[1]);
        await drawImage(ctx, url3, x, y, size3[0], size3[1], scale3);
        console.log(`字幕元素绘制完成，位置:(${x},${y}),尺寸:(${size3[0]},${size3[1]})`);
      }

      // 6. 绘制身份栏元素（如果有）
      if (url4 === 'introduceCard') {
        const x = (position4[0] - anchor4[0]);
        const y = (position4[1] - anchor4[1]);

        // 从传入的canvasData中找到身份栏元素
        const introduceCard = canvasData.find(item => item.type === 'introduceCard');

        if (introduceCard) {
          await drawIntroduceCard(ctx, x, y, size4[0], size4[1], introduceCard, identityName, identityDesc, formatTextFn);
          console.log(`身份栏元素绘制完成，位置:(${x},${y}),尺寸:(${size4[0]},${size4[1]})`);
        } else {
          console.error('未找到身份栏元素');
        }
      }

      // 执行绘制，false表示先清空画布再绘制
      ctx.draw(false);
      console.log('所有元素按顺序绘制完成');

      // 延迟300ms确保绘制完成
      setTimeout(() => resolve(), 300);
    } catch (error) {
      console.error('绘制过程中发生错误:', error);
      reject(error);
    }
  });
}

/**
 * 绘制图片（通用方法）
 * @param {Object} ctx - Canvas上下文
 * @param {String} url - 图片URL
 * @param {Number} x - X坐标
 * @param {Number} y - Y坐标
 * @param {Number} width - 宽度
 * @param {Number} height - 高度
 * @param {Array} scale - 缩放比例
 * @returns {Promise} Promise对象
 */
function drawImage(ctx, url, x, y, width, height, scale) {
  return new Promise((resolve, reject) => {
    if (!url) {
      console.warn('URL为空，跳过绘制'); // 输出警告信息
      resolve(); // 解析Promise
      return;
    }

    console.log(`开始下载图片: ${url.substring(0, 30)}...`); // 输出调试信息
    console.log(`绘制参数: x=${x}, y=${y}, width=${width}, height=${height}, scale=${JSON.stringify(scale)}`);

    uni.downloadFile({
      url: url, // 下载图片的URL
      success: res => {
        console.log(`图片下载成功: ${url.substring(0, 30)}...`); // 输出调试信息

        try {
          // 保存当前上下文状态
          ctx.save();

          // 应用缩放变换（如果需要）
          if (scale && (scale[0] !== 1 || scale[1] !== 1)) {
            // 计算中心点
            const centerX = x + width / 2; // 计算X中心点
            const centerY = y + height / 2; // 计算Y中心点

            // 移动到中心点，应用缩放，再移回原位置
            ctx.translate(centerX, centerY); // 移动坐标系原点到图片中心
            ctx.scale(scale[0], scale[1]); // 应用缩放变换
            ctx.translate(-centerX, -centerY); // 恢复坐标系原点
          }

          // 绘制图片
          console.log(`绘制图片到: x=${x}, y=${y}, width=${width}, height=${height}`); // 输出调试信息
          ctx.drawImage(res.tempFilePath, x, y, width, height); // 在指定位置绘制图片

          // 恢复上下文状态
          ctx.restore();
          resolve(); // 解析Promise
        } catch (drawErr) {
          console.error(`绘制图片时发生错误:`, drawErr);
          console.error(`错误详情: 图片源=${url.substring(0, 30)}..., 位置=(${x},${y}), 尺寸=(${width},${height})`);
          ctx.restore(); // 确保上下文被恢复
          resolve(); // 继续流程而不是中断
        }
      },
      fail: err => {
        console.error(`图片下载失败(${url.substring(0, 30)}...):`); // 输出错误信息
        console.error(`下载错误详情:`, err);
        console.error(`图片参数: x=${x}, y=${y}, width=${width}, height=${height}`);
        resolve(); // 继续流程而不是中断，不要reject
      }
    });
  });
}

/**
 * 绘制身份栏
 * @param {Object} ctx - Canvas上下文
 * @param {Number} x - X坐标
 * @param {Number} y - Y坐标
 * @param {Number} width - 宽度
 * @param {Number} height - 高度
 * @param {Object} introduceCard - 身份栏元素数据
 * @param {String} identityName - 人物名称
 * @param {String} identityDesc - 人物介绍
 * @param {Function} formatTextFn - 格式化文本的函数
 * @returns {Promise} Promise对象
 */
function drawIntroduceCard(ctx, x, y, width, height, introduceCard, identityName, identityDesc, formatTextFn) {
  return new Promise((resolve) => {
    if (!introduceCard) {
      console.error('未找到身份栏元素'); // 输出错误信息
      resolve(); // 解析Promise
      return;
    }

    // 保存上下文状态
    ctx.save();

    try {
      // 检查是否有背景图
      const hasBgImage = introduceCard.define &&
        introduceCard.define.background &&
        introduceCard.define.background.url;

      // 如果有背景图，优先使用背景图
      if (hasBgImage) {
        const bgUrl = introduceCard.define.background.url; // 获取背景图URL
        console.log('身份栏优先使用背景图:', bgUrl); // 输出调试信息

        // 下载并绘制背景图
        uni.downloadFile({
          url: bgUrl, // 下载背景图的URL
          success: res => {
            console.log('身份栏背景图下载成功:', res.tempFilePath); // 输出调试信息
            // 绘制背景图
            ctx.drawImage(res.tempFilePath, x, y, width, height); // 在指定位置绘制背景图

            // 绘制文字
            drawIntroduceCardText(ctx, x, y, width, height, introduceCard, identityName, identityDesc, formatTextFn); // 绘制身份栏文字

            // 恢复上下文
            ctx.restore();
            resolve(); // 解析Promise
          },
          fail: err => {
            console.error('身份栏背景图片下载失败:', err); // 输出错误信息
            console.log('背景图下载失败，使用背景颜色或透明背景作为备选');
            // 背景下载失败，使用纯色背景或透明背景作为备选
            drawIntroduceCardWithColor(ctx, x, y, width, height, introduceCard, identityName, identityDesc, formatTextFn);
            ctx.restore();
            resolve();
          }
        });
      } else {
        // 没有背景图，使用纯色背景或透明背景
        console.log('身份栏没有背景图，使用纯色背景或透明背景');
        drawIntroduceCardWithColor(ctx, x, y, width, height, introduceCard, identityName, identityDesc, formatTextFn);
        ctx.restore();
        resolve();
      }
    } catch (err) {
      console.error('绘制身份栏时发生错误:', err);
      ctx.restore();
      resolve(); // 继续流程而不中断
    }
  });
}

/**
 * 使用纯色背景绘制身份栏
 * @param {Object} ctx - Canvas上下文
 * @param {Number} x - X坐标
 * @param {Number} y - Y坐标
 * @param {Number} width - 宽度
 * @param {Number} height - 高度
 * @param {Object} introduceCard - 身份栏元素
 * @param {String} identityName - 人物名称
 * @param {String} identityDesc - 人物介绍
 * @param {Function} formatTextFn - 格式化文本的函数
 */
function drawIntroduceCardWithColor(ctx, x, y, width, height, introduceCard, identityName, identityDesc, formatTextFn) {
  // 获取背景色，如果没有则使用透明背景
  let bgColor = 'rgba(0, 0, 0, 0)'; // 默认透明背景
  if (introduceCard.define &&
    introduceCard.define.background &&
    introduceCard.define.background.color) {
    bgColor = introduceCard.define.background.color; // 使用定义的背景色
  }

  // 绘制背景
  console.log(`绘制身份栏背景: x=${x}, y=${y}, width=${width}, height=${height}, 背景色=${bgColor}`);

  // 保存当前上下文状态
  ctx.save();

  // 如果是透明背景，设置完全透明
  if (bgColor === 'rgba(0, 0, 0, 0)') {
    ctx.globalAlpha = 0;
  }

  ctx.setFillStyle(bgColor); // 设置填充颜色
  ctx.fillRect(x, y, width, height); // 填充矩形区域

  // 恢复上下文状态（包括不透明度）
  ctx.restore();

  // 绘制文字
  drawIntroduceCardText(ctx, x, y, width, height, introduceCard, identityName, identityDesc, formatTextFn);
}

/**
 * 绘制身份栏文字
 * @param {Object} ctx - Canvas上下文
 * @param {Number} x - X坐标
 * @param {Number} y - Y坐标
 * @param {Number} width - 宽度
 * @param {Number} height - 高度
 * @param {Object} introduceCard - 身份栏元素
 * @param {String} identityName - 人物名称
 * @param {String} identityDesc - 人物介绍
 * @param {Function} formatTextFn - 格式化文本的函数
 */
function drawIntroduceCardText(ctx, x, y, width, height, introduceCard, identityName, identityDesc, formatTextFn) {
  // 设置文字样式
  let textColor = '#000000'; // 默认文字颜色为黑色
  let fontSize = introduceCard.define.fontSize;  // 预览图上的字体大小

  // 使用定义的文字样式(如果有)
  if (introduceCard.define.name && introduceCard.define.name.color) {
    textColor = introduceCard.define.name.color; // 使用定义的文字颜色
  }

  // 计算文字位置 - 添加内边距
  const padding = 5; // 文字与边缘的距离
  const textX = x + padding; // 计算文字X坐标
  const nameY = y + padding + fontSize; // 第一行文字Y位置

  // 获取要绘制的文本内容
  const nameText = introduceCard.define.name && introduceCard.define.name.content
    ? introduceCard.define.name.content
    : identityName;

  // 绘制姓名文字
  ctx.setFillStyle(textColor); // 设置文字颜色
  ctx.setFontSize(fontSize); // 设置文字大小
  ctx.fillText(nameText, textX, nameY); // 绘制第一行文字（姓名）

  // 绘制描述文字(可能使用不同样式)
  let descColor = textColor; // 默认使用相同颜色
  if (introduceCard.define.description && introduceCard.define.description.color) {
    descColor = introduceCard.define.description.color; // 使用定义的描述文字颜色
  }

  // 获取要绘制的描述文本
  const descText = introduceCard.define.description && introduceCard.define.description.content
    ? introduceCard.define.description.content
    : identityDesc;

  // 分段处理描述文本，每15个字符一行
  const formattedDescLines = formatTextFn(descText);

  // 第二行文字Y位置 = 第一行Y位置 + 行高
  const lineHeight = fontSize * 1.5; // 行高为字体大小的1.5倍
  let currentY = nameY + lineHeight; // 计算第二行文字Y坐标起点

  // 逐行绘制描述文本
  ctx.setFillStyle(descColor); // 设置描述文字颜色
  formattedDescLines.forEach((line, index) => {
    ctx.fillText(line, textX, currentY); // 绘制当前行文字
    currentY += lineHeight; // Y坐标递增，准备绘制下一行
  });

  console.log(`绘制身份栏文字: 姓名="${nameText}", 描述="${descText}"`);
}

/**
 * 将Canvas转为图片
 * @param {Function} uploadCallback - 上传成功回调函数
 * @param {Number} canvasScale - 画布缩放比例
 */
function canvasToTempFilePath(uploadCallback, canvasScale) {
  uni.canvasToTempFilePath({
    x: 0,
    y: 0,
    width: 1080 * canvasScale,
    height: 1920 * canvasScale,
    destWidth: 1080 * canvasScale,
    destHeight: 1920 * canvasScale,
    canvasId: 'myCanvasBlack',
    success: res => {
      console.log(res.tempFilePath, '预览图生成成功');

      // 调用回调函数处理后续操作
      if (typeof uploadCallback === 'function') {
        uploadCallback(res.tempFilePath);
      } else {
        uni.hideLoading();
      }
    },
    fail: err => {
      console.error('生成图片失败:', err);
      uni.hideLoading();
      uni.showToast({
        title: '生成图片失败',
        icon: 'none'
      });
    }
  });
} 