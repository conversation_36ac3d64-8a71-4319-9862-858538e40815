<template>
	<view>
		<u-popup :show="pageShow" :round="20" @close="down_close">
			<view class="botton_card">
				<view style="display: flex;justify-content: space-between">
					<view style="width: 18px;"></view>
					<view style="font-weight: 600;font-size: 30rpx;color: black;">请选择版本</view>
					<u-icon name="close" size="24" @click="down_close"></u-icon>
				</view>

				<view class="box_vip" v-for="item in card_data" @click="go_numbeUser(item)">
					<image :src="item.src"></image>
					<view class="text_1">
						<view class="text_1_1">
							<view>{{ item.name }}</view>
							<image :src="item.icon"></image>
						</view>
						<view>{{ item.content }}</view>
					</view>
				</view>

			</view>
		</u-popup>
	</view>
</template>

<script>
import {
	getUserType
} from '../../api/numberUser/userType.js'
import {
	getcanFastClone
} from '../../api/numberUser/canFastClone.js'
export default {
	data() {
		return {
			card_data: [{
				src: '/static/index/pro.png',
				name: '专业版',
				type: 'pro',
				icon: '/static/index/pro_tab.png',
				content: '高度还原，效果更好'
			},
			{
				src: '/static/index/lite.png',
				name: '极速版',
				type: 'lite',
				icon: '',
				content: '免费克隆，极速定制'
			}
			]
		}
	},
	props: ['fastPowerRange', 'numberType', 'source', 'pageShow'],
	mounted() {
		getUserType().then(res => {
			console.log(res, 's1')
		})
	},
	methods: {
		down_close() {
			this.pageShow = false
			this.$emit('down_close')
		},
		go_numbeUser(item) {
			this.$emit('down_close')
			let source = this.source
			if (item.type == 'pro') {
				if (this.numberType == 'sound') {
					uni.navigateTo({
						url: `/subpkg/index/get_sound/index?type=pro&source=${source}`
					})
				}
				else {
					uni.navigateTo({
						url: '/subpkg/index/numbe_user/index?type=pro'
					})
				}

			} else {
				let source = this.source
				if (this.fastPowerRange == 1) {
					if (this.numberType == 'sound') {
						uni.navigateTo({
							url: `/subpkg/index/get_sound/index?type=lite&source=${source}`
						})
					}
					else {
						uni.navigateTo({
							url: '/subpkg/index/numbe_user/index?type=lite'
						})
					}

				} else {
					getcanFastClone().then(res => {
						let source = this.source
						if (res.data) {
							if (this.numberType == 'sound') {
								uni.navigateTo({
									url: `/subpkg/index/get_sound/index?type=lite&source=${source}`
								})
							}
							else {
								uni.navigateTo({
									url: '/subpkg/index/numbe_user/index?type=lite'
								})
							}
						} else {
							this.$emit('show_model', true)
						}
					})
				}
			}
		}
	}
}
</script>

<style scoped lang="scss">
.botton_card {
	padding: 30rpx 40rpx;
}

.box_vip {
	display: flex;
	align-items: center;
	margin: 40rpx 0;

	image {
		width: 80rpx;
		height: 80rpx;
	}

	.text_1 {
		font-size: 28rpx;
		margin-left: 20rpx;
		color: #919191;

		.text_1_1 {
			color: black;
			margin-bottom: 10rpx;
			font-size: 32rpx;
			display: flex;
			align-items: center;

			image {
				margin-left: 10rpx;
				width: 20rpx;
				height: 20rpx;
			}
		}
	}
}
</style>
