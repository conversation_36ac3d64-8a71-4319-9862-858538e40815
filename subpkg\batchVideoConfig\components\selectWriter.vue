<template>
  <view class="writer-container">
    <!-- 文案输入区域 -->
    <view class="input-area">
      <view class="input-box" @click="openTextEditor">
        <view class="placeholder" v-if="!currentText">粘贴你的文案或者输入内容</view>
        <view class="text-content" v-else>{{ currentText }}</view>
      </view>
      <view class="tools-row">
        <view class="tool-item">
          <view class="tool-button" @click="extractVideoText">
            <image class="tool-icon" src="/static/批量生产视频首页.png"></image>
            <view class="tool-text">提取视频链接文案</view>
          </view>
        </view>
        <view class="tool-item">
          <view class="tool-button" @click="openAiModelPopup">
            <image class="tool-icon" src="/static/aitubiao.png"></image>
            <view class="tool-text">AI生成文案</view>
          </view>
        </view>
        <view class="tool-item">
          <view class="tool-button" @click="openChannelSelector">
            <image class="tool-icon" src="/static/复制文案.png"></image>
            <view class="tool-text">频道文案</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 文案列表 -->
    <view class="writer-list" :style="{ paddingBottom: writerListBottom + 'px' }">
      <view class="writer-item" v-for="(item, index) in topicList" :key="index">
        <view class="writer-header">
          <view class="writer-title">{{ item.title }}</view>
          <image class="delete-icon" src="/static/删除.png" @click="deleteWriter(index)"></image>
        </view>
        <view class="writer-content">
          <view class="content-wrapper">
            <view class="content-text" :class="{ 'expanded': item.expanded }">{{ item.content }}</view>
            <view class="expand-text" @click="toggleExpand(index)">{{ item.expanded ? '收起' : '展开全文' }}</view>
          </view>
          <image class="expand-icon" :class="{ 'rotate': item.expanded }" @click="toggleExpand(index)"
            src="/static/下拉.png"></image>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar" :style="{ paddingBottom: safeAreaBottom + 'px' }">
      <view class="selected-count">
        <view class="count-text">共<text class="count-number">{{ writerCount }}</text>个</view>
      </view>
      <view class="bottom-bar-btn">
        <view class="prev-btn" @click="prevStep">
          <view class="btn-text">上一步</view>
        </view>
        <view class="next-btn" @click="nextStep">
          <view class="btn-text">下一步</view>
        </view>
      </view>
    </view>

    <!-- AI工具弹窗 -->
    <u-popup :show="aiToolPopupVisible" @close="aiToolPopupVisible = false" :round="20">
      <view class="popup-container" style="height: 60vh;overflow-y: scroll;">
        <view class="popup-title">选择工具</view>
        <view class="tool-item" v-for="item in aiListData" @click="handleListAiMessage(item)">
          <image :src="item.icon"></image>
          <view class="tool-info ellipsis">
            <view>{{ item.name }}</view>
            <view class="ellipsis">{{ item.description }}</view>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 频道选择弹窗 -->
    <channel-selector :show="channelSelectorVisible" :channelList="listData" @close="channelSelectorVisible = false"
      @select="handleListMessage">
    </channel-selector>
  </view>
</template>

<script>
import { getRandomText, getGroup } from '@/api/numberUser/copy.js'
import ChannelSelector from '@/subpkg/index/components/channel.vue'

export default {
  components: {
    ChannelSelector
  },
  data() {
    return {
      currentText: '', // 当前输入框中的文本
      bottomBarBottom: 0,
      writerListBottom: 0,
      aiToolPopupVisible: false, // AI工具弹窗控制
      aiListData: [], // AI工具列表
      channelSelectorVisible: false, // 频道选择弹窗控制
      listData: [], // 频道列表数据
      safeAreaBottom: 0
    };
  },
  props: {
    topicList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    // 计算文案总数
    writerCount() {
      return this.topicList.length;
    }
  },
  mounted() {
    this.safeAreaBottom = uni.getSystemInfoSync().safeAreaInsets.bottom + uni.upx2px(24);
    this.writerListBottom = this.safeAreaBottom + uni.upx2px(128);
    // 监听从其他页面返回的文案数据
    uni.$on('return-writer-data', this.handleReturnWriterData);
    setTimeout(() => {
      console.log(this.aiListData, 'aiListData');

    }, 3000)
    // 获取AI工具列表
    this.getAiToolList();

    // 获取频道列表
    this.getChannelList();
  },
  beforeDestroy() {
    // 移除事件监听
    uni.$off('return-writer-data');
  },
  methods: {
    // 打开文本编辑器
    openTextEditor() {
      uni.navigateTo({
        url: '/subpkg/index/edit_text/index?type=1',
        success: (res) => {
          // 发送初始空数据
          res.eventChannel.emit('post_data', {
            data: {
              title: '',
              content: this.currentText || ''
            }
          });

          // 监听完成编辑后的回调
          res.eventChannel.on('get_content', (data) => {
            if (data && data.content) {
              // 添加到文案列表
              this.addWriter({
                title: data.title || '自定义文案',
                content: data.content
              });

              // 更新当前文本显示
              this.currentText = data.content;
            }
          });
        }
      });
    },

    // 获取频道列表
    getChannelList() {
      getGroup().then(res => {
        this.listData = res.data;
      }).catch(err => {
        console.error('获取频道列表失败:', err);
        uni.showToast({
          title: '获取频道列表失败',
          icon: 'none'
        });
      });
    },

    // 处理频道选择
    handleListMessage(item) {
      uni.navigateTo({
        url: `/subpkg/index/get_radio/listMessage?id=${item.id}`,
        events: {
          get_content: (data) => {
            // 添加到文案列表
            this.addWriter({
              title: data.title || `频道文案-${item.title || ''}`,
              content: data.content
            });
          }
        }
      });
    },

    // 获取AI工具列表
    getAiToolList() {
      this.$api.get_authorization().then(res => {
        this.aiListData = res.rows;
      }).catch(err => {
        console.error('获取AI工具列表失败:', err);
      });
    },

    // 打开AI工具弹窗
    openAiModelPopup() {
      this.aiToolPopupVisible = true;
    },

    // 处理AI工具选择
    handleListAiMessage(item) {
      this.aiToolPopupVisible = false;
      uni.navigateTo({
        url: '/subpkg/index/page_copy?source=selectWriter',
        success(res) {
          res.eventChannel.emit('get_message', item);
        }
      });
    },

    // 处理从其他页面返回的文案数据
    handleReturnWriterData(data) {
      console.log(data);
      
      if (data && data.title && data.content) {
        this.addWriter({
          title: data.title,
          content: data.content
        });
      }
    },

    // 生成随机文案
    generateRandomText() {
      uni.showLoading({ title: '加载中...' });
      getRandomText({ longTextFlag: 1 }).then(res => {
        uni.hideLoading();
        if (res.data) {
          this.currentText = res.data.inputJson;
          // 添加到文案列表
          this.addWriter({
            title: res.data.title || '随机文案',
            content: res.data.inputJson
          });
        }
      }).catch(err => {
        uni.hideLoading();
        // uni.showToast({
        //   title: '获取随机文案失败',
        //   icon: 'none'
        // });
      });
    },

    // 打开视频链接提取页面
    extractVideoText() {
      // 跳转到视频文案提取页面，添加来源标记
      uni.navigateTo({
        url: '/subpkg/index/extractionCopy?from=selectWriter'
      });
    },

    // 打开频道选择器
    openChannelSelector() {
      // 打开频道选择弹窗
      this.channelSelectorVisible = true;
    },

    toggleExpand(index) {
      // 切换指定索引的文案的展开/收起状态
      const updatedTopicList = [...this.topicList];
      updatedTopicList[index] = {
        ...updatedTopicList[index],
        expanded: !updatedTopicList[index].expanded
      };
      this.$emit('saveTopicList', updatedTopicList);
    },

    // 上一步
    prevStep() {
      uni.$emit('batch-video-prev-step');
    },

    // 下一步
    nextStep() {
      // 检查是否有文案
      if (this.writerCount === 0) {
        uni.showToast({
          title: '请至少添加一个文案',
          icon: 'none'
        });
        return;
      }

      // 切换到下一步
      uni.$emit('batch-video-next-step');
    },

    deleteWriter(index) {
      const updatedTopicList = [...this.topicList];
      updatedTopicList.splice(index, 1);
      this.$emit('saveTopicList', updatedTopicList);
    },

    addWriter(writer) {
      // 添加expanded属性，默认为false
      const newWriter = {
        ...writer,
        expanded: false
      };
      const updatedTopicList = [...this.topicList, newWriter];
      this.$emit('saveTopicList', updatedTopicList);
    }
  }
};
</script>

<style lang="scss">
.writer-container {
  background-color: #f3f5f8;
  width: 100%;
  height: 100%;

  // 文案输入区域
  .input-area {
    background-color: #ffffff;
    border-radius: 24rpx;
    width: 686rpx;
    padding-bottom: 24rpx;
    margin-bottom: 32rpx;

    .input-box {
      padding: 32rpx;
      min-height: 120rpx;

      .placeholder {
        color: #999999;
        font-size: 28rpx;
        line-height: 40rpx;
      }

      .text-content {
        color: #333333;
        font-size: 28rpx;
        line-height: 40rpx;
      }
    }

    .tools-row {
      display: flex;
      flex-direction: row;
      padding: 0 24rpx;
      margin-top: 80rpx;

      .tool-item {
        margin-right: 12rpx;

        &:last-child {
          margin-right: 0;
        }

        .tool-button {
          background-color: #f3f5f8;
          border-radius: 8rpx;
          height: 56rpx;
          padding: 0 18rpx;
          display: flex;
          flex-direction: row;
          align-items: center;

          .tool-icon {
            width: 24rpx;
            height: 24rpx;
            margin-right: 8rpx;
          }

          .tool-text {
            color: #666666;
            font-size: 24rpx;
            line-height: 32rpx;
            font-weight: 500; 
          }
        }
      }
    }
  }

  // 文案列表
  .writer-list {
    width: 686rpx;

    .writer-item {
      background-color: #ffffff;
      border-radius: 24rpx;
      padding: 32rpx;
      margin-bottom: 32rpx;

      .writer-header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24rpx;

        .writer-title {
          color: #333333;
          font-size: 28rpx;
          font-weight: bold;
          line-height: 40rpx;
          max-width: 480rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .delete-icon {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .writer-content {
        display: flex;
        flex-direction: row;
        align-items: flex-start;

        .content-wrapper {
          position: relative;
          flex: 1;

          .content-text {
            color: #999999;
            font-size: 24rpx;
            line-height: 36rpx;
            height: 108rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            transition: height 0.3s ease;

            &.expanded {
              height: auto;
              -webkit-line-clamp: unset;
            }
          }

          .expand-text {
            position: absolute;
            right: 0rpx;
            bottom: 0;
            color: #666666;
            font-size: 24rpx;
            line-height: 32rpx;
            font-weight: bold;
            z-index: 2;
            padding-left: 40rpx;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 20%);
          }
        }

        .expand-icon {
          width: 32rpx;
          height: 32rpx;
          align-self: flex-end;
          margin-left: 8rpx;
          transition: transform 0.3s ease;

          &.rotate {
            transform: rotate(180deg);
          }
        }
      }
    }
  }

  // 底部操作栏
  .bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    // height: 128rpx;
    background-color: #ffffff;
    border-radius: 16rpx 16rpx 0 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 48rpx;
    box-sizing: border-box;
    z-index: 4;

    .selected-count {
      background-color: #efefef;
      border-radius: 12rpx;
      height: 64rpx;
      padding: 0 32rpx;
      display: flex;
      align-items: center;

      .count-text {
        color: #666666;
        font-size: 24rpx;

        .count-number {
          color: #21bd74;
        }
      }
    }

    .bottom-bar-btn {
      display: flex;

      .prev-btn,
      .next-btn {
        height: 80rpx;
        width: 180rpx;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 24rpx;

        .btn-text {
          font-size: 28rpx;
          font-weight: bold;
        }
      }
    }

    .prev-btn {
      background-color: #efefef;

      .btn-text {
        color: #666666;
      }
    }

    .next-btn {
      background: linear-gradient(135deg, #22232c 0%, #0f0f0f 100%);

      .btn-text {
        color: #ffffff;
      }
    }
  }
}

/* AI模型弹窗样式 */
.popup-container {
  padding: 40rpx;

  .popup-title {
    font-weight: 600;
    font-size: 32rpx;
    text-align: center;
    margin-bottom: 30rpx;
  }

  .tool-item {
    display: flex;
    align-items: center;
    background: #F3F5F8;
    border-radius: 20rpx;
    padding: 24rpx 48rpx;
    margin-top: 20rpx;
    border: 2rpx solid white;

    &:active {
      background: rgba(204, 252, 234, 0.3);
      border: 2rpx solid #21BD74;
    }

    image {
      width: 60rpx;
      height: 60rpx;
    }

    .tool-info {
      flex: 1;
      padding: 0 20rpx;
      position: relative;

      .ellipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 22rpx;
        color: #666;
        margin-top: 4rpx;
      }
    }
  }
}
</style>