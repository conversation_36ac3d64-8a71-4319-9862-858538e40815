/**
 * 装修主题 Vuex 模块
 * 管理装修主题的数据和状态
 */

import {
  convertGridToRpx,
  getComponentStyle,
  getImageComponentStyle,
  getTextComponentStyle,
  getFunctionComponentStyle,
  getFunctionIconStyle,
  getFunctionBigIconStyle,
  getFunctionTitleStyle,
  getFunctionDescStyle,
  getFunctionBackgroundImageStyle,
  getCustomTemplate
} from '@/utils/fitmentDataLoader.js'

import {
  convertServerDataToMobile,
  validateMobileData
} from '@/utils/dataFormatConverter.js'

const state = {
  // 装修数据
  fitmentData: {},

  // 页面状态
  loading: true,
  error: null,

  // TabBar 相关
  currentTabIndex: 0,

  // 是否使用自定义模板
  isCustomTemplate: false,

  // 静态资源
  defaultImage: '/static/images/default-image.png'
}

const getters = {
  // 布局项目
  layoutItems: state => state.fitmentData.layout || [],

  // 背景样式
  backgroundStyle: state => {
    const style = {}
    const background = state.fitmentData.background || {}
    if (background.type === '色彩填充') {
      style.backgroundColor = background.color || '#FFFFFF'
      return style
    }

    if (background.type === '上传背景') {
      return background
    }
  },

  // 是否使用自定义模板
  isCustomTemplate: state => state.isCustomTemplate,

  // TabBar 列表
  tabList: state => {
    return (state.fitmentData.navigation?.items || []).map(item => ({
      ...item,
      enabled: item.enabled !== false
    }))
  },

  // 是否显示 TabBar
  showTabBar: state => {
    const tabList = (state.fitmentData.navigation?.items || []).map(item => ({
      ...item,
      enabled: item.enabled !== false
    }))
    return state.fitmentData.navigation?.enabled !== false && tabList.length > 0
  },

  // 页面标题
  pageTitle: state => state.fitmentData.page?.title || '装修主题',

  // 导航栏样式配置
  navigationStyle: state => {
    return state.fitmentData.navigation?.style || {
      backgroundColor: '#FFFFFF',
      textColor: '#666666',
      activeTextColor: '#21BD74',
      fontSize: 12
    }
  },

  // 页面统计信息
  pageStatistics: (state, getters) => {
    const stats = {
      totalComponents: getters.layoutItems.length,
      componentTypes: {},
      hasBackground: state.fitmentData.background?.type !== '色彩填充' || state.fitmentData.background?.image,
      hasNavigation: getters.showTabBar,
      dataSize: JSON.stringify(state.fitmentData).length,
      lastModified: state.fitmentData.timestamp ? new Date(state.fitmentData.timestamp).toLocaleString() : '未知'
    }

    // 统计组件类型
    getters.layoutItems.forEach(item => {
      const type = item.type
      stats.componentTypes[type] = (stats.componentTypes[type] || 0) + 1
    })

    return stats
  },

  // 计算容器最小高度
  containerMinHeight: (state, getters) => {
    if (!getters.layoutItems || getters.layoutItems.length === 0) return 1000

    let maxY = 0

    getters.layoutItems.forEach(item => {
      const position = item.position || {}
      const itemBottom = (position.y || 0) + (position.height || 1)
      if (itemBottom > maxY) {
        maxY = itemBottom
      }
    })

    return convertGridToRpx(maxY, true) + 32 // 添加底部间距
  }
}

const mutations = {
  // 设置装修数据
  SET_FITMENT_DATA(state, data) {
    state.fitmentData = data
  },

  // 设置加载状态
  SET_LOADING(state, loading) {
    state.loading = loading
  },

  // 设置错误信息
  SET_ERROR(state, error) {
    state.error = error
  },

  // 设置当前Tab索引
  SET_CURRENT_TAB_INDEX(state, index) {
    state.currentTabIndex = index
  },

  // 设置是否使用自定义模板
  SET_IS_CUSTOM_TEMPLATE(state, isCustom) {
    state.isCustomTemplate = isCustom
  },

  // 重置状态
  RESET_STATE(state) {
    state.fitmentData = {}
    state.loading = true
    state.error = null
    state.currentTabIndex = 0
    state.isCustomTemplate = false
  }
}

const actions = {
  /**
   * 初始化页面数据
   */
  async initPageData({ commit, state }) {
    try {
      commit('SET_LOADING', true)
      commit('SET_ERROR', null)

      // 尝试获取当前小程序的appId
      let appId = null
      try {
        appId = uni.getAccountInfoSync().miniProgram.appId
        console.log('当前小程序appId:', appId)
      } catch (e) {
        console.warn('无法获取appId:', e)
      }

      // 尝试获取自定义模板
      const templateData = await getCustomTemplate(appId)
      console.log('获取到的模板数据:', templateData)

      if (templateData) {
        // 如果获取到了自定义模板，使用自定义模板
        commit('SET_FITMENT_DATA', JSON.parse(templateData))
        commit('SET_IS_CUSTOM_TEMPLATE', true)
        console.log('使用自定义模板')

      } else {
        // 如果没有获取到自定义模板，只设置标记
        console.log('自定义模板不存在')
        commit('SET_FITMENT_DATA', {})
        commit('SET_IS_CUSTOM_TEMPLATE', false)
      }

      console.log('页面数据初始化完成')
      return true
    } catch (error) {
      console.error('页面数据加载失败:', error)
      commit('SET_ERROR', error.message || '页面数据加载失败')
      commit('SET_IS_CUSTOM_TEMPLATE', false)
      commit('SET_FITMENT_DATA', {})
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  /**
   * 刷新数据
   */
  async refreshData({ dispatch }) {
    return await dispatch('initPageData')
  },
}

// 导出样式计算方法，供组件直接使用
export const styleHelpers = {
  convertGridToRpx,
  getComponentStyle,
  getImageComponentStyle,
  getTextComponentStyle,
  getFunctionComponentStyle,
  getFunctionIconStyle,
  getFunctionBigIconStyle,
  getFunctionTitleStyle,
  getFunctionDescStyle,
  getFunctionBackgroundImageStyle
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
