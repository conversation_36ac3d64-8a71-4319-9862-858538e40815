/**
 * todo 临时处理一下，避免闪剪报错
 * 只缩放宽度为1080，crop 参数也传但不实际裁剪
 * @param {number} originalWidth 原始视频宽度
 * @param {number} originalHeight 原始视频高度
 * @param {number} targetWidth 目标宽度（默认1080）
 * @param {number} duration 裁剪时长，默认30秒
 * @returns {{ scale: [number, -1], crop: [number, number, number, number], duration: [number, number] }}
 */
export function getSafeScaleWithDummyCrop(
    originalWidth,
    originalHeight,
    targetWidth = 1080,
    duration = 30
) {
    // const enforcedMinWidth = 1080;
    // const safeTargetWidth = Math.max(targetWidth, enforcedMinWidth);
    // 缩放比例（保持纵横比）
    // const scaleRatio = safeTargetWidth / originalWidth;
    // const scaledHeight = Math.round(originalHeight * scaleRatio);
    // 这里 crop 区域设置为整张图：不做实际裁剪
    return {
        scale: [originalWidth, -1], // 只缩放宽度，高度自适应
        crop: [originalWidth, originalHeight, 0, 0], // 裁剪整张图，不裁任何边
        duration: [0, duration],
    };
}
