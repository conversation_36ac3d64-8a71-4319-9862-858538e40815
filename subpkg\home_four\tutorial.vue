<template>
	<view class="page">
		<view class="video-list">
			<view v-for="(item, index) in page_arr" :key="index" class="video-item" @click="playVideo(item.videoUrl)">
				<view class="video-wrapper">
					<image class="video-thumbnail" mode="aspectFill" :src="item.videoUrl + '?x-oss-process=video/snapshot,t_0'"></image>
					<view class="play-icon">
						<image :src="ossPath + '/project1_video_play.png'" mode="aspectFit"></image>
					</view>
				</view>
				<view class="video-title">{{item.title}}</view>
			</view>
		</view>
		
		<!-- 视频播放器组件 -->
		<view v-if="currentVideo" class="video-player-container">
			<video 
				id="myVideo" 
				:src="currentVideo" 
				controls 
				autoplay 
				class="fullscreen-video"
				@fullscreenchange="onFullscreenChange"
				object-fit="contain"
			></video>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				page_arr: [],
				currentVideo: null,
				videoContext: null,
				ossPath: this.$appConfig.appInfo.ossPath
			}
		},
		onLoad() {
			this.$api.get_tutorial().then(res => {
				console.log(res)
				this.page_arr = res.data
			})
		},
		onReady() {
			// 创建视频上下文
			this.videoContext = uni.createVideoContext('myVideo', this)
		},
		methods: {
			playVideo(url) {
				this.currentVideo = url
				this.$nextTick(() => {
					// 进入全屏播放
					this.videoContext.requestFullScreen({direction: 0})
					this.videoContext.play()
				})
			},
			onFullscreenChange(e) {
				if (!e.detail.fullScreen) {
					// 退出全屏时停止播放
					this.videoContext.stop()
					this.currentVideo = null
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.page {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 20rpx;
	}
	
	.video-list {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 20rpx;
	}
	
	.video-item {
		width: 48%;
		margin-bottom: 30rpx;
		background: #fff;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
	}
	
	.video-wrapper {
		position: relative;
		width: 100%;
		height: 400rpx;
		overflow: hidden;
	}
	
	.video-thumbnail {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	
	.play-icon {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		background: rgba(0,0,0,0.3);
		border-radius: 50%;
		padding: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100rpx;
		height: 100rpx;
		
		image {
			width: 60rpx;
			height: 60rpx;
		}
	}
	
	.video-title {
		font-size: 28rpx;
		color: #333;
		padding: 20rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	
	.video-player-container {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: #000;
		z-index: 999;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.fullscreen-video {
		width: 100%;
		height: 100%;
	}
</style>