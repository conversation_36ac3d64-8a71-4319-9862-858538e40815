<template>
	<view>
		<!--    <logingModel></logingModel>-->
		<vipVersion :pageShow="page_show" @show_model="show_model" source="genVideo" @down_close="handleCloseVipVersion"
			:fastPowerRange="fastPowerRange" :numberType="number_type"></vipVersion>
		<view class="page">
			<custom-navbar title="生成数字人视频" titleSize="36" titleFontFamily="MiSans-Demibold" :showBackBtn="true"
				:backgroundImage="showBackground" :paddingBottom="14" :isBlack="true"></custom-navbar>
			<view style="background: white;padding: 32rpx;border-radius: 20rpx;position: relative;">
				<view class="top_btn_box" :style="{ background: page_index == 0 ? '#F3F5F8' : '#fee7d5' }">
					<view class="top_btn" @click="handlePageIndexChange(0)"
						:style="{ background: page_index == 0 ? '#ffffff' : '#fee7d5' }"
						:class="page_index == 0 ? 'top_btn_active' : ''">
						数字人视频
						<view v-if="page_index == 0" class="active-indicator"></view>
					</view>
					<view class="top_btn" @click="handlePageIndexChange(1)" :class="page_index == 1 ? 'top_btn_active' : ''">
						智能剪辑视频
						<view v-if="page_index == 1" class="active-indicator"></view>
					</view>
				</view>

				<view style="height: 30rpx;"></view>

				<view v-if="page_index == 0" style="margin-top: 45rpx">
					<view class="box_text">
						<view>选择数字人</view>
						<view class="text_too" @click="handleAddNumberUser">
							<text>+</text>
							<view>新增数字人</view>
						</view>
					</view>

					<view class="content_none" v-if="!showDrList">
						<view class="text_one">您还没有数字人，快去定制一个吧</view>
						<view class="box_button" @click="handleAddNumberUser">
							定制数字人
						</view>
					</view>
					<view v-else>
						<!-- 骨架屏 -->
						<scroll-view v-if="isDrListLoading" class="page_scroll" scroll-x="true" scroll-left="0" scrollbar="true">
							<view class="skeleton-box" v-for="i in 4" :key="i">
								<view class="skeleton-image"></view>
							</view>
						</scroll-view>
						<!-- 真实内容 -->
						<scroll-view v-else class="page_scroll" scroll-x="true" scroll-left="0" scrollbar="true">
							<view v-for="item in dr_list" class="numberCard" @click="down_card(item)" :key="item.id"
								:class="item.outId == change_numberid ? 'image_active' : ''">
								<image :src="item.cover" mode="aspectFit" class="image_">
								</image>
								<image class="right_top" src="/static/index/changed.png" v-if="item.outId == change_numberid"></image>
								<image v-if="item.type == 'pro'" mode="heightFix" src="/static/index/pro_vip.png" class="right_bottom">
								</image>
							</view>
						</scroll-view>
					</view>
				</view>
				<view v-else>
					<view class="box_text" style="margin-top: 45rpx;">
						<view>选择模版</view>
						<view class="text_too" @click="handleAddStencil">
							<text>+</text>
							<view> 新增模版</view>
						</view>
					</view>

					<view class="content_none" v-if="templatelist.length < 1">
						<view class="text_one">您还没有模版，快去定制一个吧</view>
						<view class="box_button" @click="handleAddStencil">
							新建模版
						</view>
					</view>
					<view v-else>
						<!-- 骨架屏 -->
						<scroll-view v-if="isTemplateListLoading" class="page_scroll" scroll-x="true" scroll-left="0"
							scrollbar="true">
							<view class="skeleton-box" v-for="i in 4" :key="i">
								<view class="skeleton-image"></view>
							</view>
						</scroll-view>
						<!-- 真实内容 -->
						<scroll-view v-else class="page_scroll" scroll-x="true" scroll-left="0" scrollbar="true">
							<view v-for="item in templatelist" class="numberCard" @click="down_card2(item)" :key="item.id"
								:class="item.status == 'change' ? 'image_active' : ''">
								<image :src="item.preUrl" mode="aspectFit" class="image_">
								</image>
								<image class="right_top" src="/static/index/changed.png" v-if="item.status == 'change'"></image>
							</view>
						</scroll-view>
					</view>
				</view>
				<view class="box_text" style="margin-top: 20rpx;" @click="change_music"
					:class="{ bottomLine: page_index == 1 }">
					<view>选择声音</view>
					<view class="text_sound" v-if="!page_voice_show">
						点击选择您的声音
					</view>
					<view v-else @click="change_music">
						<view style="display: flex;align-items: center;" class="text_sound">
							<view>{{ change_voiceing.name }}</view>
							<image v-if="change_voiceing == 'pro'" src="/static/index/pro_vip.png" mode="heightFix"
								style="height: 30rpx;margin-left: 20rpx;"></image>
							<view style="margin-left: 20rpx;">({{ music_value.toFixed(1) }}倍速)</view>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</view>
				</view>
				<view v-if="page_index == 1" class="bottomLine">

					<view class="box_text" style="margin-top: 10rpx" @click="choose_music" v-if="!selectedMusic">
						<view>选择音乐</view>
						<u-icon name="arrow-right" size="40rpx" color="#7e7f80"></u-icon>
					</view>
					<view v-else class="box_text" @click="choose_music">
						<view>选择音乐</view>
						<view class="text_sound">
							{{ selectedMusic.name }}
						</view>
					</view>
				</view>
				<view v-if="page_index == 1" style="margin-top: 20rpx;">
					<view class="box_text" @click="chooseMaterial" v-if="!selectedMaterial">
						<view>选择素材</view>
						<u-icon name="arrow-right" size="40rpx" color="#7e7f80"></u-icon>
					</view>
					<view v-else class="box_text" @click="chooseMaterial">
						<view>选择素材</view>
						<view class="text_sound">
							{{ selectedMaterial.name }}
						</view>
					</view>
				</view>
			</view>
			<view>
			</view>
			<view style="margin-top:15rpx;background: white; padding: 10rpx 20rpx 32rpx 20rpx; border-radius: 20rpx;">
				<view class="card_list">
					<view v-for="(item, index) in card_list" @click="change_list(item)" class="list_card" :key="index">
						<view style="text-align: center;">
							<image :src="page_img_statr + item.src"></image>
							<view class="text_scal">{{ item.name }}</view>
						</view>
					</view>


					<view class="list_card" style="width: 180rpx;" @click="start_music" v-if="!isPlaying">
						<view class="icon-wrapper">
							<image v-if="!isPollingLoading" :src="page_img_statr + 'project1_get_radio_bf.png'" class="icon-image">
							</image>
							<wave-loading v-if="isPollingLoading" color="#01f675" barWidth="5rpx" barSpacing="9rpx" />
						</view>
						<view class="text_scal" style="justify-content: flex-end;">
							<span style="color: #01f675;" v-if="isPollingLoading">加载中...</span>
							<span v-else style="color: #01f675;">试听(1算力/次)</span>
						</view>
					</view>
					<view class="list_card" style="width: 180rpx;" @click="stop_music" v-else>
						<view style="text-align: center;">
							<view class="icon-wrapper">
								<image :src="page_img_statr + 'project1_get_radio_zt.png'" class="icon-image"></image>
							</view>
							<view class="text_scal" style="color: #01f675;">试听(1算力/次)</view>
						</view>
					</view>
				</view>

				<!-- oss音频 -->
				<!-- <view style="text-align: center;" v-if="voicePath">
					<image v-if="!videoing" src="/static/home/<USER>/bofangluyin.png" @click="playVoice"></image>
					<image v-else src='/static/home/<USER>/zanting.png' @click="stop_video"></image>
				</view> -->

				<u-divider></u-divider>

				<view @click="handleEditText" class="content-box" style="margin-top: 40rpx;height: 600rpx;">
					<view v-show="page_index == 1" class="page_text_title" v-html="page_title ? page_title : '在此输入标题'"></view>
					<!-- <u--textarea  maxlength="-1" v-model="page_textarea" placeholder="请输入内容" autoHeight :border="false">
					</u--textarea> -->
					<view class="page_textarea">
						<rich-text v-if="page_textarea" :nodes="replace_pause(page_textarea)" :selectable="false"
							:user-select="false"></rich-text>
						<text v-else>在此输入或粘贴您的文案...</text>
					</view>

				</view>
			</view>


			<!-- <view style="height: 220rpx;"></view> -->
			<view class="tab-bottom" :style="{ paddingBottom: safeAreaHeight + 10 + 'px' }">
				<view class="card" @click="handleMyVideo">
					<image src="/static/home/<USER>/dianshi.png"></image>
					<view>我的作品</view>
				</view>
				<view class="card" @click="handleExample">
					<image src="/static/home/<USER>/shipin3.png"></image>
					<view>案例样片</view>
				</view>
				<view class="card" @click="toPowerRule">
					<image src="/static/home/<USER>/jjifen.png"></image>
					<view>算力消耗</view>
				</view>


				<view v-if="page_index == 0" class="text_button" @click="post_video"
					:class="change_numberid && change_voiceing.speakerId ? 'text_button_active' : ''">
					生成视频
				</view>
				<view v-else class="text_button" @click="post_video_mb"
					:class="change_voiceId && change_voiceing.speakerId ? 'text_button_active' : ''">
					生成视频
				</view>
			</view>
		</view>
		<InsufficientPermissions v-if="page_model" @closeModel="close"></InsufficientPermissions>
		<channel-selector :show="model_bottom" :channelList="listData" @close="model_bottom = false"
			@select="handleListMessage"></channel-selector>
		<u-popup :show="voice_model_bottom" @close="voice_model_bottom = false" :round="20">
			<view class="page_modelShow">
				<view class="model_title">选择方式</view>
				<view class="model_box" v-for="item in voice_list" :key="item.id">
					<view style="width: 80rpx;">
						<image :src="item.icon"></image>
					</view>
					<view>{{ item.name }}</view>
				</view>
			</view>
		</u-popup>
		<u-popup :show="ai_model_bottom" @close="ai_model_bottom = false" :round="20">
			<view class="page_modelShow" style="height: 60vh;overflow-y: scroll;">
				<view class="model_title">选择工具</view>
				<view class="model_box"
					style="background: none;border-radius: 0;border-bottom: 1rpx solid #f5f5f5;display: flex;align-items: flex-end;"
					v-for="item in aiListData" @click="handleListAiMessage(item)" :key="item.id">
					<image :src="item.icon"></image>
					<view class="text_1 ellipsis" style="display: flex;flex-direction:column;justify-content: space-around;">
						<view style="font-size:30rpx;font-family: ChillRoundGothic_Medium,serif;font-weight: normal">{{ item.name }}
						</view>
						<view class="ellipsis" style="font-size: 24rpx;color: #919191">{{ item.description }}</view>
					</view>
				</view>
			</view>
		</u-popup>

		<u-popup :show="music_model_bottom" @close="music_model_bottom = false" :round="20">
			<view class="page_modelShow">

				<view class="model_sound" @click="edit_music" style="margin-bottom: 20rpx;">
					<!-- <view>当前语速</view> -->
					<view class="model_title">语速: {{ music_value.toFixed(1) }}</view>
					<view>
						<view style="display: flex;justify-content: space-between;align-items: center;">
							<view>慢速</view>
							<view style="width: 70vw;">
								<u-slider v-model="music_value" activeColor="#21BD74" step="0.1" min="0.7" max="1.3"
									blockColor="#21BD74"></u-slider>
							</view>
							<view>快速</view>
						</view>
					</view>
				</view>
				<view class="model_box" style="justify-content: space-between;" @click="handleAddMusic">
					<view style="display: flex;align-items: center;">
						<image style="width: 80rpx;height: 80rpx;" :src="page_img_statr + 'project1_home1_get_radio_music_add.png'">
						</image>
						<view class="model_voicelist">
							<view>录制音频</view>
						</view>
					</view>
				</view>
				<view class="model-box-list">
					<view v-for="item in music_list" :class="item.state == 'change' ? 'model_box_active' : 'model_box'"
						style="justify-content: space-between;" @click="down_voice(item)" :key="item.id">
						<view style="display: flex;align-items: center;">
							<!-- 添加播放按钮 -->
							<view class="preview-audio" @click.stop>
								<image v-if="playingVoiceId === item.outId" @click="stopPreviewVoice()"
									:src="page_img_statr + 'project1_home4_music_playing.gif'" class="preview-icon"></image>
								<image v-else @click="previewVoice(item)" src="/static/home/<USER>/get_radio_bf.png"
									class="preview-icon"></image>
							</view>
							<view class="model_voicelist">
								<view style="display: flex;align-items: center;">
									<view>{{ item.name }}</view>
									<image v-if="item.type == 'pro'" mode="heightFix" style="height: 30rpx;margin-left: 10rpx;"
										src="/static/index/pro_vip.png"></image>
								</view>
							</view>
						</view>
						<image v-if="item.state == 'change'" src="/static/index/changed.png" style="width: 40rpx;height: 40rpx;">
						</image>
					</view>
				</view>

				<view class="project_btn" @click="change_voice">确定</view>
			</view>
		</u-popup>


		<u-popup :show="templateModel" mode="center" round="20">
			<view class="template_box">
				<view style="position: absolute;top: 20rpx; right: 20rpx;" @click="templateModel = false">
					<u-icon name="close-circle" size="60rpx"></u-icon>
				</view>

				<view style="font-size: 36rpx;font-weight: 600;margin-bottom: 20rpx;">模版信息</view>

				<image style="width: 200rpx;border-radius: 20rpx;" mode="widthFix" :src="templateItem.preUrl"></image>
				<view class="text_1">
					<view class="title">数字人</view>
					<view class="text">{{ templateItem.name }}</view>
				</view>
				<view class="text_1">
					<view class="title">标题</view>
					<image class="text" style="height: 60rpx;" mode="heightFix" :src="templateItem.titleUrl"></image>
				</view>
				<view class="text_1">
					<view class="title">字幕</view>
					<image class="text" style="height: 60rpx;" mode="heightFix" :src="templateItem.captionUrl"></image>
				</view>
				<view class="text_1">
					<view class="title">身份栏</view>
					<view class="text">{{ templateItem.introduceCardName }}</view>
				</view>
				<!-- <view class="text_1">
					<view class="title">背景</view>
					<image class="text" style="width: 50rpx;" mode="widthFix" :src="templateItem.bgUrl"></image>
				</view> -->

				<view class="project_btn" style="margin-top: 40rpx;" @click="down_del(templateItem.id)">删除模板</view>
			</view>

		</u-popup>


		<u-popup :show="video_model" mode="bottom" round="20" @close="video_model = false">

			<view style="position: absolute;top: 20rpx;right: 20rpx;padding: 10rpx" @click="video_model = false;">
				<u-icon name="close" size="20" style="padding: 20rpx"></u-icon>
			</view>


			<view style="padding: 200rpx 0 100rpx 0;text-align: center;">


				<view v-if="page_state == 3">
					<view style="position: absolute;top: 40rpx;left: 50%;transform: translateX(-50%);">{{ page_time }}
					</view>

					<view style="display: flex;justify-content: space-around;align-items: center;">
						<image style="width: 100rpx;height: 100rpx;" src="/static/home/<USER>/again.png"
							@click="() => { model_wxts = true; video_model = false }"></image>


						<image style="width: 150rpx;height: 150rpx;" src="/static/home/<USER>/luyin.png" @click="continue_video">
						</image>


						<image style="width: 100rpx;height: 100rpx;" src="/static/home/<USER>/save.png" @click="save_video">

						</image>
					</view>

					<view v-if="message_show"
						style="color: red;background: #ffddc9;display: inline-block;border-radius: 20rpx;padding: 4rpx 10rpx;margin-top: 20rpx;font-size: 22rpx">
						最少录制5秒，请继续录制
					</view>

				</view>



				<!-- 暂停 -->
				<view v-if="page_state == 2">
					<view style="position: absolute;top: 20rpx;left: 50%;transform: translateX(-50%);">{{ page_time }}
					</view>
					<image style="width: 150rpx;height: 150rpx;" src="/static/home/<USER>/luzhi.png" @click="endRecord">
					</image>
				</view>


				<!-- 开始录制 -->
				<view v-if="page_state == 1">
					<image style="width: 150rpx;height: 150rpx;" src="/static/home/<USER>/luyin.png" @click="startRecord">
					</image>
					<view>
						点击开始录制
					</view>
				</view>
			</view>
		</u-popup>
		<projectModel style="z-index: 999999;" title="温馨提示" v-if="model_wxts" content="您确认要重新录制吗?重新录制将丢失当前录制内容。" save="重新录制"
			@btn_close="() => { model_wxts = false; video_model = true }" @btn_save="again_video">
		</projectModel>

		<!-- 添加内容验证提示弹窗 -->
		<projectModel style="z-index: 999999;" title="提示" v-if="contentValidationModel" :content="validationMessage"
			@btn_close="() => contentValidationModel = false" @btn_save="() => contentValidationModel = false">
		</projectModel>

	</view>
</template>

<script>
import {
	getUserType,
	getVoiceList,
	getTemplateList
} from '../../../api/numberUser/userType.js'
import {
	getRandomText,
	getGroup
} from '../../../api/numberUser/copy.js'
import {
	postCreateVideo,
	postListenerAudio
} from '../../../api/numberUser/dr.js'

import {
	requests
} from '../../../utils/request.js'

import {
	uploadToOss
} from '../../../utils/ali-oss.js'

import { page_img_statr } from '../../../api/getData.js'
import ChannelSelector from '../../index/components/channel.vue'
import { mapState } from 'vuex'
import WaveLoading from "@/components/wave-loader/index.vue";

export default {
	components: {
		WaveLoading,
		ChannelSelector
	},
	data() {
		return {
			// 安全区域高度
			safeAreaHeight: 0,

			isDrListLoading: true,      // 数字人列表加载状态
			isTemplateListLoading: true, // 模板列表加载状态

			templateModel: false,
			templateItem: '',
			templateId: '',
			figureId: '',
			metadata: '',
			page_Polling: null,
			polling_id: '',
			page_voice_show: false,
			change_voiceing: '',
			change_numberid: '',
			change_voiceId: '',
			voice_list: [{
				name: '录制音频',
				icon: '/static/home/<USER>/luzhiyinpin.png'
			},
			{
				name: '提取视频中的音频',
				icon: '/static/home/<USER>/xiazaiwenjian.png'
			},
			{
				name: '上传音频文件',
				icon: '/static/home/<USER>/shangchuanwenjian.png'
			}
			],
			number_type: '',
			music_list: [],
			music_value: 1,
			edit_sound_show: false,
			listData: '',
			aiListData: '',
			model_bottom: false,
			ai_model_bottom: false,
			music_model_bottom: false,
			voice_model_bottom: false,
			dr_list: [],
			templatelist: [],
			page_type: '',
			page_model: false,
			fastPowerRange: '',
			page_show: false,
			list1: [{
				name: '数字人视频'
			},
			{
				name: '智能剪辑视频'
			},
			],
			page_index: 0,
			card_list: [{
				src: 'project1_get_radio_sj.png',
				name: '随机文案'
			},
			{
				src: 'project1_get_radio_pd.png',
				name: '频道文案'
			},
			// {
			// 	src: 'project1_get_radio_yp.png',
			// 	name: '录制音频'
			// },
			{
				src: 'project1_get_radio_wa.png',
				name: 'AI文案'
			}

			],
			page_textarea: '',
			page_title: '',

			innerAudioContext: null, // 音频上下文
			isPlaying: false, // 是否正在播放

			recorderManager: null,

			time1: 0,
			time2: 0,
			page_state: 1,

			video_model: false,
			model_wxts: false,
			message_show: false,
			clTime: null,
			authorization: false,
			videoing: false,
			voicePath: '',
			oss_videoUrl: null,
			selectedMaterial: null,

			image_url_start: '',
			page_img_statr: '',

			// 存储选中的音乐信息
			selectedMusic: null,

			// 添加正在播放的音频ID
			playingVoiceId: '',
			isPollingLoading: false,

			// 内容验证相关
			contentValidationModel: false,
			validationMessage: '',

			// 添加用户类型数据缓存
			userTypeData: null,
		}
	},
	watch: {
	},
	onLoad() {
		this.page_img_statr = page_img_statr
		// 获取安全区域高度
		let app = uni.getSystemInfoSync();
		this.safeAreaHeight = app.safeAreaInsets.bottom

		// 添加音乐选择监听
		uni.$on('selectMusic', (data) => {
			console.log('收到选中的音乐数据:', data)
			this.selectedMusic = data
		})

		// 添加素材选择监听
		uni.$on('material-selected', (data) => {
			console.log('收到素材选择数据:', data)
			// 构建素材对象
			this.selectedMaterial = {
				name: data.displayText,
				materialPosition: data.materialPosition,
				materialOrigin: data.materialOrigin,
				userMaterialInfo: data.userMaterialInfo || []
			}
		})

		// 添加数字人更新监听
		uni.$on('update-dr-list', () => {
			console.log('收到更新数字人列表事件')
			this.get_drList()
		})

		let that = this
		//获取频道类
		getGroup().then(res => {
			this.listData = res.data
		})
		//获取工具类
		this.$api.get_authorization().then(res => {
			this.aiListData = res.rows
		})

		// 获取用户类型数据（提前获取，避免重复调用）
		getUserType().then(res => {
			this.userTypeData = res.data
			console.log('已获取用户类型数据:', this.userTypeData)
		})

		// 获取数字人列表
		this.get_drList()
		// 获取模板列表
		this.get_templateList()

		this.recorderManager = uni.getRecorderManager();
		//监听录制结束
		this.recorderManager.onStop(async (res) => {
			console.log(res)
			this.voicePath = res.tempFilePath;

			const fileExtension = res.tempFilePath.split('.').pop()

			uni.showLoading({
				title: '音频文件上传中'
			})
			this.oss_videoUrl = await uploadToOss({
				name: `tran-${this.user.userId}.${fileExtension}`,
				path: res.tempFilePath
			}, 'temp-audio')
			uni.hideLoading()

			console.log(this.oss_videoUrl, 'oss上传成功')

		})

		// 创建音频上下文
		this.innerAudioContext = uni.createInnerAudioContext({
      useWebAudioImplement: true
    })
		// 监听音频自然结束
		this.innerAudioContext.onEnded(() => {
			this.isPlaying = false
			this.videoing = false
			this.playingVoiceId = ''
		})

		if (this.$store.state.message_save) {
			this.$store.commit('setMessage_save', false)
			this.page_textarea = this.$store.state.aiMessage.content.join('\n')
			this.page_title = this.$store.state.aiMessage.title
		} else {
			// this.change_list({
			// 	name: '随机文案'
			// })
		}
	},
	onShow(options) {
		let data = {
			pageNum: 1,
			pageSize: 100
		}

		// 生成marks数组
		const marks = this.generateMarks(this.page_textarea);
		console.log(marks, 'marks');
		//获取音频列表
		getVoiceList(data).then(res => {
			this.music_list = res.rows
			console.log(res, '音频列表')
		})


		this.page_show = false
		this.model_bottom = false
		this.ai_model_bottom = false
		this.voice_model_bottom = false
		this.music_model_bottom = false
		// 是否来自其他页面

		// this.get_drList()
		this.get_templateList()


		// 检查是否有AI文案数据需要应用
		if (this.$store.state.message_save) {
			this.$store.commit('setMessage_save', false)
			// 应用AI文案数据到页面
			this.page_textarea = this.$store.state.aiMessage.content.join('\n')
			this.page_title = this.$store.state.aiMessage.title
			console.log('已应用AI文案数据到页面', this.page_textarea, this.page_title)
		}

	},
	onHide() {
		this.innerAudioContext.stop()
		this.playingVoiceId = ''
	},
	onUnload() {
		// 页面卸载时销毁音频,定时器
		if (this.innerAudioContext) {
			this.innerAudioContext.destroy()
		}
		if (this.page_Polling) {
			clearTimeout(this.page_Polling)
		}
		// 移除音乐选择监听
		uni.$off('selectMusic')
		// 移除素材选择监听
		uni.$off('material-selected')
		// 移除数字人列表更新监听
		uni.$off('update-dr-list')
	},

	// watch:{
	// 	page_index:{
	// 		handler(newVal){
	// 			if(newVal == 1){
	// 				this.templateItem = this.templatelist[0]
	// 				this.page_templateID = this.templateItem.id
	// 				this.templatelist[0].status = 'change'
	// 				this.change_numberid =  this.templateItem.outId
	// 				this.change_voiceId = this.templateItem.id
	// 			}
	// 		},
	// 		immediate:true
	// 	}
	// },

	computed: {
		...mapState(['musicData', 'user']),
		page_time() {
			let a = ''
			let b = ''
			if (this.time1 < 10) {
				{
					a = '0' + this.time1
				}
			} else {
				a = this.time1
			}
			if (this.time2 < 10) {
				b = '0' + this.time2
			} else {
				b = this.time2
			}
			return '00:' + a + ':' + b
		},
		// 是否显示数字人列表
		showDrList() {
			return this.dr_list.length > 0
		},
		showBackground() {
			if (this.page_index == 0) {
				return 'linear-gradient(129deg, #c7ffff, #f4f9f8, #f1ffe6)';
			}
			if (this.page_index == 1) {
				return 'linear-gradient(59deg, #fcffea, #fbfee9, #fff8f2)';
			}
			// 默认背景，防止未定义
			return 'linear-gradient(129deg, #c7ffff, #f4f9f8, #f1ffe6)';
		},

	},

	methods: {
		// 解析文本中的停顿标记并创建marks数组
		generateMarks(text) {
			if (!text) return [];

			// 存储最终结果
			const marks = [];

			// 创建一个临时文本用于处理
			let cleanText = '';

			// 匹配所有停顿标记的正则表达式
			const pauseRegex = /:(\d+\.\d+)s:/g;

			// 存储所有匹配到的停顿标记
			const matches = [];
			let match;

			// 获取所有匹配到的停顿标记
			while ((match = pauseRegex.exec(text)) !== null) {
				matches.push({
					fullMatch: match[0],  // 完整匹配
					time: parseFloat(match[1]), // 停顿时间
					startIndex: match.index, // 在原文本中的起始位置
					endIndex: match.index + match[0].length // 在原文本中的结束位置
				});
			}

			// 按起始位置排序
			matches.sort((a, b) => a.startIndex - b.startIndex);

			// 遍历文本，构建纯文本和计算停顿位置
			let lastEndIndex = 0;

			for (const pauseMatch of matches) {
				// 添加当前停顿标记前的文本到纯文本
				const textBeforePause = text.substring(lastEndIndex, pauseMatch.startIndex);
				cleanText += textBeforePause;

				// 记录此停顿标记在纯文本中的位置
				marks.push({
					type: 'break',
					index: cleanText.length - 1, // 在纯文本中的位置
					time: pauseMatch.time * 1000 // 转换为毫秒
				});

				// 更新lastEndIndex为当前停顿标记的结束位置
				lastEndIndex = pauseMatch.endIndex;
			}

			// 添加最后一个停顿标记后的文本
			cleanText += text.substring(lastEndIndex);

			// 输出调试信息
			console.log('原始文本:', text);
			console.log('纯文本:', cleanText);
			console.log('停顿标记:', marks);

			return marks;
		},
		// 判断返回 heightFix 还是 widthFix
		getImageMode(extra) {
			try {
				// 如果extra是字符串，尝试解析它
				const extraData = typeof extra === 'string' ? JSON.parse(extra) : extra;

				// 确保存在width和height属性
				if (extraData && extraData.width && extraData.height) {
					// 比较宽高，确定适合的缩放模式
					return extraData.width > extraData.height ? 'widthFix' : 'heightFix';
				}

				// 默认返回widthFix
				return 'widthFix';
			} catch (error) {
				console.error('解析extra数据出错:', error);
				// 发生错误时使用默认值
				return 'widthFix';
			}
		},
		toPowerRule() {
			uni.navigateTo({ url: '/subpkg/home_four/power_rule' })
		},
		handlePageIndexChange(index) {
			// 如果相同不切换
			if (this.page_index === index) return;

			this.page_index = index;
			// 可以在这里添加额外的过渡逻辑
		},
		get_templateList() {
			this.isTemplateListLoading = true;
			getTemplateList({
				pageNum: 1,
				pageSize: 100
			}).then(res => {
				this.isTemplateListLoading = false;
				this.templatelist = res.rows

				this.templatelist.map(item => {
					if (this.page_templateID == item.id) item.status = 'change'
				})
			}).catch(() => {
				this.isTemplateListLoading = false;
			})
		},
		down_del(id) {
			requests({
				url: `/user/template/${id}`,
				method: 'delete'
			}).then(res => {
				this.templateModel = false
				this.get_templateList()
				uni.showToast({
					title: '删除成功',
					icon: 'none'
				})
			})
		},



		//重新录制
		again_video() {

			this.video_model = true
			this.innerAudioContext.stop();

			this.message_show = false
			this.model_wxts = false
			this.clear_time()
			this.page_state = 1


		},

		clear_time() {
			this.time1 = 0
			this.time2 = 0
		},
		down_tab(e) {
			this.page_currend = e.index
		},


		//开始录音
		startRecord() {
			if (this.authorization) {
				this.recorderManager.start();
				this.page_state = 2
				this.clTime = setInterval(() => {
					this.time2++
					if (this.time2 >= 59) this.time1++
					console.log('正在录音')
				}, 1000)
			} else {
				uni.showToast({
					title: '请开启您的麦克风权限',
					icon: 'none'
				})
			}
		},
		//暂停录音
		endRecord() {
			this.recorderManager.pause();
			clearInterval(this.clTime)
			if (this.time2 < 5 && this.time1 < 1) {
				this.message_show = true
			} else {
				this.message_show = false
			}
			this.page_state = 3

		},
		//继续录制
		continue_video() {
			this.page_state = 2
			this.recorderManager.resume();
			this.clTime = setInterval(() => {
				this.time2++
				if (this.time2 >= 59) this.time1++
				console.log('正在录音')
			}, 1000)
		},

		// 替换停顿
		replace_pause(text) {
			if (!text) return '';

			let replace = text
				.replace(/:0.1s:/g, '<div class="pause_box"><img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/<EMAIL>" class="pause_box_img"></img>0.1s</div>')
				.replace(/:0.3s:/g, '<div class="pause_box"><img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/<EMAIL>" class="pause_box_img"></img>0.3s</div>')
				.replace(/:0.5s:/g, '<div class="pause_box"><img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/<EMAIL>" class="pause_box_img"></img>0.5s</div>')

			// 使用span并保留原始空格和换行
			return replace;
		},

		//确认提交
		save_video() {
			if (this.message_show) {
				uni.showToast({
					title: '录制时间不足',
					icon: 'none'
				})
				return
			}

			this.recorderManager.stop();
			this.video_model = false
			uni.showToast({
				title: '录制完成',
				icon: 'none'
			})
		},

		//播放录音
		playVoice() {
			if (this.voicePath) {
				this.innerAudioContext.src = this.voicePath;
				this.innerAudioContext.play();
			}
			this.videoing = true
		},
		//停止播放
		stop_video() {
			this.innerAudioContext.stop();
			this.videoing = false
		},

		handleCloseVipVersion() {
			this.page_show = false
		},

		choose_music() {
			uni.navigateTo({
				url: '/subpkg/index/get_radio/choose_music'
			})
		},

		chooseMaterial() {
			uni.navigateTo({
				url: '/subpkg/index/get_radio/selectMaterial',
				success: (res) => {
					// 如果已有素材数据，传递给selectMaterial页面
					if (this.selectedMaterial && this.selectedMaterial.userMaterialInfo) {
						res.eventChannel.emit('init-material-data', {
							materialPosition: this.selectedMaterial.materialPosition,
							materialOrigin: this.selectedMaterial.materialOrigin,
							typeText: this.selectedMaterial.typeText || 'AI智能配图',
							positionText: this.selectedMaterial.positionText || '画面底部',
							userMaterialInfo: this.selectedMaterial.userMaterialInfo || []
						});
					}
				}
			});
		},

		handleMyVideo() {
			uni.navigateTo({
				url: '/subpkg/index/get_radio/my_video'
			})
		},
		handleExample() {
			uni.navigateTo({
				url: '/subpkg/index/get_radio/example'
			})
		},
		//试听--获取声音id进行轮询
		start_music() {
			if (!this.change_voiceing.speakerId) {
				uni.showToast({
					title: '请选择声音',
					icon: 'none'
				})
				return
			}
			this.isPollingLoading = true // 开始局部loading

			// 生成marks数组
			const marks = this.generateMarks(this.page_textarea);
			postListenerAudio({
				speakerId: this.change_voiceing.speakerId,
				text: this.page_textarea.replace(/:(\d+\.\d+)s:/g, ''),
				speed: this.music_value.toFixed(1),
				marks: marks
			}).then(res => {
				this.polling_id = res.data
				this.play_polling()
			}).catch(() => {
				this.isPollingLoading = false
			})
		},
		//试听轮询
		play_polling() {
			if (this.page_Polling) {
				clearTimeout(this.page_Polling)
			}
			this.page_Polling = setTimeout(() => {
				requests({
					url: `/dr/listener/audio/${this.polling_id}`,
					method: 'get',
					data: {}
				}).then(res => {
					if (res.data.status == 'completed') {
						this.isPlaying = true
						this.innerAudioContext.src = res.data.materialUrl
						this.innerAudioContext.play();
						this.isPlaying = true
						this.isPollingLoading = false // 结束局部loading
					} else if (res.data.status == 'pending') {
						this.play_polling()
					} else {
						this.isPollingLoading = false // 结束局部loading
					}
				}).catch(() => {
					this.isPollingLoading = false // 结束局部loading
				})
			}, 5000)
		},

		//暂停
		stop_music() {
			this.isPlaying = false
			this.innerAudioContext.stop()
			this.isPollingLoading = false // 结束局部loading
		},

		post_video() {
			if (!this.change_numberid) {
				uni.showToast({
					title: '请选择数字人模版',
					icon: 'none'
				})
				return
			}
			if (!this.change_voiceing.speakerId) {
				uni.showToast({
					title: '请选择声音模版',
					icon: 'none'
				})
				return
			}
			if (this.page_textarea.length < 2) {
				uni.showToast({
					title: '文案字数不能小于两字符',
					icon: 'none'
				})
				return
			}

			// 验证文案内容
			if (!this.page_textarea || this.page_textarea.trim() === '' || this.page_textarea === '在此输入或粘贴您的文案...') {
				this.validationMessage = '请填写文案内容再生成视频'
				this.contentValidationModel = true
				return
			}

			if (this.page_textarea.length > 800) {
				uni.showToast({
					title: '文案字数超限，请限制在800字内',
					icon: 'none'
				})
				return
			}
			uni.showLoading({ title: '创建中...', mask: true })

			// this.page_textarea.indexOf('/')


			console.log(JSON.stringify({
				text: this.page_textarea,
				type: 'figure',
				figureId: this.change_numberid,
				speakerId: this.change_voiceing.speakerId,
				speed: this.music_value.toFixed(1)
			}), 'asdasdasd');

			// 生成marks数组
			const marks = this.generateMarks(this.page_textarea);
			console.log(marks, 'marks');

			postCreateVideo({
				text: this.page_textarea.replace(/:(\d+\.\d+)s:/g, ''), // 移除标记后的纯文本
				type: 'figure',
				figureId: this.change_numberid,
				speakerId: this.change_voiceing.speakerId,
				speed: this.music_value.toFixed(1),
				marks: marks // 添加marks数组
			}).then(res => {
				// uni.hideLoading()

				console.log(res, '成功')

				uni.navigateTo({
					url: '/subpkg/index/numbe_user/genVideoResult'
				})
			})
				.catch(error => {
					console.log('asdasdas');

					// uni.hideLoading()
				})
		},
		post_video_mb() {
			if (!this.change_voiceId) {
				uni.showToast({
					title: '请选择视频剪辑模版',
					icon: 'none'
				})
				return
			}
			if (!this.change_voiceing.speakerId) {
				uni.showToast({
					title: '请选择声音模版',
					icon: 'none'
				})
				return
			}

			// 验证标题和文案内容
			// if (!this.page_title || this.page_title.trim() === '' || this.page_title === '在此输入标题') {
			// 	this.validationMessage = '请填写视频标题再生成视频'
			// 	this.contentValidationModel = true
			// 	return
			// }
			if (this.page_title.length > 50) {
				this.validationMessage = '标题字数超限，请限制在50字内'
				this.contentValidationModel = true
				return
			}

			if (!this.page_textarea.replace(/:(\d+\.\d+)s:/g, '') || this.page_textarea.trim() === '' || this.page_textarea === '在此输入或粘贴您的文案...') {
				this.validationMessage = '请填写文案内容再生成视频'
				this.contentValidationModel = true
				return
			}

			if (this.page_textarea.length > 800) {
				uni.showToast({
					title: '文案字数超限，请限制在800字内',
					icon: 'none'
				})
				return
			}

			uni.showLoading({
				mask: true
			})

			// 准备请求参数
			const marks = this.generateMarks(this.page_textarea);

			const requestData = {
				title: this.page_title,
				text: this.page_textarea.replace(/:(\d+\.\d+)s:/g, ''), // 移除标记后的纯文本
				type: 'intelligent-clip',
				templateId: this.templateItem.templateId,
				speakerId: this.change_voiceing.speakerId,
				speed: this.music_value.toFixed(1),
				figureId: this.templateItem.figureId,
				metadata: JSON.parse(this.templateItem.metadata),
				marks: marks // 添加marks数组
			}
			// 如果选择了音乐，添加音乐信息
			if (this.selectedMusic) {
				requestData.backgroundMusicInfo = {
					musicUrl: this.selectedMusic.url
				}
			}

			// 如果选择了素材，添加素材信息
			if (this.selectedMaterial) {
				requestData.setMaterialPosition = this.selectedMaterial.materialPosition
				requestData.materialOrigin = this.selectedMaterial.materialOrigin

				// 如果有用户选择的素材，则添加到请求中
				if (this.selectedMaterial.userMaterialInfo && this.selectedMaterial.userMaterialInfo.length > 0) {
					requestData.userMaterialInfo = this.selectedMaterial.userMaterialInfo
				}
			}
			console.log(JSON.stringify(requestData), 'requestData');

			postCreateVideo(requestData).then(res => {
				uni.hideLoading()
				uni.navigateTo({
					url: '/subpkg/index/numbe_user/genVideoResult'
				})
			})
		},

		down_card(item) {
			this.dr_list.map(item => {
				if (item.status == 'change') item.status = 'success'
			})

			this.change_numberid = item.outId
			item.status = 'change'
			this.$forceUpdate()
		},

		down_card2(item) {
			this.change_voiceId = item.id


			this.templateItem = item

			if (this.page_templateID == item.id) this.templateModel = true
			this.page_templateID = item.id

			this.templatelist.map(item => {
				if (item.status == 'change') item.status = 'success'
			})
			this.change_numberid = item.outId
			item.status = 'change'
			this.$forceUpdate()
		},

		change_voice() {
			this.stopPreviewVoice()
			this.music_model_bottom = false
			this.page_voice_show = true
		},
		down_voice(item) {
			this.music_list.map(item => {
				item.state = 'false'
			})

			this.change_voiceing = item
			item.state = 'change'
			this.$forceUpdate()
		},
		handleAddMusic() {
			this.number_type = 'sound'
			if (this.userTypeData) {
				if (this.userTypeData.enableFastClone == '0') {
					this.page_show = true
					this.fastPowerRange = this.userTypeData.fastPowerRange
				} else {
					uni.navigateTo({
						url: '/subpkg/index/get_sound/index?type=pro'
					})
				}
			} else {
				// 如果还没有加载完用户数据，需要重新获取
				getUserType().then(res => {
					this.userTypeData = res.data
					if (res.data.enableFastClone == '0') {
						this.page_show = true
						this.fastPowerRange = res.data.fastPowerRange
					} else {
						uni.navigateTo({
							url: '/subpkg/index/get_sound/index?type=pro'
						})
					}
				})
			}
			this.music_model_bottom = false
		},

		edit_music() {
			this.edit_sound_show = true
			this.music_model_bottom = false
		},
		//选择频道文案
		handleListMessage(item) {
			uni.navigateTo({
				url: `/subpkg/index/get_radio/listMessage?id=${item.id}`,
				events: {
					get_content: (content) => {
						this.page_title = content.title
						this.page_textarea = content.content
					},
				}
			})
		},
		handleListAiMessage(item) {
			uni.navigateTo({
				url: '/subpkg/index/page_copy',
				success(res) {
					res.eventChannel.emit('get_message', item)
				}
			})
		},
		change_music() {
			this.music_model_bottom = true
		},
		change_list(item) {
			if (item.name == '随机文案') {
				//todo 默认长文案
				getRandomText({ longTextFlag: 1 }).then(res => {
					console.log(res)
					this.page_textarea = res.data.inputJson
					this.page_title = res.data.title
				})
			}
			if (item.name == '频道文案') {
				this.model_bottom = true
			}

			if (item.name == '录制音频') {

				this.video_model = true



				//上传音频，提取音频，录制音频
				// this.voice_model_bottom = true
			}

			if (item.name == 'AI文案') {
				this.ai_model_bottom = true
			}

		},
		get_drList() {
			this.isDrListLoading = true;
			this.$api.get_drList({
				pageNum: 1,
				pageSize: 100,
				status: 'success'
			}).then((res) => {
				this.isDrListLoading = false;

				if (res && res.rows) {
					if (this.dr_list && this.dr_list.length > 0) {
						if (!this.fromOther) {
							this.dr_list[0].status = 'change';
							this.change_numberid = this.dr_list[0].outId;
						}
					}
					this.$nextTick(() => {
						this.dr_list = res.rows
						console.log(this.dr_list.length, 'this.dr_list');

					})
				} else {
					this.dr_list = [];
				}
			}).catch(err => {
				this.isDrListLoading = false;
				console.error('获取数字人列表失败:', err);
				this.dr_list = [];
			});
		},
		close(value) {
			this.page_model = value
		},
		show_model(value) {
			this.page_show = false
			this.page_model = value
		},
		handleAddNumberUser() {
			if (this.userTypeData) {
				if (this.userTypeData.enableFastClone == '0') {
					this.page_show = true
					this.fastPowerRange = this.userTypeData.fastPowerRange
				} else {
					uni.navigateTo({
						url: '/subpkg/index/numbe_user/index?type=pro',
						events: {
							// 添加事件监听，在数字人添加完成后触发
							drAdded: () => {
								// 触发全局事件，更新数字人列表
								uni.$emit('update-dr-list')
							}
						}
					})
				}
			} else {
				// 如果还没有加载完用户数据，需要重新获取
				getUserType().then(res => {
					this.userTypeData = res.data
					if (res.data.enableFastClone == '0') {
						this.page_show = true
						this.fastPowerRange = res.data.fastPowerRange
					} else {
						uni.navigateTo({
							url: '/subpkg/index/numbe_user/index?type=pro',
							events: {
								// 添加事件监听，在数字人添加完成后触发
								drAdded: () => {
									// 触发全局事件，更新数字人列表
									uni.$emit('update-dr-list')
								}
							}
						})
					}
				})
			}
		},
		down_idnex(e) {
			this.page_index = e.index
		},

		handleEditText() {
			uni.navigateTo({
				url: `/subpkg/index/edit_text/index?type=${this.page_index}`,
				events: {
					get_content: (res) => {
						this.page_textarea = res.content
						this.page_title = res.title
					},
				},
				success: (res) => {
					res.eventChannel.emit('post_data', {
						data: {
							title: this.page_title,
							content: this.page_textarea
						}
					})
				}
			})
		},

		handleAddStencil() {
			uni.navigateTo({
				url: '/subpkg/index/get_radio/add_stencil',
				events: {
					get_video_data: (res) => {
						this.templateId = res.mb_id
						this.figureId = res.user_id
						this.metadata = res.data
					}
				}
			})
		},

		// 预览声音
		previewVoice(item) {
			// 如果有正在播放的音频，先停止它
			if (this.playingVoiceId) {
				this.innerAudioContext.stop();
			}

			this.playingVoiceId = item.outId;

			// 设置音频源并播放
			if (item.demoUrl) {
				this.innerAudioContext.src = item.demoUrl;
				this.innerAudioContext.play();

				// 监听播放结束事件
				this.innerAudioContext.onEnded(() => {
					this.playingVoiceId = '';
				});
			} else {
				uni.showToast({
					title: '音频不可用',
					icon: 'none'
				});
				this.playingVoiceId = '';
			}
		},

		// 停止预览
		stopPreviewVoice() {
			if (this.innerAudioContext) {
				this.innerAudioContext.stop();
			}
			this.playingVoiceId = '';
		},

		// 覆盖原来的修改和关闭方法，确保在关闭或切换页面时停止音频
		music_model_bottom_close() {
			this.stopPreviewVoice();
			this.music_model_bottom = false;
		},

		// 处理导航栏返回按钮点击
		handleNavBack() {
			// 可以在这里添加返回前的确认或其他逻辑
			console.log('返回按钮被点击');
		},
	}
}
</script>

<style scoped lang="scss">
.page {
	// padding: 0 4vw;
	height: 100vh;
	background: #f3f3f3;
	transition: background-image 0.4s ease, background-color 0.4s ease, background 0.4s ease;

	.top_btn_box {
		// border: 2rpx solid white;
		border-top-left-radius: 20rpx;
		border-top-right-radius: 20rpx;

		width: 100%;
		height: 100rpx;
		position: absolute;
		left: 0;
		top: 0;
		display: flex;

		.top_btn {
			font-size: 32rpx;
			width: 50%;
			text-align: center;
			height: 100rpx;
			line-height: 100rpx;
			border-top-left-radius: 20rpx;
			border-top-right-radius: 20rpx;
			position: relative;
			// transition: all 1s ease-in-out;
			color: #666;
		}

		.top_btn_active {
			border-top-left-radius: 20rpx;
			border-top-right-radius: 20rpx;
			background: white;
			color: #333;
			font-weight: 600;
		}

		.active-indicator {
			position: absolute;
			bottom: 10rpx;
			left: 50%;
			transform: translateX(-50%);
			width: 40rpx;
			height: 8rpx;
			background-color: #2cdd8a;
			border-radius: 4rpx;
			transition: all 0.3s ease-in-out;
		}
	}

	.box_text {
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 32rpx;
		font-weight: 500;
		// margin-top: 60rpx;

		.text_too {
			display: flex;
			align-items: center;
			padding: 20rpx 0;

			text {
				width: 30rpx;
				height: 30rpx;
				line-height: 30rpx;
				text-align: center;
				color: white;
				border-radius: 50%;
				background: black;
				margin-right: 1rpx;
			}
		}
	}

	.box_button {
		color: white;
		background: black;
		border-radius: 14rpx;
		height: 60rpx;
		line-height: 60rpx;
		padding: 0 30rpx;
		font-size: 28rpx;
		display: inline-block;
	}

	.content_none {
		display: flex;
		flex-direction: column;
		justify-content: space-around;
		align-items: center;
		margin-top: 200rpx;
		margin-bottom: 100rpx;

		.text_one {
			margin-bottom: 20rpx;
			font-size: 24rpx;
			color: #919191;
		}
	}

	.text_sound {
		font-size: 24rpx;
		font-weight: 500;
		color: #2cdd8a;
	}

	.card_list {
		display: flex;
		justify-content: flex-start;
		align-items: center;

		.list_card {
			padding: 12rpx 5rpx;
			border-radius: 20rpx;
			background: #F3F5F8;
			width: 120rpx;
			height: 100rpx;
			margin-right: 10rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-between;

			.text_scal {
				transform: scale(0.9);
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				gap: 20rpx;
			}
		}

		image {
			width: 44rpx;
			height: 44rpx;
		}

		font-size: 22rpx;
	}

	.tab-bottom {
		z-index: 9;
		// height: 98rpx;
		background: white;
		width: 100vw;
		position: fixed;
		bottom: 0rpx;
		left: 0rpx;
		// box-shadow: 0rpx -2rpx 10rpx 0rpx #b5b5b5;
		display: flex;
		justify-content: space-around;
		padding: 30rpx 20rpx 0rpx 20rpx;

		.card {
			image {
				width: 40rpx;
				height: 40rpx;
			}

			text-align: center;
			font-size: 24rpx;
		}

		.text_button {
			height: 80rpx;
			line-height: 80rpx;
			padding: 0 60rpx;
			background: #e8e8e8;
			border-radius: 20rpx;
			color: #c1c1c1;
		}

		.text_button_active {
			background: #000000;
			color: white;
		}
	}

	.page_scroll {
		// margin-top: 20rpx;
		white-space: nowrap;
		width: 100%;
		position: relative;

		.numberCard {
			display: inline-block;
			position: relative;
			width: 136rpx;
			height: 240rpx;
			margin-right: 20rpx;
			background-color: #000000;
			border-radius: 24rpx;
			overflow: hidden;
			box-sizing: border-box;
		}

		.image_ {
			width: 100%;
			height: 100%;
			// width: 148rpx;
			// height: 188rpx;


			// border: 6rpx solid white;
		}

		.image_active {
			border: 7rpx solid #26da86;
		}



		.right_bottom {
			height: 30rpx;
			position: absolute;
			top: 0rpx;
			left: 0rpx;
			z-index: 99;
		}

		.right_top {
			width: 30rpx;
			height: 30rpx;
			position: absolute;
			top: 5rpx;
			right: 5rpx;
		}
	}

}

.page_modelShow {
	padding: 40rpx;

	.add_music {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-around;
		margin-bottom: 40rpx;

		.add {
			font-size: 80rpx;
		}
	}

	.model_sound {
		border-radius: 20rpx;
	}

	.model_voicelist {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		font-weight: 600;
		color: #333333;
		margin-left: 24rpx;

		image {
			width: 80rpx;
			height: 80rpx;
			margin-right: 20rpx;
		}

	}

	.model_box {
		display: flex;
		align-items: center;
		background: #F3F5F8;
		border-radius: 20rpx;
		padding: 24rpx 48rpx;
		margin-top: 20rpx;

		border: 2rpx solid white;

		image {
			width: 65rpx;
			height: 65rpx;
		}

		.text_1 {
			flex: 1;
			padding: 0 20rpx;
			position: relative;
			top: -2rpx;
		}
	}

	.model-box-list {
		height: 40vh;
		overflow-y: scroll;
	}

	.model_box_active {
		display: flex;
		align-items: center;

		background: #F3F5F8;
		border-radius: 20rpx;
		padding: 24rpx 48rpx;
		margin-top: 20rpx;

		background: rgb(204, 252, 234);
		border: 2rpx solid #21BD74;

		image {
			width: 60rpx;
			height: 60rpx;
		}

		.text_1 {
			flex: 1;
			padding: 0 20rpx;
		}
	}

	.model_title {
		font-weight: 600;
		font-size: 32rpx;
		text-align: center;
		margin-bottom: 30rpx;
	}

	.preview-audio {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 10rpx;

		.preview-icon {
			width: 60rpx;
			height: 60rpx;
		}
	}
}

.page_text_title {
	font-weight: 600;
	font-size: 32rpx;
	margin: 20rpx 0;
	// height: 300rpx;
}

.page_textarea {
	font-size: 32rpx;
	line-height: 1.8;
	display: block;
	width: 100%;
	text-align: left;
	white-space: normal;
	word-wrap: break-word;
}

.page_textarea rich-text {
	display: block;
	line-height: 1.8;
}

.content-box {
	color: #1c1c1c;
	text-align: left;
}

.project_btn {
	margin-top: 100rpx;
	background: black;
}

.template_box {
	width: 80vw;
	border-radius: 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 5vw;

	.text_1 {
		width: 70vw;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 26rpx;
		margin-top: 40rpx;

		.title {
			font-weight: 600;
		}
	}
}

.bottomLine {
	padding: 20rpx 0;
	border-bottom: 1px solid #f3f3f3;
}

::v-deep .uni-textarea-textarea {
	lighting-color: 50rpx;
}

.skeleton-box {
	display: inline-block;
	margin-right: 10rpx;
	position: relative;
}

.skeleton-image {
	width: 10*15rpx;
	height: 16*15rpx;
	background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
	background-size: 200% 100%;
	animation: skeleton-loading 1.5s infinite;
	border-radius: 26rpx;
}

@keyframes skeleton-loading {
	0% {
		background-position: 200% 0;
	}

	100% {
		background-position: -200% 0;
	}
}
</style>
<style lang="scss">
.pause_box {
	background-color: #01F675;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	width: 90rpx;
	height: 36rpx;
	border-radius: 8rpx;
	gap: 8rpx;
	vertical-align: middle;
	margin: 0 4rpx;
	position: relative;
	top: -4rpx;
	/* 微调垂直位置 */
	font-size: 24rpx;
	color: #ffffff;

}

.pause_box_img {
	width: 24rpx;
	height: 24rpx;
	vertical-align: middle;
	filter: brightness(0) invert(1);
	/* 将黑色图标变成白色 */
}

.page_textarea_content {
	display: inline;
	white-space: normal;
	word-break: break-word;
}

.icon-wrapper {
	display: inline-block;
	width: 44rpx;
	height: 44rpx;
	overflow: hidden;
}

.icon-image {
	width: 44rpx;
	height: 44rpx;
	// transform: translateY(-100%);
	// filter: drop-shadow(0 44rpx #01f675);
}
</style>
