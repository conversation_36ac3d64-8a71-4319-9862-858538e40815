import { requests } from '../../utils/request.js'

// 批量生成视频
export const postBatchVideo = (data) => {
  return requests({
    url: '/dr/batch/video',
    method: 'post',
    data: data
  })
}

// 预估生成需要的算力
export const postBatchVideoPower = (data) => {
  return requests({
    url: '/dr/check/power',
    method: 'post',
    data: data
  })
}

// 我的作品合集列表
export const postMyBatchVideoList = (params) => {
  return requests({
    url: '/user/dr/batch-tasks',
    method: 'get',
    params: params
  })
}

// 批量视频详情
export const postBatchVideoDetail = (id) => {
  return requests({
    url: `/user/dr/batch-tasks/${id}`,
    method: 'get',
  })
}

// 批量视频删除
export const postBatchVideoDelete = (id) => {
  return requests({
    url: `/user/dr/batch-tasks/${id}`,
    method: 'delete',
  })
}
