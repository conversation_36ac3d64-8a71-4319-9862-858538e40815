// 根据项目实际路径导入request
import { requests } from '../../utils/request.js'

// 获取素材列表
export const getMaterialsList = (data) => {
  return requests({
    url: '/user/media-list',
    method: 'get',
    data: data
  })
}

// 删除素材
export const deleteMaterial = (id) => {
  return requests({
    url: `/user/media/${id}`,
    method: 'delete'
  })
}

// 上传素材
export const uploadMaterial = (data) => {
  return requests({
    url: '/user/media-upload',
    method: 'upload',
    filePath: data.file,
    name: 'file',
    formData: {
      name: data.name,
      type: data.type
    }
  })
}


// 保存素材
export const saveMaterial = (url) => {
  return requests({
    url: '/user/save-media',
    method: 'post',
    data:  {url}
  })
}

// 获取素材保存结果
export const getMeterialResult = (id) => {
  return requests({
    url: `/user/media-detail/${id}`,
    method: 'get'
  })
}

