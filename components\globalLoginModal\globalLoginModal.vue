<template>
	<view>
		<u-popup :show="loginModal.visible" @close="close" mode="bottom" :round="30">
			<view class="page_model">
				<view class="close-icon" @click="close">
					<u-icon name="close" size="22" color="#666"></u-icon>
				</view>
				<view class="box">
					<view class="quan" :class="{ 'checked': isAgreed }" @click="toggleAgree"></view>
					<view class="text_1">已阅读并同意<text @click="toAgreement('userAgreement')">用户协议</text>、<text
							@click="toAgreement('policy')">隐私政策</text></view>
				</view>
				<view class="project_btn1">
					<!-- 假按钮-getPhoneNumber无法被禁用 -->
					<button v-if="!isAgreed" class="login-btn" @click="showAgreeTip">
						一 键 登 录
					</button>
					<!-- 真按钮 -->
					<button v-else open-type="getPhoneNumber" @getphonenumber="getPhoneNumber" class="login-btn" :disabled="isLoading">
						<view class="login-btn-content">
							<!-- <u-loading-icon v-if="isLoading" mode="circle" color="#ffffff" size="28"></u-loading-icon> -->
							<text>{{ isLoading ? '登录中...' : '一 键 登 录' }}</text>
						</view>
					</button>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
	data() {
		return {
			isAgreed: false,
			isLoading: false
		}
	},
	computed: {
		...mapState('user', ['loginModal'])
	},
	methods: {
		...mapActions('user', ['hideLoginModal']),

		toAgreement(type) {
			uni.navigateTo({
				url: `/subpkg/home_four/agreement?type=${type}`
			});
		},
		toggleAgree() {
			this.isAgreed = !this.isAgreed;
		},
		showAgreeTip() {
			uni.showToast({
				title: '请先阅读并同意协议',
				icon: 'none'
			});
		},
		close() {
			// 如果正在加载中，不允许关闭弹窗
			if (this.isLoading) return;
			
			this.hideLoginModal();
			if (this.loginModal.cancelCallback) {
				this.loginModal.cancelCallback();
			}
		},
		//获取用户手机号
		getPhoneNumber(e) {
			// 设置loading状态
			this.isLoading = true;

			// 用户拒绝授权
			if (e.detail.errMsg === 'getPhoneNumber:fail user deny') {
				this.isLoading = false;
				uni.showToast({
					title: '用户取消授权',
					icon: 'none'
				});
				if (this.loginModal.cancelCallback) {
					this.loginModal.cancelCallback();
				}
				this.hideLoginModal();
				return;
			}

			// 获取手机号
			// 获取手机号授权 code
			if (e.detail.code) {
				wx.login({// 登录获取 openId 的 code
					success: (loginRes) => {
						if (loginRes.code) {
							// 调用后端接口，同时传两个 code：一个是手机号授权用的，一个是换 openId 的
							this.$api.post_login({
								appId: this.$appConfig.appInfo.appId,
								phoneCode: e.detail.code,     // 手机号临时凭证
								loginCode: loginRes.code      // wx.login 的 code（用于换 openId）
							}).then(res => {
								// 登录成功
								this.$store.dispatch('user/login', { token: res.token });

								// 获取用户信息
								this.$store.dispatch('user/getUserInfo').then(userData => {
									this.isLoading = false;
									if (this.loginModal.successCallback) {
										this.loginModal.successCallback(userData);
									}
									this.hideLoginModal();
								}).catch(err => {
									this.isLoading = false;
									if (this.loginModal.failCallback) {
										this.loginModal.failCallback(err);
									}
									this.hideLoginModal();
								});
							}).catch(err => {
								this.isLoading = false;
								if (this.loginModal.failCallback) {
									this.loginModal.failCallback(err);
								}
								this.hideLoginModal();
							});
						} else {
							this.isLoading = false;
							uni.showToast({ title: '登录失败', icon: 'none' });
						}
					},
					fail: () => {
						this.isLoading = false;
						uni.showToast({ title: '登录失败', icon: 'none' });
						this.hideLoginModal();
					}
				});
			} else {
				this.isLoading = false;
			}
		}
	}
}
</script>

<style scoped lang="scss">
.page_model {
	border-radius: 30rpx 30rpx 0 0;
	background: #fff;
	padding: 40rpx 40rpx 100rpx 40rpx;
	position: relative;

	.close-icon {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 50%;

		&:active {
			background-color: #f5f5f5;
		}
	}

	.box {
		display: flex;
		align-items: center;
		padding: 20rpx 0;

		.quan {
			width: 40rpx;
			height: 40rpx;
			border-radius: 50%;
			border: 2rpx solid #919191;
			position: relative;
			transition: all 0.2s ease;

			&.checked {
				background: #000;
				border-color: #000;

				&::after {
					content: '';
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%) rotate(45deg);
					width: 8rpx;
					height: 16rpx;
					border: 4rpx solid #fff;
					border-left: 0;
					border-top: 0;
				}
			}

			&:active {
				transform: scale(0.95);
			}
		}

		.text_1 {
			margin-left: 20rpx;
			color: #333;
			font-size: 28rpx;

			text {
				color: #333;
				position: relative;

				&::after {
					content: '';
					position: absolute;
					left: 0;
					bottom: -2rpx;
					width: 100%;
					height: 1rpx;
					background: #333;
				}

				&:active {
					opacity: 0.8;
				}
			}
		}
	}

	.project_btn1 {
		margin: auto;
		margin-top: 50rpx;
		font-size: 32rpx;
		padding: 0;
		width: 100%;

		.login-btn {
			width: 100%;
			height: 88rpx;
			line-height: 88rpx;
			background: #000;
			color: #fff;
			font-size: 32rpx;
			font-weight: 500;
			border-radius: 44rpx;
			box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
			transition: all 0.2s ease;

			&:active {
				transform: translateY(2rpx);
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
				opacity: 0.9;
			}
			
			&:disabled {
				opacity: 0.8;
			}
			
			.login-btn-content {
				display: flex;
				justify-content: center;
				align-items: center;
				gap: 10rpx;
				height: 100%;
			}
		}
	}
}
</style>