<template>
  <view class="stepList-container">
    <view class="custom-stepList">
      <view v-for="(item, index) in stepList" :key="index" class="custom-step-item"
        @click="$emit('handleStepClick', index)">
        <!-- 步骤图标 -->
        <view class="custom-step-icon">
          <image v-if="index < current" src="/static/batch_video/编辑完成.png" class="step-icon" />
          <image v-else-if="index === current" src="/static/batch_video/正在编辑.png" class="step-icon" />
          <image v-else style="width: 32rpx; height: 32rpx;" src="/static/batch_video/待编辑.png" class="step-icon" />
        </view>

        <!-- 步骤文本 -->
        <view class="custom-step-text" :style="{ color: index <= current ? activeColor : inactiveColor }">
          {{ item.title }}
        </view>
        <!-- 步骤线条 -->
        <view v-if="index < stepList.length - 1" class="custom-step-line"
          :class="{ 'dashed-line': isDashedLine(index), 'solid-line': !isDashedLine(index) }"
          :style="[getLineStyle(index), getLinePosition()]">
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'StepsComponent',
  data() {
    return {
      stepList: [
        { title: '选模板' },
        { title: '选文案' },
        { title: '选素材' },
        { title: '选音乐' },
        { title: '批量生成' }
      ]
    }
  },
  props: {
    current: {
      type: Number,
      default: 0
    },
    isDashed: {
      type: Boolean,
      default: false
    },
    activeColor: {
      type: String,
      default: '#07C160'
    },
    inactiveColor: {
      type: String,
      default: '#C8C9CC'
    },
    spacing: {
      type: Number,
      default: 30
    }
  },
  methods: {
    getLineStyle(index) {
      console.log(index, this.current, 'index, this.current');

      const isActive = index <= this.current;
      return {
        borderColor: isActive ? this.activeColor : this.inactiveColor,
        // backgroundColor: isActive ? this.activeColor : this.inactiveColor
      };
    },
    getLinePosition() {
      return {
        width: `calc(100% - ${this.spacing}rpx)`,
        right: `calc(-50% + ${this.spacing / 2}rpx)`
      };
    },
    // 判断显示虚线实现
    isDashedLine(index) {
      return index !== this.current;
    }
  }
}
</script>

<style lang="scss" scoped>
.stepList-container {
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
  // background-color: #FFFFFF;
  border-radius: 8rpx;
}

.custom-stepList {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  position: relative;
}

.custom-step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  z-index: 1;
}

.custom-step-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;

  .step-icon {
    width: 40rpx;
    height: 40rpx;
  }
}

.custom-step-text {
  font-size: 24rpx;
  margin-top: 10rpx;
  text-align: center;
}

.custom-step-line {
  position: absolute;
  width: 100%;
  // height: 2rpx;
  top: 20rpx;
  z-index: 0;
}

.solid-line {
  border-top-style: solid;
  height: 0;
  border-top-width: 2rpx;
}

.dashed-line {
  height: 0;
  border-top-width: 2rpx;
  border-top-style: dashed;
}
</style>