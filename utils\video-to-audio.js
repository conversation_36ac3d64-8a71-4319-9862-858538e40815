import { chooseAndUploadVideo, uploadToOss } from "./ali-oss";
import { createVideoToAudioTask, getVideoToAudioTaskResult } from "../api/oss/oss";
import store from '../store'

/**
 * 本地视频提取音频
 * @returns {Promise<String>}
 */
export const handleVideoToAudio = async () => {
    try {
        const videoUrl = await chooseAndUploadVideo({
            dir: 'temp-video', // 临时上传目录
            maxSize: 150 * 1024 * 1024, // 最大150MB
            maxDuration: 60 // 最大120秒,
        });

        const audioUrl = await convertVideoToAudio_too(videoUrl, (progress, total) => {
            console.log(`转换进度：第 ${progress} 次轮询`);
        });

        return audioUrl;
    } catch (err) {
        // 出错时隐藏loading
        uni.hideLoading();
        uni.showToast({ title: err.message || '转换失败', icon: 'none' });
        throw err; // 继续抛出错误，让调用者处理
    }
};




export const handleVideoFileToAudio_too = (videoUrl) => {
    return new Promise(async (resolve, reject) => {
        try {
            uni.downloadFile({
                url: videoUrl,
                success: async (res) => {
                    try {
                        console.log("下载完成：" + JSON.stringify(res));
                        // 提取文件后缀
                        const fileExtension = res.tempFilePath.split('.').pop(); // 这将得到"mp4"
                        if (res.statusCode === 200) {
                            const url = await uploadToOss({
                                name: `tran-${store.state.user.userInfo.userId}.${fileExtension}`,
                                path: res.tempFilePath
                            }, 'temp-tran-video');
                            
                            const audioUrl = await convertVideoToAudio_too(url, (progress, total) => {
                                console.log(`转换进度：第 ${progress} 次轮询`);
                                // 不更新loading文字
                            });

                            // 成功时不隐藏loading，由调用方负责隐藏
                            console.log('音频地址：', audioUrl);
                            resolve(audioUrl);
                        } else {
                            reject(new Error(`下载失败，状态码: ${res.statusCode}`));
                        }
                    } catch (error) {
                        reject(error);
                    }
                },
                fail: (err) => {
                    reject(new Error(`下载文件失败: ${err.errMsg}`));
                }
            });
        } catch (error) {
            reject(error);
        }
    });
};


/**
 * 视频文件路径提取音频
 * @param videoUrl
 * @returns {Promise<void>}
 */
export const handleVideoFileToAudio = async (videoUrl) => {
    // 显示上传进度
    uni.showLoading({
        title: '解码中...'
    });
    uni.downloadFile({
        url: videoUrl,
        success: async (res) => {
            console.log("下载完成：" + JSON.stringify(res))
            // 提取文件后缀
            const fileExtension = res.tempFilePath.split('.').pop(); // 这将得到"mp4"
            if (res.statusCode === 200) {
                const url = await uploadToOss({ name: `tran-${store.state.user.userInfo.userId}.${fileExtension}`, path: res.tempFilePath }, 'temp-tran-video')
                const audioUrl = await convertVideoToAudio(url, (progress, total) => {
                    console.log(`转换进度：第 ${progress} 次轮询`);
                });
                console.log('音频地址：', audioUrl);
            }
        }
    });
}

/**
 * 视频文件转音频
 * @param {String} videoUrl - 已上传到 OSS 的视频地址
 * @param {Function} onProgress - 可选，轮询回调
 * @param {Number} interval - 可选，轮询间隔（毫秒）
 * @param {Number} maxRetries - 可选，最大重试次数
 * @returns {Promise<String>} 转换后音频地址
 */
export const convertVideoToAudio = async (
    videoUrl,
    onProgress = null,
    interval = 3000,
    maxRetries = 30
) => {
    try {

        // 显示上传进度
        uni.showLoading({
            title: '转码中...'
        });


        // 1. 发起转码请求
        const startResponse = await createVideoToAudioTask({ videoUrl })
        console.log("转码结果" + JSON.stringify(startResponse))
        if (startResponse.code !== 200 || !startResponse.data) {
            throw new Error('视频转音频任务创建失败');
        }

        const taskId = startResponse.data;

        // 2. 开始轮询后端任务状态
        let attempts = 0;

        return await new Promise((resolve, reject) => {
            const timer = setInterval(async () => {
                attempts++;
                if (attempts > maxRetries) {
                    clearInterval(timer);
                    return reject(new Error('音频转换超时'));
                }

                if (onProgress) onProgress(attempts, maxRetries);

                try {
                    const pollRes = await getVideoToAudioTaskResult({ taskId, videoUrl })
                    if (pollRes.code === 200 && pollRes.data) {
                        const audioUrl = pollRes.data;
                        clearInterval(timer);
                        resolve(audioUrl);
                    } else if (pollRes.code === 200) { // 任务未完成，进行中
                        console.log('转换进行中...');
                    } else {
                        console.warn('轮询失败');
                        clearInterval(timer);
                        reject(new Error(errorMsg || '音频转换失败'));
                    }
                } catch (pollError) {
                    clearInterval(timer);
                    console.error('轮询错误:', pollError);
                    reject(pollError);
                }
            }, interval);
        });
    } catch (error) {
        console.error('convertVideoToAudio 失败:', error);
        throw error;
    }
};
export const convertVideoToAudio_too = async (
    videoUrl,
    onProgress = null,
    interval = 3000,
    maxRetries = 30
) => {
    try {
        // 1. 发起转码请求
        const startResponse = await createVideoToAudioTask({ videoUrl })
        console.log("转码结果" + JSON.stringify(startResponse))
        if (startResponse.code !== 200 || !startResponse.data) {
            throw new Error('视频转音频任务创建失败');
        }

        const taskId = startResponse.data;

        
        let attempts = 0;

        return await new Promise((resolve, reject) => {
            const timer = setInterval(async () => {
                attempts++;
                if (attempts > maxRetries) {
                    clearInterval(timer);
                    uni.hideLoading(); // 确保超时时隐藏loading
                    return reject(new Error('音频转换超时 请稍后重试'));
                }

                if (onProgress) onProgress(attempts, maxRetries);

                try {
                    const pollRes = await getVideoToAudioTaskResult({ taskId, videoUrl })
                    if (pollRes.code === 200 && pollRes.data) {
                        const audioUrl = pollRes.data;
                        clearInterval(timer);
                        resolve(audioUrl);
                    } else if (pollRes.code === 200) { // 任务未完成，进行中
                        console.log('转换进行中...');
                    } else {
                        console.warn('轮询失败');
                        clearInterval(timer);
                        reject(new Error(pollRes.msg || '音频转换失败'));
                    }
                } catch (pollError) {
                    clearInterval(timer);
                    uni.hideLoading();
                    console.error('轮询错误:', pollError);
                    reject(pollError);
                }
            }, interval);
        });
    } catch (error) {
        uni.hideLoading();
        console.error('convertVideoToAudio 失败:', error);
        throw error;
    }
};
