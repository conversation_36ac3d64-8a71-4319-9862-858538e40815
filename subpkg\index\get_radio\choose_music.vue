<template>
	<view class="page">


		<view class="tab_box">
			<view v-for="(item, index) in music_list" :class="page_current == index ? 'active_tab' : 'tab_text'"
				@click="down_tabs(item, index)">
				{{ item.dictLabel }}
			</view>
		</view>


		<view @click="get_music" class="extract-audio-btn" style="margin-top: 40rpx;">
			<view class="diplay_center">
				<view class="diplay_center">
					<image src="/static/home/<USER>/get_music.png"></image>
					<view style="color: #1e1f27;margin-left: 16rpx;font-family: ChillRoundGothic_Medium,serif;font-size: 27rpx">提取视频中音频</view>
				</view>
				<image src="/static/home/<USER>/change_right.png"></image>
			</view>
		</view>

		<view class="music_sound_box" v-if="false">
			<view class="diplay_center">
				<view class="diplay_center">
					<image src="/static/home/<USER>/laba.png"></image>
					<view class="title">当前音量:</view>
				</view>
				<view class="text">{{ music_value }}%</view>
			</view>
			<view style="margin: 48rpx 0 32rpx 0;color: #333333;font-weight: 600;">音量调节</view>
			<view style="display: flex;justify-content: space-between;align-items: center;font-size: 24rpx;color: #666666;">
				<view>最小</view>
				<view style="width:500rpx">
					<u-slider v-model="music_value" activeColor="#21BD74" step="1" min="1" max="100"
						blockColor="#21BD74"></u-slider>
				</view>
				<view>最大</view>
			</view>
		</view>


		<scroll-view scroll-y="true" class="scroll-Y" :style="{ height: scrollHeight + 'px' }">
			<view class="music_title">背景音乐</view>
			<view style="margin-top: 50rpx;" v-if="page_show">
				<u-skeleton rows="3" title :loading="page_show"></u-skeleton>
			</view>

			<view v-if="music_message.length > 0 && !fromSelectMusic" class="music_box" @click="change_music({ id: '-1', name: '不使用背景音乐' })"
				:class="page_item.id == '-1' ? 'music_box_active' : ''">
				<view class="music_view">
					<image src="/static/home/<USER>/get_radio_bf_none.png"></image>
					<view>不使用背景音乐</view>
				</view>
				<image v-if="page_item.id == '-1'" src="/static/index/changed.png" style="width: 40rpx;height: 40rpx;"></image>
			</view>

			<view v-for="item in music_message" class="music_box" 
				:class="{ 'music_box_active': fromSelectMusic ? selectedMusics.includes(item.id) : page_item.id == item.id }"
				@click="fromSelectMusic ? toggleSelectMusic(item) : change_music(item)">
				<view class="music_view">
<!--					<image-->
<!--						:src="page_img_statr + 'project1_home4_music_playing.gif'"></image>-->
         <view  v-if="page_music_id == item.id" @click.stop="stop_music(item)" style="width: 80rpx;height: 80rpx">
           <wave-loading color="#02ef95" barWidth="5rpx" barSpacing="9rpx" />
         </view>
					<image v-else @click.stop="play_music(item)" src="/static/home/<USER>/get_radio_bf.png"></image>
					<view class="ellipsis">{{ item.name }}</view>
				</view>
				<view class="action-buttons">
					<!-- 单选模式下的选中图标 -->
					<image v-if="!fromSelectMusic && page_item.id == item.id" src="/static/index/changed.png" style="width: 40rpx;height: 40rpx;"></image>
					
					<!-- 多选模式下的选中图标 -->
					<image v-if="fromSelectMusic && selectedMusics.includes(item.id)" src="/static/index/changed.png" style="width: 40rpx;height: 40rpx;"></image>
				</view>
			</view>

			<view v-if="music_message.length <= 0">
				<noneMessage type="music" content="暂无数据" pad_top="50rpx">
				</noneMessage>
			</view>


			<!-- <view>暂无数据</view> -->
		</scroll-view>

		<projectModel v-if="edit_model" title='修改音乐名称' @btn_close="edit_model = false" @btn_save="btn_save">
			<view style="margin-bottom: 20rpx;">
				<u--input placeholder="请填写名称" border="surround" v-model="page_name"></u--input>
			</view>
		</projectModel>
		<projectModel v-if="del_model" title='删除音乐' content="是否确定删除当前所选音乐" @btn_close="del_model = false"
			@btn_save="del_music">
		</projectModel>

		<!-- 统一的底部按钮区域 -->
		<view class="bottom-buttons">
			<!-- 多选模式下显示已选数量 -->
			<view class="selected-count" v-if="fromSelectMusic">
				<view class="count-text">已选择: {{ selectedMusics.length }} 个音乐</view>
			</view>
			
			<!-- 单选模式下才显示编辑按钮组 -->
			<view class="button-group" v-if="!fromSelectMusic">
				<view class="action-button rename-button" @click="handleRenameMusic(page_item)"
					:class="{ 'disabled-button': page_current > 0 || !page_item || page_item.id === '-1' }">

					<text>重命名</text>
				</view>
				<view class="action-button delete-button" @click="handleDeleteMusic(page_item)"
					:class="{ 'disabled-button': page_current > 0 || !page_item || page_item.id === '-1' }">

					<text>删除</text>
				</view>
			</view>
			
			<view class="confirm-button" id="save-btn" @click="save">确定</view>
		</view>
    <view v-if="uploading" class="project_model_1" style="background: rgba(0, 0, 0, 0.9); display: flex; justify-content: center; align-items: center;">
      <view class="project_model_sub" style="background: none;color: white;">
        <u-loading-icon mode="circle" size="80rpx"></u-loading-icon>
        <view style="text-align: center;margin-top: 30rpx;">音频提取中...</view>
        <view style="text-align: center;margin: 20rpx 0;font-size: 24rpx;opacity: 0.8;">请勿熄屏或切换应用</view>
      </view>
    </view>
	</view>
</template>

<script>
import {
	chooseAndUploadAudio
} from '../../../utils/ali-oss.js'

import {
	handleVideoToAudio
} from '../../../utils/video-to-audio.js'
import {
	getMusicClass,
	getMusicList,
	postMusic,
	putMusic,
	deleteMusic
} from '../../../api/music/music.js'

import {
	requests
} from '../../../utils/request.js'

import { page_img_statr } from '../../../api/getData.js'
import WaveLoading from "@/components/wave-loader/index.vue";
export default {
  components: {WaveLoading},
	data() {
		return {
      uploading:false,
			del_model: false,
			edit_model: false,
			page_show: true,
			music_list: [],
			page_current: 0,
			music_message: '',
			modelShow: true,

			page_item: '',
			page_name: '',

			music_value: 1,

			innerAudioContext: null,

			page_music_id: '',
			page_img_statr: '',
			bottomHight: 0,

			// 滚动列表高度
			scrollHeight: 0,
			
			// 是否从selectMusic页面来
			fromSelectMusic: false,

			// 多选模式下已选择的音乐ID数组
			selectedMusics: []
		}
	},
	onLoad() {
		// 检查是否从selectMusic页面跳转来的
		const eventChannel = this.getOpenerEventChannel();
		if (eventChannel) {
			eventChannel.on('fromSelectMusic', (data) => {
				if (data && data.fromSelectMusic) {
					this.fromSelectMusic = true;
				}
			});
		}
		
		getMusicClass().then(res => {
			this.music_list = [{
				dictLabel: '我的',
				dictSort: '-1'
			}, ...res.data]
		})
		this.down_tabs()
		this.page_img_statr = page_img_statr
		// 创建音频上下文
		this.innerAudioContext = uni.createInnerAudioContext({
      useWebAudioImplement: true
    })
		// 监听音频自然结束
		this.innerAudioContext.onEnded(() => {
			this.page_music_id = ''
		})
		let app = uni.getSystemInfoSync();
		this.bottomHight = app.safeAreaInsets.bottom

		// 初始计算高度
		this.calculateScrollHeight()

		// 添加窗口尺寸变化监听
		uni.onWindowResize(this.handleResize)
	},
	onReady() {
		// 组件挂载完成后重新计算一次高度
		this.calculateScrollHeight()
	},
	onShow() {
		// 页面显示时重新计算高度，以适应可能的方向变化
		this.calculateScrollHeight()
	},
	onHide() {
		this.innerAudioContext.stop()
		clearInterval(this.page_setInterval)
	},
	onUnload() {
		// 页面卸载时销毁音频
		if (this.innerAudioContext) {
			this.innerAudioContext.destroy()
		}

		// 移除窗口尺寸变化监听
		uni.offWindowResize(this.handleResize)
	},

	methods: {
		// 在多选模式下切换选择音乐
		toggleSelectMusic(item) {
			const index = this.selectedMusics.indexOf(item.id);
			if (index !== -1) {
				// 如果已选中，则移除
				this.selectedMusics.splice(index, 1);
			} else {
				// 否则添加到已选中列表
				this.selectedMusics.push(item.id);
			}
		},
		
		play_music(item) {
			this.page_music_id = item.id
			this.innerAudioContext.src = item.fileUrl;
			this.innerAudioContext.play();
		},
		stop_music() {
			this.page_music_id = ''
			this.innerAudioContext.stop();
		},

		// 处理重命名音乐
		handleRenameMusic(item) {
			// 如果不是"我的"类型或没有选择有效音乐，则不执行操作
			if (this.page_current > 0 || !item || !item.id || item.id === '-1') {
				uni.showToast({
					title: '请在我的音乐中选择有效音乐',
					icon: 'none'
				})
				return
			}

			this.page_item = item;
			this.page_name = item.name;
			this.edit_model = true;
		},

		// 处理删除音乐
		handleDeleteMusic(item) {
			// 如果不是"我的"类型或没有选择有效音乐，则不执行操作
			if (this.page_current > 0 || !item || !item.id || item.id === '-1') {
				uni.showToast({
					title: '请在我的音乐中选择有效音乐',
					icon: 'none'
				})
				return
			}

			this.page_item = item;
			this.del_model = true;
		},

		del_music() {
			if (!this.page_item || !this.page_item.id) {
				uni.showToast({
					title: '请选择要删除的音乐',
					icon: 'none'
				})
				return
			}

			deleteMusic(this.page_item.id).then(res => {
				uni.showToast({
					title: '删除成功',
					icon: 'success'
				})
				this.del_model = false

				// 如果删除的是当前选中的音乐，则清空选择
				if (this.page_item.id === this.page_item.id) {
					this.page_item = '';
				}

				// 重新加载音乐列表
				this.down_tabs({
					dictSort: '-1'
				}, 0)
			}).catch(err => {
				uni.showToast({
					title: '删除失败',
					icon: 'none'
				})
			})
		},

		btn_save() {
			if (!this.page_item || !this.page_item.id || !this.page_name.trim()) {
				uni.showToast({
					title: '请输入有效名称',
					icon: 'none'
				})
				return
			}

			putMusic({
				id: this.page_item.id,
				name: this.page_name.trim(),
			}).then(res => {
				uni.showToast({
					title: '重命名成功',
					icon: 'success'
				})
				this.edit_model = false

				// 更新当前选中音乐的名称
				if (this.page_item && this.page_item.id === this.page_item.id) {
					this.page_item.name = this.page_name.trim();
				}

				// 重新加载音乐列表
				this.down_tabs({
					dictSort: '-1'
				}, 0)
			}).catch(err => {
				uni.showToast({
					title: '重命名失败',
					icon: 'none'
				})
			})
		},

		async upload_music() {
			let fileUrl = await chooseAndUploadAudio()

			const now = new Date();
			const hours = now.getHours(); // 时 (0-23)
			const minutes = now.getMinutes(); // 分 (0-59)
			const seconds = now.getSeconds(); // 秒 (0-59)
			const formattedTime =
				`${hours.toString().padStart(2, '0')}${minutes.toString().padStart(2, '0')}${seconds.toString().padStart(2, '0')}`;

			postMusic({
				name: `默认名称${formattedTime}`,
				fileUrl,
			}).then(res => {
				this.down_tabs()
			})
		},

		async get_music() {
			if (this.page_current) {
				this.page_current = 0
				this.down_tabs()
			}

			try {
        this.uploading=true
				let fileUrl = await handleVideoToAudio()
				// 只有在获取到fileUrl后再处理后续操作
				if (fileUrl) {
					const now = new Date();
					const hours = now.getHours(); // 时 (0-23)
					const minutes = now.getMinutes(); // 分 (0-59)
					const seconds = now.getSeconds(); // 秒 (0-59)
					const formattedTime =
						`${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

					// 保存到音乐库
					await postMusic({
						name: `默认名称${formattedTime}`,
						fileUrl,
					})
          this.uploading=false
					// 显示成功提示
					uni.showToast({
						title: '音频提取成功',
						icon: 'none'
					})

					// 刷新音乐列表
					this.down_tabs()
				}
			} catch (error) {
        uni.showToast({title: '音频提取失败,请检查视频文件后重试', icon: 'none'})
				console.error('提取音频失败:', error)
			} finally {
        this.uploading=false
				// 确保loading被关闭
				uni.hideLoading()
			}
		},

		save() {
			// 判断是否来自selectMusic页面 - 多选模式
			if (this.fromSelectMusic) {
				if (this.selectedMusics.length === 0) {
					uni.showToast({
						title: '请至少选择一个音乐',
						icon: 'none'
					});
					return;
				}
				
				// 获取所有选中的音乐数据
				const selectedMusicData = this.selectedMusics.map(id => {
					const music = this.music_message.find(item => item.id === id);
					if (music) {
						return {
							id: music.id,
							name: music.name,
							url: music.fileUrl,
							volume: this.music_value
						};
					}
					return null;
				}).filter(item => item !== null);
				
				// 发送多个音乐数据
				uni.$emit('selectMusics', selectedMusicData);
				uni.navigateBack();
			} else {
				// 原有单选逻辑
			if (!this.page_item.id) {
				uni.showToast({
					title: '请选择音乐',
					icon: 'none'
					});
					return;
			}
				
			const musicData = {
				name: this.page_item.name,
				id: this.page_item.id,
				url: this.page_item.fileUrl,
				volume: this.music_value
				};

				// 发送单个音乐数据
				uni.$emit('selectMusic', musicData);
				uni.navigateBack();
			}
		},

		change_music(item) {
			this.page_item = item
			this.page_name = item.name
		},

		down_tabs(item, index) {
			this.page_current = index || 0
			let categoryId = ''
			if (item) {
				categoryId = item.dictSort
			}
			else {
				categoryId = -1
			}

			// 切换类型时，重置当前选中的音乐
			this.page_item = ''
			this.page_name = ''
			
			// 多选模式下重置选中项
			if (this.fromSelectMusic) {
				this.selectedMusics = [];
			}

			// 停止当前播放的音乐
			if (this.page_music_id) {
				this.innerAudioContext.stop()
				this.page_music_id = ''
			}

			getMusicList({
				categoryId,
				pageNum: 1,
				pageSize: 100
			}).then(res => {
				this.music_message = res.rows
				this.page_show = false
			})
		},

		// 计算音乐列表的高度
		calculateScrollHeight() {
			// 使用uni.createSelectorQuery()获取各个元素的高度
			const query = uni.createSelectorQuery().in(this);

			// 等待DOM渲染完成
			setTimeout(() => {
				// 获取页面整体信息
				const systemInfo = uni.getSystemInfoSync();
				const windowHeight = systemInfo.windowHeight;

				// 获取各个元素的高度
				query.select('.tab_box').boundingClientRect();
				query.select('.extract-audio-btn').boundingClientRect();
				query.select('.page').boundingClientRect();
				query.select('.bottom-buttons').boundingClientRect();

				query.exec((res) => {
					if (res && res[0] && res[1] && res[2] && res[3]) {
						// 获取标签栏高度
						const tabHeight = res[0].height || 0;
						// 获取提取视频音频按钮高度
						const extractBtnHeight = res[1].height || 0;
						// 获取页面整体高度
						const pageHeight = res[2].height || 0;
						// 获取底部按钮区域高度
						const btnAreaHeight = res[3].height || 0;

						// 底部安全区域高度
						const safeAreaBottom = this.bottomHight || 0;

						// 顶部和底部内边距估算 (32rpx + 32rpx)
						const padding = uni.upx2px(64);

						// 上下边距总和
						const marginHeight = uni.upx2px(130);

						// 计算滚动区域高度
						// 页面高度 - 标签栏高度 - 提取视频音频按钮高度 - 底部按钮区域高度 - 安全区域高度 - 边距高度
						this.scrollHeight = pageHeight - tabHeight - extractBtnHeight - btnAreaHeight - safeAreaBottom - marginHeight - padding;
					}
				});
			}, 100);
		},

		// 处理窗口尺寸变化
		handleResize() {
			// 重新计算高度
			this.calculateScrollHeight();
		}
	}
}
</script>

<style scoped lang="scss">
/* 全局变量定义 - 便于主题维护 */
$primary-color: black; // 主色调：清新绿色
$primary-dark: #1AA663; // 主色调加深
$primary-light: #E8F8F0; // 主色调浅色背景
$accent-color: #f9bbbb; // 强调色：删除按钮红色
$text-primary: #333333; // 主要文字色
$text-secondary: #666666; // 次要文字色
$text-tertiary: #999999; //  tertiary文字色
$bg-primary: #F5F7FA; // 页面背景色
$bg-secondary: #FFFFFF; // 卡片背景色
$border-color: #EEEEEE; // 边框色
$spacing-xs: 10rpx;
$spacing-sm: 20rpx;
$spacing-md: 30rpx;
$spacing-lg: 40rpx;
$radius-sm: 12rpx;
$radius-md: 20rpx;
$radius-lg: 24rpx;
$shadow-sm: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
$shadow-md: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
$transition-default: all 0.3s ease;

.page {
	padding: $spacing-md;
	height: 100vh;
	background: $bg-primary;
	
	// 添加选中数量样式
	.selected-count {
	background-color: $primary-light;
	border-radius: $radius-sm;
	height: 64rpx;
	padding: 0 $spacing-md;
	display: flex;
	align-items: center;
	margin-bottom: $spacing-sm;
	transition: $transition-default;

	.count-text {
		color: $text-secondary;
		font-size: 28rpx;
	}
}

	.scroll-Y {
	position: relative;
	background: $bg-secondary;
	margin-top: $spacing-lg;
	border-radius: $radius-lg;
	padding: $spacing-md;
	box-sizing: border-box;
	overflow: hidden;
	box-shadow: $shadow-sm;
	transition: $transition-default;

	&:hover {
		box-shadow: $shadow-md;
	}

		.music_title {
			font-weight: 600;
			font-size: 32rpx;
			color: #333333;
			margin-bottom: 24rpx;
		}


		.btn_box {
			position: absolute;
			bottom: 0;
			right: 0;
			display: flex;
			text-align: center;
			font-size: 24rpx;
			font-weight: 600;

			.btn1 {
				width: 168rpx;
				height: 64rpx;
				line-height: 64rpx;
				background: #F3F5F8;
				border-radius: 8rpx 8rpx 8rpx 8rpx;
				margin-right: 32rpx;
			}

			.btn2 {
				width: 168rpx;
				height: 64rpx;
				line-height: 64rpx;
				color: white;
				background: #FD4F4F;
				border-radius: 8rpx 8rpx 8rpx 8rpx;
			}
		}
	}

	.music_box {
	border-radius: $radius-md;
	padding: $spacing-sm $spacing-md;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: $spacing-sm;
	transition: $transition-default;
	box-shadow: $shadow-sm;

	&:hover {
		transform: translateY(-2rpx);
		box-shadow: $shadow-md;
	}

	.music_view {
		display: flex;
		align-items: center;

		.ellipsis {
			width: 380rpx;
			color: $text-primary;
			font-size: 28rpx;
		}

		image {
			width: 80rpx;
			height: 80rpx;
			margin-right: $spacing-sm;
			border-radius: $radius-sm;
		}
	}
}

.music_box_active {
	background: $primary-light;
	border-left: 4rpx solid #01f675;
	border-radius: $radius-md;
	padding: $spacing-sm $spacing-md;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: $spacing-sm;
	box-shadow: $shadow-sm;
}

	.extract-audio-btn {
	margin: 0;
	width: 100%;
	margin-top: $spacing-lg;
	padding: $spacing-md $spacing-lg;
	background: linear-gradient(135deg, #5af4a3 0%, #E8F8F0 100%);
	border-radius: $radius-lg;
	color: white;
	text-align: center;
	box-shadow: $shadow-sm;
	transition: $transition-default;

	&:hover {
		transform: translateY(-3rpx);
		box-shadow: 0 10rpx 20rpx rgba(33, 189, 116, 0.2);
	}

	&:active {
		transform: translateY(0);
	}

	image {
		width: 40rpx;
		height: 40rpx;
	}
}

	// 底部按钮区域样式
	.bottom-buttons {
	display: flex;
	flex-direction: column;
	margin-top: $spacing-lg;

	.button-group {
		display: flex;
		justify-content: space-between;
		margin-bottom: $spacing-sm;

		.action-button {
			flex: 1;
			height: 88rpx;
			border-radius: $radius-md;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			font-weight: 600;
			transition: $transition-default;

		

			text {
				margin-left: 10rpx;
			}
		}

		.rename-button {
			background-color: $bg-secondary;
			color: $text-secondary;
			border: 1rpx solid $border-color;
			margin-right: $spacing-sm;

			&:hover {
				background-color: #f5f5f5;
			}
		}

		.delete-button {
			color: red;
			background-color: #ffffff;
		
		}

		.disabled-button {
			opacity: 0.5;
			background-color: $border-color;
			color: $text-tertiary;
			border: none;
		}
	}
}

.confirm-button {
	width: 100%;
	height: 88rpx;
	line-height: 88rpx;
	background: $primary-color;
	border-radius: $radius-md;
	color: white;
	text-align: center;
	font-size: 32rpx;
	font-weight: 600;
	box-shadow: 0 4rpx 12rpx rgba(33, 189, 116, 0.3);
	transition: $transition-default;

	&:hover {
		background-color: $primary-dark;
		box-shadow: 0 6rpx 16rpx rgba(33, 189, 116, 0.4);
	}

	&:active {
		transform: scale(0.98);
	}
}
}

.tab_box {
	display: flex;
	align-items: center;
	justify-content: space-around;
	width: 100%;
	border-radius: $radius-lg;
	height: 90rpx;
	background: $bg-secondary;
	padding: 40rpx $spacing-md;
	box-shadow: $shadow-sm;

	.tab_text {
		font-size: 28rpx;
		color: $text-secondary;
		border-radius: 30rpx;
		padding: $spacing-xs 26rpx;
		transition: $transition-default;

		&:hover {
			background-color: rgba(33, 189, 116, 0.05);
		}
	}

	.active_tab {
		font-size: 28rpx;
		border-radius: 30rpx;
		padding: 13rpx 25rpx;
		color: white;
		background: $primary-color;
		font-weight: 500;
		box-shadow: 0 4rpx 8rpx rgba(33, 189, 116, 0.2);
		transition: $transition-default;
	}
}

.music_sound_box {
	padding: $spacing-md;
	background: $bg-secondary;
	margin-top: $spacing-sm;
	border-radius: $radius-lg;
	box-shadow: $shadow-sm;

	image {
		width: 32rpx;
		height: 32rpx;
	}

	.title {
		color: $text-primary;
		font-size: 32rpx;
		font-weight: 600;
		margin-left: 8rpx;
	}

	.text {
		font-weight: 600;
		font-size: 32rpx;
		color: $primary-color;
	}
}

.diplay_center {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.none_musicData {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	height: 40vh;
	text-align: center;
	padding: $spacing-lg;

	image {
		width: 360rpx;
		height: 360rpx;
		margin-bottom: $spacing-md;
		opacity: 0.8;
	}

	view {
		font-size: 28rpx;
		color: $text-tertiary;
		line-height: 40rpx;
	}
}

.scroll-Y {
	background-color: white;
	border-radius: 24rpx;
	padding: 32rpx;
	margin-top: 24rpx;
	box-sizing: border-box;
	overflow: hidden;
	position: relative;

	/* 添加滚动条样式 */
	::-webkit-scrollbar {
		width: 4px;
		background-color: transparent;
	}

	::-webkit-scrollbar-thumb {
		border-radius: 4px;
		background-color: rgba(0, 0, 0, 0.1);
	}
}

.action-buttons {
	display: flex;
	align-items: center;
}

.action-icons {
	display: flex;
	align-items: center;
}

.action-icon {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 16rpx;
	border-radius: 50%;
	background-color: #F3F5F8;
}

.music_box:hover .action-icons {
	opacity: 1;
}

.project_model_1 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(8rpx);
}

.project_model_sub {
  width: 80%;
  background: $bg-secondary;
  border-radius: $radius-lg;
  padding: $spacing-lg;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15);
  transform: translateY(-20rpx);
  animation: modalFadeIn 0.3s ease forwards;
}

@keyframes modalFadeIn {
  to {
    transform: translateY(0);
  }
}

</style>