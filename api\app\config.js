import {requests} from '../../utils/request.js'

/**
 * 获取页面配置信息
 * @param data type:voice声音,demoUrl案例样片
 * @returns {Promise | Promise<unknown>}
 */
export const getPageConfigInfo = (data) =>{
    return requests({
        url: '/page/config',
        method:'get',
        data:data
    })
}

/**
 * 获取项目基础信息
 * @param data
 * @returns {Promise | Promise<unknown>}
 */
export const getAppBaseInfo=(data)=>{
    return requests({
        url:'/info',
        methods: 'get',
        data:data
        }
    )
}

/**
 * 重复：获取协议信息
 * @param data
 * @returns {Promise | Promise<unknown>}
 */
export const getUserAgreement=(data)=>{
    return requests({
        url: '/page/config/agreement',
        method:'get',
        data:data
    })
}
