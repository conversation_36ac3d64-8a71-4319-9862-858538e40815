<template>
  <view class="tailor-page">
    <!-- 顶部导航栏 -->
    <custom-navbar title="裁剪" titleColor="#FFFFFF" :isBlack="false" backgroundColor="rgba(20, 20, 20, 1)"
      @back="handleBack"></custom-navbar>

    <!-- 视频预览区域 -->
    <view class="video-preview-container">
      <view class="video-preview-wrapper" :style="[previewStyle]">
        <!-- 视频播放器作为背景 -->
        <video id="videoPlayer" class="video-preview" :src="videoSrc" :style="[videoStyle]" @timeupdate="onTimeUpdate"
          @play="onPlay" @pause="onPause" @ended="onEnded" :controls="false" object-fit="contain" :show-play-btn="false"
          :show-center-play-btn="false" :show-fullscreen-btn="false" :enable-progress-gesture="false"
          @touchstart.stop="handleVideoTouchStart" @touchmove.stop="handleVideoTouchMove"
          @touchend.stop="handleVideoTouchEnd"></video>

        <!-- 九宫格裁剪框 -->
        <view class="nine-grid" :style="[nineGridStyle]">
          <view class="grid-row">
            <view class="grid-cell"></view>
            <view class="grid-cell"></view>
            <view class="grid-cell"></view>
          </view>
          <view class="grid-row">
            <view class="grid-cell"></view>
            <view class="grid-cell"></view>
            <view class="grid-cell"></view>
          </view>
          <view class="grid-row">
            <view class="grid-cell"></view>
            <view class="grid-cell"></view>
            <view class="grid-cell"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部控制区域 -->
    <view class="tailor-controls">
      <!-- 裁剪比例选择 -->
      <view class="aspect-ratio-selector">
        <view v-for="ratio in ratioOptions" :key="ratio.value" class="aspect-ratio-item">
          <view :class="['ratio-border', { 'ratio-border-active': tailorParams.aspectRatio === ratio.value }]"
            @tap="handleRatioChange(ratio.value)"></view>
          <text :class="['ratio-option', { 'ratio-active': tailorParams.aspectRatio === ratio.value }]"
            @tap="handleRatioChange(ratio.value)">
            {{ ratio.label }}
          </text>
        </view>
      </view>

      <view class="video-timeline">
        <!-- 时间显示 -->
        <view class="time-display"
          :style="{ position: 'absolute', left: (startMarkerPosition + 40 + progressWidth) + 'rpx', top: '-65rpx', transform: 'translateX(-50%)' }">
          <text class="time-text">{{ formatTime(tailorParams.currentTime) }}</text>
        </view>

        <!-- 裁剪时长提示 -->
        <!-- <view class="clip-duration-tip">
          <text>当前裁剪: {{ formatTime(tailorParams.endTime - tailorParams.startTime) }}</text>
          <text class="min-duration-tip">（最少30秒）</text>
        </view> -->

        <!-- 使用绝对定位重新布局时间轴元素 -->
        <view class="timeline-button" @tap="handlePlayPause">
          <image class="play-icon" referrerpolicy="no-referrer"
            :src="isPlaying ? '/static/tailor/icon-暂停@2x.png' : '/static/tailor/icon-播放@2x.png'" />
        </view>

        <view class="timeline-divider"></view>

        <!-- 时间线指示器 -->
        <view class="timeline-indicator" :style="{
          left: (startMarkerPosition + 40 + progressWidth) + 'rpx'
        }"></view>

        <!-- 左侧时间裁剪标记 - 可拖拽 -->
        <!-- <view class="timeline-prev" :style="{ left: startMarkerPosition + 'rpx' }" @tap="handlePrevFrame">
          <image class="prev-icon" referrerpolicy="no-referrer" src="/static/tailor/icon-左@2x.png" />
          <view class="marker-handle"></view>
        </view> -->
        <view class="timeline-prev" :style="{ left: startMarkerPosition + 'rpx' }" @tap="handlePrevFrame"
          @touchstart.stop.prevent="handleStartMarkerDragStart" @touchmove.stop.prevent="handleStartMarkerDragMove"
          @touchend.stop.prevent="handleStartMarkerDragEnd">
          <image class="prev-icon" referrerpolicy="no-referrer" src="/static/tailor/icon-左@2x.png" />
          <view class="marker-handle"></view>
        </view>
        <!-- 时间轴滑动区域 -->
        <view class="timeline-slider-container" @tap="handleTimelineClick" @touchstart="handleDragStart"
          @touchmove="handleDragMove" @touchend="handleDragEnd"
          :style="{ width: timelineWidth + 'rpx', left: (startMarkerPosition + 40) + 'rpx' }">
          <image class="timeline-slider" referrerpolicy="no-referrer" src="/static/tailor/时间轴@2x.png" />
          <view class="timeline-start-time">{{ formatTime(tailorParams.startTime) }}</view>
          <view class="timeline-end-time">{{ formatTime(tailorParams.endTime || videoInfo.duration) }}</view>
        </view>

        <!-- 右侧时间裁剪标记 - 可拖拽 -->
        <view class="timeline-next" :style="{ left: (startMarkerPosition + 40 + timelineWidth) + 'rpx' }"
          @tap="handleNextFrame" @touchstart.stop.prevent="handleEndMarkerDragStart"
          @touchmove.stop.prevent="handleEndMarkerDragMove" @touchend.stop.prevent="handleEndMarkerDragEnd">
          <image class="next-icon" referrerpolicy="no-referrer" src="/static/tailor/icon-右@2x.png" />
          <!-- <view class="marker-handle"></view> -->
        </view>
      </view>

      <!-- 完成按钮 -->
      <view class="finish-button" @tap="handleFinish">
        <text class="finish-text">完成</text>
      </view>

      <!-- 底部安全区域 -->
      <image class="bottom-safe-area" referrerpolicy="no-referrer"
        src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG0ec5d9f2cf6b8e184db2711cddb510eb.png" />
    </view>
  </view>
</template>

<script>
import customNavbar from '@/components/custom-navbar/custom-navbar.vue';

export default {
  components: {
    customNavbar
  },
  data() {
    return {
      // 视频源
      videoSrc: '',
      videoLoaded: false,
      videoInfo: {
        duration: 0,
        width: 0,
        height: 0
      },

      // 视频上下文
      videoContext: null,

      // 播放状态
      isPlaying: false,

      // 裁剪参数
      tailorParams: {
        aspectRatio: '9:16', // 裁剪框比例，格式为"宽:高"，如"9:16"表示竖屏，"16:9"表示横屏
        startTime: 0, // 视频裁剪起始时间点（单位：秒）
        endTime: 0, // 视频裁剪结束时间点（单位：秒）
        currentTime: 0, // 视频当前播放时间点（单位：秒）
      },

      // 界面控制参数
      progressWidth: 0,
      startMarkerPosition: 114, // 左侧标记位置（rpx）
      timelineWidth: 492, // 时间轴宽度（rpx），可变更
      timelineMaxWidth: 492, // 时间轴最大宽度（rpx）

      // 拖拽相关
      isDragging: false,
      dragStartX: 0,
      dragStartProgress: 0,

      // 左右标记拖拽
      isStartMarkerDragging: false,
      isEndMarkerDragging: false,
      startMarkerDragStartX: 0,
      endMarkerDragStartX: 0,
      dragStartTimelineWidth: 0,
      dragStartPosition: 0,

      // 裁剪比例选项
      ratioOptions: [
        { label: '9:16', value: '9:16' },
        { label: '4:3', value: '4:3' },
        { label: '16:9', value: '16:9' },
        { label: '3:4', value: '3:4' },
        { label: '1:1', value: '1:1' }
      ],

      // 视频位置控制
      videoPosition: {
        x: 0,
        y: 0
      },
      // 视频拖拽相关
      videoIsDragging: false,
      videoDragStartX: 0,
      videoDragStartY: 0,
      videoDragStartPosition: null,
      // 节流相关变量
      videoTouchThrottleTimer: null,
      videoTouchThrottleDelay: 16, // 约60fps

      // 视频位置更新防抖
      seekDebounceTimer: null,
      seekDebounceDelay: 100, // 防抖延迟时间(ms)
    };
  },

  computed: {
    // 根据选择的裁剪比例计算预览容器样式
    previewStyle() {
      // 使用与九宫格相同的计算方法
      const containerSize = 750; // 容器宽高都是750rpx
      const [w, h] = this.tailorParams.aspectRatio.split(':').map(Number);
      const ratio = w / h;
      let width, height;

      if (ratio > 1) {
        // 横屏比例 (16:9, 4:3 等) - 宽度占满，高度按比例
        width = `${containerSize}rpx`;
        height = `${containerSize * (h / w)}rpx`;
      } else if (ratio < 1) {
        // 竖屏比例 (9:16, 3:4 等) - 高度占满，宽度按比例
        height = `${containerSize}rpx`;
        width = `${containerSize * (w / h)}rpx`;
      } else {
        // 1:1 正方形
        width = `${containerSize}rpx`;
        height = `${containerSize}rpx`;
      }

      return {
        width,
        height
      };
    },

    // 视频样式，确保在预览区域正确显示
    videoStyle() {
      const [w, h] = this.tailorParams.aspectRatio.split(':').map(Number);
      const gridRatio = w / h; // 九宫格比例

      // 计算固定的rpx尺寸，而不是使用百分比
      let videoWidth, videoHeight;

      // 如果有视频信息，根据视频原始比例计算
      if (this.videoInfo.width && this.videoInfo.height) {
        const videoRatio = this.videoInfo.width / this.videoInfo.height; // 视频比例

        // 计算九宫格尺寸（rpx）
        let gridWidth, gridHeight;
        if (gridRatio > 1) {
          // 横屏比例
          gridWidth = 750;
          gridHeight = 750 / gridRatio;
        } else if (gridRatio < 1) {
          // 竖屏比例
          gridHeight = 750;
          gridWidth = 750 * gridRatio;
        } else {
          // 正方形
          gridWidth = 750;
          gridHeight = 750;
        }

        // 计算九宫格高度/视频高度比例
        const heightScale = gridHeight / this.videoInfo.height;
        // 计算九宫格宽度/视频宽度比例
        const widthScale = gridWidth / this.videoInfo.width;
        // 选择较大的缩放比例，确保视频能充满九宫格
        const scaleFactor = heightScale > widthScale ? heightScale : widthScale;

        // 根据缩放比例计算视频尺寸
        videoWidth = this.videoInfo.width * scaleFactor;
        videoHeight = this.videoInfo.height * scaleFactor;

        console.log('九宫格尺寸(rpx):', gridWidth, 'x', gridHeight);
        console.log('视频原始尺寸:', this.videoInfo.width, 'x', this.videoInfo.height);
        console.log('高度缩放比例:', heightScale);
        console.log('宽度缩放比例:', widthScale);
        console.log('选择的缩放比例:', scaleFactor);
        console.log('计算后的视频尺寸(rpx):', videoWidth, 'x', videoHeight);
      } else {
        // 没有视频信息，默认填充
        videoWidth = 750;
        videoHeight = 750;
      }

      return {
        width: `${videoWidth}rpx`,
        height: `${videoHeight}rpx`,
        objectFit: 'none', // 修改为none，防止浏览器自动裁剪
        transform: `translate(${this.videoPosition.x}px, ${this.videoPosition.y}px)`
      };
    },

    // 九宫格样式计算
    nineGridStyle() {
      const containerSize = 750; // 容器宽高都是750rpx
      const [w, h] = this.tailorParams.aspectRatio.split(':').map(Number);
      const ratio = w / h;
      let width, height;

      if (ratio > 1) {
        // 横屏比例 (16:9, 4:3 等) - 宽度占满，高度按比例
        width = `${containerSize}rpx`;
        height = `${containerSize * (h / w)}rpx`;
      } else if (ratio < 1) {
        // 竖屏比例 (9:16, 3:4 等) - 高度占满，宽度按比例
        height = `${containerSize}rpx`;
        width = `${containerSize * (w / h)}rpx`;
      } else {
        // 1:1 正方形
        width = `${containerSize}rpx`;
        height = `${containerSize}rpx`;
      }

      return {
        width,
        height
      };
    }
  },

  onLoad(options) {
    // 从参数中获取视频路径
    if (options.videoSrc) {
      this.videoSrc = decodeURIComponent(options.videoSrc);
      this.videoLoaded = true;

      // 获取视频信息
      this.getVideoInfo();
    }
  },

  mounted() {
    this.$nextTick(() => {
      // 初始化视频上下文
      this.videoContext = uni.createVideoContext('videoPlayer', this);
    })
  },

  methods: {
    // 获取视频信息
    getVideoInfo() {
      uni.getVideoInfo({
        src: this.videoSrc,
        success: (res) => {
          this.videoInfo.duration = res.duration;
          this.videoInfo.width = res.width;
          this.videoInfo.height = res.height;
          console.log(this.videoInfo, 'this.videoInfo');

          // 设置默认结束时间为视频时长
          this.tailorParams.endTime = res.duration;

          // 如果视频总时长小于30秒，提示用户
          if (res.duration < 30) {
            uni.showToast({
              title: '视频时长需不少于30秒',
              icon: 'none',
              duration: 3000
            });
          }
        },
        fail: (err) => {
          console.error('获取视频信息失败', err);
          uni.showToast({
            title: '获取视频信息失败',
            icon: 'none'
          });
        }
      });
    },

    // 视频位置更新防抖方法
    debounceSeek(time) {
      // 清除现有的定时器
      if (this.seekDebounceTimer) {
        clearTimeout(this.seekDebounceTimer);
        this.seekDebounceTimer = null;
      }

      // 设置新的定时器
      this.seekDebounceTimer = setTimeout(() => {
        // 将时间值取整后再传入seek方法
        const intTime = Math.floor(time);
        console.log(intTime, 'intTime');

        this.videoContext.seek(intTime);
      }, this.seekDebounceDelay);
    },

    // 格式化时间显示（秒转为 00:00:00 格式）
    formatTime(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = Math.floor(seconds % 60);

      return [hours, minutes, secs]
        .map(val => val.toString().padStart(2, '0'))
        .join(':');
    },

    // 切换裁剪比例
    handleRatioChange(ratio) {
      // 更新裁剪比例
      this.tailorParams.aspectRatio = ratio;

      // 重置视频位置为原点
      this.videoPosition = {
        x: 0,
        y: 0
      };

      // 使用nextTick确保DOM更新后重新计算视频位置和边界
      this.$nextTick(() => {
        // 获取当前裁剪比例
        const [w, h] = ratio.split(':').map(Number);
        const gridRatio = w / h; // 九宫格比例

        // 辅助函数：将rpx单位转换为px
        const convertRpxToPx = (rpxValue) => {
          const screenWidth = uni.getSystemInfoSync().windowWidth;
          return rpxValue * screenWidth / 750;
        };

        // 计算九宫格尺寸（rpx）
        let gridWidth, gridHeight;
        if (gridRatio > 1) {
          // 横屏比例
          gridWidth = 750;
          gridHeight = 750 / gridRatio;
        } else if (gridRatio < 1) {
          // 竖屏比例
          gridHeight = 750;
          gridWidth = 750 * gridRatio;
        } else {
          // 正方形
          gridWidth = 750;
          gridHeight = 750;
        }

        // 如果视频信息已加载，计算视频尺寸并调整位置
        if (this.videoInfo.width && this.videoInfo.height) {
          // 计算九宫格高度/视频高度比例
          const heightScale = gridHeight / this.videoInfo.height;
          // 计算九宫格宽度/视频宽度比例
          const widthScale = gridWidth / this.videoInfo.width;
          // 选择较大的缩放比例，确保视频能充满九宫格
          const scaleFactor = heightScale > widthScale ? heightScale : widthScale;

          // 根据缩放比例计算视频尺寸
          const videoWidth = this.videoInfo.width * scaleFactor;
          const videoHeight = this.videoInfo.height * scaleFactor;

          // 转换为px进行边界计算
          const videoWidthPx = convertRpxToPx(videoWidth);
          const videoHeightPx = convertRpxToPx(videoHeight);
          const gridWidthPx = convertRpxToPx(gridWidth);
          const gridHeightPx = convertRpxToPx(gridHeight);

          // 如果视频小于九宫格，则居中显示
          if (videoWidthPx < gridWidthPx) {
            this.videoPosition.x = (gridWidthPx - videoWidthPx) / 2;
          }

          if (videoHeightPx < gridHeightPx) {
            this.videoPosition.y = (gridHeightPx - videoHeightPx) / 2;
          }
        }
      });
    },

    // 播放/暂停视频
    handlePlayPause() {
      if (this.isPlaying) {
        this.videoContext.pause();
        this.isPlaying = false;
      } else {
        // 确保从裁剪起始点开始播放
        if (this.tailorParams.currentTime < this.tailorParams.startTime ||
          this.tailorParams.currentTime > this.tailorParams.endTime) {
          this.tailorParams.currentTime = this.tailorParams.startTime;
          // 立即跳转到起始位置，不使用防抖
          this.videoContext.seek(Math.floor(this.tailorParams.startTime));
        }

        // 确保视频已经跳转到正确位置后再播放
        setTimeout(() => {
          this.videoContext.play();
          this.isPlaying = true;
          // 更新进度条位置
          this.updateProgressPosition();
        }, 200)
      }
    },

    // 前一帧
    handlePrevFrame() {
      const newTime = Math.max(this.tailorParams.currentTime - 0.5, 0);

      this.videoContext.seek(Math.floor(newTime));
      this.tailorParams.currentTime = newTime;
      this.updateProgressPosition();
    },

    // 后一帧
    handleNextFrame() {
      const newTime = Math.min(this.tailorParams.currentTime + 0.5, this.videoInfo.duration);

      this.videoContext.seek(Math.floor(newTime));
      this.tailorParams.currentTime = newTime;
      this.updateProgressPosition();
    },

    // 点击时间轴
    handleTimelineClick(e) {
      if (!this.videoInfo.duration) return;

      const clickX = e.touches ? e.touches[0].clientX : e.detail.x;

      // 使用uni-app选择器查询API
      const query = uni.createSelectorQuery();
      query.select('.timeline-slider-container').boundingClientRect(data => {
        if (data) {
          // 计算点击位置相对于时间轴的百分比
          const offsetX = clickX - data.left;
          const percentage = Math.max(0, Math.min(offsetX / data.width, 1));

          // 计算裁剪区间内的时间点
          const clipDuration = this.tailorParams.endTime - this.tailorParams.startTime;
          const newTime = this.tailorParams.startTime + (percentage * clipDuration);

          // 更新视频时间和UI
          this.tailorParams.currentTime = Math.max(
            this.tailorParams.startTime,
            Math.min(newTime, this.tailorParams.endTime)
          );

          this.videoContext.seek(Math.floor(this.tailorParams.currentTime));

          // 更新进度条位置
          this.progressWidth = percentage * this.timelineWidth;
        }
      }).exec();
    },

    // 更新进度条位置
    updateProgressPosition() {
      if (!this.videoInfo.duration) return;

      // 确保当前时间在裁剪区间内
      if (this.tailorParams.currentTime < this.tailorParams.startTime) {
        this.tailorParams.currentTime = this.tailorParams.startTime;
      } else if (this.tailorParams.currentTime > this.tailorParams.endTime) {
        this.tailorParams.currentTime = this.tailorParams.endTime;
      }

      // 计算相对时间点（相对于裁剪区域起点）
      const relativeTime = this.tailorParams.currentTime - this.tailorParams.startTime;

      // 计算裁剪区域的时长
      const clipDuration = this.tailorParams.endTime - this.tailorParams.startTime;

      // 计算在裁剪区域内的进度比例
      const progressPercentage = clipDuration > 0 ? relativeTime / clipDuration : 0;

      // 将进度比例转换为进度条宽度，并限制在时间轴宽度内
      this.progressWidth = Math.max(0, Math.min(progressPercentage * this.timelineWidth, this.timelineWidth));
    },

    // 视频时间更新事件
    onTimeUpdate(e) {
      // 如果正在拖拽，则不更新位置
      if (this.isDragging || this.isStartMarkerDragging || this.isEndMarkerDragging) return;
      const currentTime = e.detail.currentTime;
      // 定义一个极小的容差值，解决浮点数精度问题
      const timeTolerance = 0.5;
      // 检查是否超出裁剪区域，增加容差值避免边界上的跳跃
      if (currentTime < this.tailorParams.startTime - timeTolerance) {
        // 如果当前时间小于裁剪起始时间，则设置为裁剪起始时间
        // this.tailorParams.currentTime = this.tailorParams.startTime;
        // this.debounceSeek(this.tailorParams.startTime);
      } else if (currentTime > this.tailorParams.endTime - timeTolerance) {
        // 如果当前时间大于裁剪结束时间，则暂停播放并回到起始位置
        this.tailorParams.currentTime = this.tailorParams.startTime;
        this.debounceSeek(this.tailorParams.startTime);
        this.videoContext.pause();
        this.isPlaying = false;
      } else {
        // 在裁剪区域内，正常更新当前时间
        this.tailorParams.currentTime = currentTime;
      }

      // 更新进度条和时间指示器位置
      this.updateProgressPosition();
    },

    // 视频播放事件
    onPlay() {
      this.isPlaying = true;
    },

    // 视频暂停事件
    onPause() {
      this.isPlaying = false;
    },

    // 视频播放结束事件
    onEnded() {
      this.isPlaying = false;
      this.tailorParams.currentTime = 0;
      this.updateProgressPosition();
    },

    // 返回上一页
    handleBack() {
      uni.navigateBack();
    },

    // 完成裁剪
    handleFinish() {
      // 验证裁剪后的视频时长是否符合要求
      const clipDuration = this.tailorParams.endTime - this.tailorParams.startTime;
      if (clipDuration < 30) {
        uni.showToast({
          title: '裁剪后的视频不能少于30秒',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 计算当前九宫格尺寸
      const [w, h] = this.tailorParams.aspectRatio.split(':').map(Number);
      const gridRatio = w / h; // 九宫格比例

      // 计算九宫格尺寸（rpx）
      let gridWidth, gridHeight;
      if (gridRatio > 1) {
        // 横屏比例
        gridWidth = 750;
        gridHeight = 750 / gridRatio;
      } else if (gridRatio < 1) {
        // 竖屏比例
        gridHeight = 750;
        gridWidth = 750 * gridRatio;
      } else {
        // 正方形
        gridWidth = 750;
        gridHeight = 750;
      }

      // 辅助函数：将rpx单位转换为px
      const convertRpxToPx = (rpxValue) => {
        const screenWidth = uni.getSystemInfoSync().windowWidth;
        return rpxValue * screenWidth / 750;
      };

      // 计算九宫格尺寸（px）
      const gridWidthPx = convertRpxToPx(gridWidth);
      const gridHeightPx = convertRpxToPx(gridHeight);

      // 计算视频实际显示尺寸
      let videoWidth, videoHeight;
      if (this.videoInfo.width && this.videoInfo.height) {
        // 计算缩放比例
        const heightScale = gridHeight / this.videoInfo.height;
        const widthScale = gridWidth / this.videoInfo.width;
        const scaleFactor = heightScale > widthScale ? heightScale : widthScale;

        // 根据缩放比例计算视频尺寸
        videoWidth = this.videoInfo.width * scaleFactor;
        videoHeight = this.videoInfo.height * scaleFactor;
      } else {
        videoWidth = 750;
        videoHeight = 750;
      }

      // 转换为px
      const videoWidthPx = convertRpxToPx(videoWidth);
      const videoHeightPx = convertRpxToPx(videoHeight);

      // 保存裁剪参数
      const params = {
        videoSrc: this.videoSrc, // 视频源文件路径
        aspectRatio: this.tailorParams.aspectRatio, // 裁剪比例，格式为"宽:高"
        startTime: this.tailorParams.startTime, // 时间轴裁剪起始点（秒）
        endTime: this.tailorParams.endTime || this.videoInfo.duration, // 时间轴裁剪结束点（秒）
        duration: this.videoInfo.duration, // 原视频总时长（秒）
        // 视频位置信息，用于计算最终裁剪区域
        position: {
          x: this.videoPosition.x, // 视频X轴偏移量（像素），控制裁剪的水平位置
          y: this.videoPosition.y  // 视频Y轴偏移量（像素），控制裁剪的垂直位置
        },
        // 视频和九宫格尺寸信息
        dimensions: {
          // 原始视频尺寸
          originalVideo: {
            width: this.videoInfo.width, // 原始视频宽度（px）
            height: this.videoInfo.height // 原始视频高度（px）
          },
          // 缩放后的视频尺寸
          scaledVideo: {
            width: videoWidthPx, // 缩放后的视频宽度（px）
            height: videoHeightPx // 缩放后的视频高度（px）
          },
          // 九宫格尺寸
          grid: {
            width: gridWidthPx, // 九宫格宽度（px）
            height: gridHeightPx // 九宫格高度（px）
          }
        }
      };
      // 缩放比例
      const scale = params.dimensions.originalVideo.width / params.dimensions.scaledVideo.width;

      this.$store.commit('setDrVideoCropInfo', {
        scale: [params.dimensions.scaledVideo.width * scale, -1],
        crop: [params.dimensions.grid.width * scale, params.dimensions.grid.height * scale, -params.position.x * scale, -params.position.y * scale],
        duration: [Number(this.tailorParams.startTime), Number(this.tailorParams.endTime)]
      });
      console.log({
        scale: [params.dimensions.scaledVideo.width * scale, -1],
        crop: [params.dimensions.grid.width * scale, params.dimensions.grid.height * scale, -params.position.x * scale, -params.position.y * scale],
        duration: [Number(this.tailorParams.startTime), Number(this.tailorParams.endTime)]
      }, '参数');

      this.$store.commit('SetNumberUserVideo', this.videoSrc);

      uni.redirectTo({ url: '/subpkg/index/save_video/index?type=pro' });
      // 裁剪完成后返回上两个页面
      // uni.navigateBack({
      //   delta: 2
      // });
    },

    // 拖拽开始
    handleDragStart(e) {
      this.isDragging = true;

      // 暂停视频
      if (this.isPlaying) {
        this.videoContext.pause();
        this.isPlaying = false;
      }

      // 立即处理第一次触摸，快速更新位置
      this.handleDragMove(e);
    },

    // 拖拽移动
    handleDragMove(e) {
      if (!this.isDragging) return;

      // 获取触摸点在时间轴内的相对位置
      const query = uni.createSelectorQuery().in(this);
      query.select('.timeline-slider-container').boundingClientRect(data => {
        if (!data) return;

        const touchX = e.touches[0].clientX;

        // 计算触摸点相对于时间轴容器左边界的位置
        const offsetX = touchX - data.left;

        // 将位置限制在时间轴内
        const limitedOffsetX = Math.max(0, Math.min(offsetX, data.width));

        // 计算百分比位置
        const percentage = limitedOffsetX / data.width;

        // 计算对应的进度宽度
        this.progressWidth = percentage * this.timelineWidth;

        // 计算视频时间点
        const startTimePercentage = (this.startMarkerPosition - 114) / this.timelineMaxWidth;
        const clipDuration = this.tailorParams.endTime - this.tailorParams.startTime;
        const newTime = this.tailorParams.startTime + (percentage * clipDuration);

        // 更新视频时间
        this.tailorParams.currentTime = Math.max(
          this.tailorParams.startTime,
          Math.min(newTime, this.tailorParams.endTime)
        );

        // 更新视频位置（使用防抖）
        this.debounceSeek(this.tailorParams.currentTime);
        // console.log(this.tailorParams.currentTime);
      }).exec();
    },

    // 拖拽结束
    handleDragEnd(e) {
      if (!this.isDragging) return;

      this.isDragging = false;
    },

    // 开始时间标记拖拽开始
    handleStartMarkerDragStart(e) {
      this.isStartMarkerDragging = true;
      this.startMarkerDragStartX = e.touches[0].clientX;

      // 记录当前的左侧标记位置和时间轴宽度
      this.dragStartPosition = this.startMarkerPosition;
      this.dragStartTimelineWidth = this.timelineWidth;

      // 暂停视频
      if (this.isPlaying) {
        this.videoContext.pause();
        this.isPlaying = false;
      }
    },

    // 开始时间标记拖拽移动
    handleStartMarkerDragMove(e) {
      if (!this.isStartMarkerDragging) return;

      // 计算拖拽距离
      const currentX = e.touches[0].clientX;
      const moveDistance = currentX - this.startMarkerDragStartX;

      // 将移动距离转换为rpx
      const screenWidth = uni.getSystemInfoSync().windowWidth;
      const moveDistanceRpx = moveDistance / screenWidth * 750;

      // 计算新的左侧标记位置和时间轴宽度
      let newPosition = this.dragStartPosition + moveDistanceRpx;
      let newWidth = this.dragStartTimelineWidth - moveDistanceRpx;

      // 设置最小宽度限制，防止时间轴过小
      const minWidth = 120; // 最小宽度为120rpx
      if (newWidth < minWidth) {
        newWidth = minWidth;
        newPosition = this.dragStartPosition + (this.dragStartTimelineWidth - minWidth);
      }

      // 设置左侧标记位置限制
      const minPosition = 114; // 最小位置（不能小于播放按钮和分隔线的宽度）
      if (newPosition < minPosition) {
        newPosition = minPosition;
        newWidth = this.dragStartTimelineWidth + (this.dragStartPosition - minPosition);
      }

      // 计算并更新开始时间
      const percentage = (newPosition - minPosition) / this.timelineMaxWidth;
      let newStartTime = percentage * this.videoInfo.duration;

      // 确保裁剪时长至少30秒
      const minDuration = 30; // 最小时长30秒
      if (this.tailorParams.endTime - newStartTime < minDuration) {
        newStartTime = Math.max(0, this.tailorParams.endTime - minDuration);

        // 根据新的开始时间重新计算position和width
        const newPercentage = newStartTime / this.videoInfo.duration;
        newPosition = minPosition + (newPercentage * this.timelineMaxWidth);
        newWidth = this.timelineWidth + (this.startMarkerPosition - newPosition);
      }

      this.tailorParams.startTime = newStartTime;

      // 直接更新位置和宽度属性
      this.startMarkerPosition = newPosition;
      this.timelineWidth = newWidth;

      // 将当前时间设置为起始时间，使指示器跟随左标记移动
      this.tailorParams.currentTime = this.tailorParams.startTime;
      this.debounceSeek(this.tailorParams.currentTime);

      // 将进度宽度设为0，让指示器位于左侧标记位置
      this.progressWidth = 0;
    },

    // 开始时间标记拖拽结束
    handleStartMarkerDragEnd() {
      this.isStartMarkerDragging = false;

      // 确保视频暂停在正确位置并更新UI
      const intTime = Math.floor(this.tailorParams.currentTime);
      this.videoContext.seek(intTime);

      // 确保所有状态同步更新
      setTimeout(() => {
        // 清除可能存在的防抖定时器
        if (this.seekDebounceTimer) {
          clearTimeout(this.seekDebounceTimer);
          this.seekDebounceTimer = null;
        }

        // 重新计算和更新UI
        this.updateProgressPosition();
      }, 50);
    },

    // 结束时间标记拖拽开始
    handleEndMarkerDragStart(e) {
      this.isEndMarkerDragging = true;
      this.endMarkerDragStartX = e.touches[0].clientX;
      this.dragStartTimelineWidth = this.timelineWidth;

      // 暂停视频
      if (this.isPlaying) {
        this.videoContext.pause();
        this.isPlaying = false;
      }
    },

    // 结束时间标记拖拽移动
    handleEndMarkerDragMove(e) {
      if (!this.isEndMarkerDragging) return;

      // 计算拖拽距离
      const currentX = e.touches[0].clientX;
      const moveDistance = currentX - this.endMarkerDragStartX;

      // 将移动距离转换为rpx
      const screenWidth = uni.getSystemInfoSync().windowWidth;
      const moveDistanceRpx = moveDistance / screenWidth * 750;

      // 计算新的时间轴宽度
      let newWidth = this.dragStartTimelineWidth + moveDistanceRpx;

      // 设置最小和最大宽度限制
      const minWidth = 120; // 最小宽度为120rpx
      // 修改最大宽度计算，使用video-timeline容器的宽度减去左侧占用的宽度
      const containerWidth = 686; // 时间轴容器的总宽度
      const leftOffset = this.startMarkerPosition + 40; // 左侧标记位置加上间距
      const maxWidth = containerWidth - leftOffset - 40; // 预留40rpx的安全边距

      if (newWidth < minWidth) {
        newWidth = minWidth;
      }

      if (newWidth > maxWidth) {
        newWidth = maxWidth;
      }

      // 计算对应的结束时间
      const startPercentage = (this.startMarkerPosition - 114) / this.timelineMaxWidth;
      const endPercentage = startPercentage + (newWidth / this.timelineMaxWidth);
      let newEndTime = Math.min(endPercentage * this.videoInfo.duration, this.videoInfo.duration);

      // 确保裁剪时长至少30秒
      const minDuration = 30; // 最小时长30秒
      if (newEndTime - this.tailorParams.startTime < minDuration) {
        newEndTime = Math.min(this.tailorParams.startTime + minDuration, this.videoInfo.duration);

        // 根据新的结束时间重新计算width
        const durationPercentage = minDuration / this.videoInfo.duration;
        newWidth = durationPercentage * this.timelineMaxWidth;
      }

      // 更新宽度
      this.timelineWidth = newWidth;

      // 更新结束时间
      this.tailorParams.endTime = newEndTime;

      // 将当前时间设置为结束时间，使指示器跟随右标记移动
      this.tailorParams.currentTime = this.tailorParams.endTime;
      this.debounceSeek(this.tailorParams.currentTime);

      // 将进度宽度设为时间轴宽度，让指示器位于右侧标记位置
      this.progressWidth = this.timelineWidth;
    },

    // 结束时间标记拖拽结束
    handleEndMarkerDragEnd() {
      this.isEndMarkerDragging = false;

      // 确保视频暂停在正确位置并更新UI
      this.videoContext.seek(Math.floor(this.tailorParams.currentTime));
    },

    // 视频拖拽开始
    handleVideoTouchStart(e) {
      this.videoIsDragging = true;
      this.videoDragStartX = e.touches[0].clientX;
      this.videoDragStartY = e.touches[0].clientY;

      // 记录开始拖拽时的视频位置
      this.videoDragStartPosition = {
        x: this.videoPosition.x,
        y: this.videoPosition.y
      };
    },

    // 视频拖拽中
    handleVideoTouchMove(e) {
      if (!this.videoIsDragging) return;

      // 添加节流逻辑
      if (this.videoTouchThrottleTimer) return;

      this.videoTouchThrottleTimer = setTimeout(() => {
        this.videoTouchThrottleTimer = null;

        // 计算拖拽距离
        const moveX = e.touches[0].clientX - this.videoDragStartX;
        const moveY = e.touches[0].clientY - this.videoDragStartY;

        // 基础位置更新
        let newX = this.videoDragStartPosition.x + moveX;
        let newY = this.videoDragStartPosition.y + moveY;

        // 获取当前裁剪比例
        const [w, h] = this.tailorParams.aspectRatio.split(':').map(Number);
        const gridRatio = w / h; // 九宫格比例

        // 辅助函数：将rpx单位转换为px
        const convertRpxToPx = (rpxValue) => {
          const screenWidth = uni.getSystemInfoSync().windowWidth;
          return rpxValue * screenWidth / 750;
        };

        // 计算九宫格尺寸（rpx）
        let gridWidth, gridHeight;
        if (gridRatio > 1) {
          // 横屏比例
          gridWidth = 750;
          gridHeight = 750 / gridRatio;
        } else if (gridRatio < 1) {
          // 竖屏比例
          gridHeight = 750;
          gridWidth = 750 * gridRatio;
        } else {
          // 正方形
          gridWidth = 750;
          gridHeight = 750;
        }

        // 计算视频实际显示尺寸（rpx）
        let videoWidth, videoHeight;
        if (this.videoInfo.width && this.videoInfo.height) {
          // 计算九宫格高度/视频高度比例
          const heightScale = gridHeight / this.videoInfo.height;
          // 计算九宫格宽度/视频宽度比例
          const widthScale = gridWidth / this.videoInfo.width;
          // 选择较大的缩放比例，确保视频能充满九宫格
          const scaleFactor = heightScale > widthScale ? heightScale : widthScale;

          // 根据缩放比例计算视频尺寸
          videoWidth = this.videoInfo.width * scaleFactor;
          videoHeight = this.videoInfo.height * scaleFactor;
        } else {
          // 默认情况
          videoWidth = 750;
          videoHeight = 750;
        }

        // 转换为px进行边界计算
        const videoWidthPx = convertRpxToPx(videoWidth);
        const videoHeightPx = convertRpxToPx(videoHeight);
        const gridWidthPx = convertRpxToPx(gridWidth);
        const gridHeightPx = convertRpxToPx(gridHeight);

        // 计算视频内容必须保持在九宫格内的最大可移动距离
        let maxMoveX = 0, maxMoveY = 0;

        // 如果视频比九宫格大，允许拖动，但限制边界
        if (videoWidthPx > gridWidthPx) {
          // 视频初始位置在左上角(0,0)，所以最大可移动距离是视频宽度减去九宫格宽度
          maxMoveX = videoWidthPx - gridWidthPx;
          // 限制移动：视频左边界不能超出九宫格左边界，视频右边界不能小于九宫格右边界
          newX = Math.max(Math.min(newX, 0), -maxMoveX);
        } else {
          // 如果视频小于九宫格，应该居中且不可拖动
          newX = (gridWidthPx - videoWidthPx) / 2;
        }

        if (videoHeightPx > gridHeightPx) {
          // 视频初始位置在左上角(0,0)，所以最大可移动距离是视频高度减去九宫格高度
          maxMoveY = videoHeightPx - gridHeightPx;
          // 限制移动：视频上边界不能超出九宫格上边界，视频下边界不能小于九宫格下边界
          newY = Math.max(Math.min(newY, 0), -maxMoveY);
        } else {
          // 如果视频小于九宫格，应该居中且不可拖动
          newY = (gridHeightPx - videoHeightPx) / 2;
        }

        // 更新视频位置
        this.videoPosition.x = newX;
        this.videoPosition.y = newY;
      }, this.videoTouchThrottleDelay);
    },

    // 视频拖拽结束
    handleVideoTouchEnd() {
      this.videoIsDragging = false;
      // 清除可能存在的节流定时器
      if (this.videoTouchThrottleTimer) {
        clearTimeout(this.videoTouchThrottleTimer);
        this.videoTouchThrottleTimer = null;
      }
    }
  }
};
</script>

<style lang="scss">
.tailor-page {
  background-color: rgba(34, 34, 34, 1);
  position: relative;
  width: 750rpx;
  height: 100vh;
  // height: 1624rpx;
  overflow: scroll;

  // 隐藏滚动条
  &::-webkit-scrollbar {
    display: none;
  }

  .video-preview-container {
    width: 750rpx;
    height: 750rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #000;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1;
    }

    .video-preview-wrapper {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 2;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      // overflow: hidden;
      transition: all 0.3s ease;

      .nine-grid {
        position: absolute;
        z-index: 3;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        pointer-events: none; // 允许点击穿透
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        overflow: hidden;
        box-sizing: border-box;
        border: 2px solid rgba(255, 255, 255, 0.8); // 添加白色边框
        box-shadow: 0 0 0 1000px rgba(0, 0, 0, 0.5); // 添加阴影创建镂空效果
        transition: width 0.3s ease, height 0.3s ease, box-shadow 0.3s ease; // 指定属性过渡动画

        .grid-row {
          display: flex;
          flex: 1;
          width: 100%;
          justify-content: space-between;

          .grid-cell {
            flex: 1;
            height: 100%;
            position: relative;
            box-sizing: border-box;
          }

          // 使用伪元素创建更精确的网格线
          &::before,
          &::after {
            content: '';
            position: absolute;
            background-color: rgba(255, 255, 255, 0.6);
            z-index: 4;
            transition: all 0.3s ease;
          }

          // 横线
          &::before {
            height: 1px;
            width: 100%;
            left: 0;
            top: 33.33%;
          }

          &::after {
            height: 1px;
            width: 100%;
            left: 0;
            top: 66.66%;
          }
        }

        // 竖线
        &::before,
        &::after {
          content: '';
          position: absolute;
          background-color: rgba(255, 255, 255, 0.6);
          z-index: 4;
          transition: all 0.3s ease;
        }

        &::before {
          width: 1px;
          height: 100%;
          left: 33.33%;
          top: 0;
        }

        &::after {
          width: 1px;
          height: 100%;
          left: 66.66%;
          top: 0;
        }
      }

      .video-preview {
        width: 100%;
        height: 100%;
        background-color: #000;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        // transition: all 0.3s ease;

        /* 隐藏视频默认的控制元素 */
        &::-webkit-media-controls {
          display: none !important;
        }

        &::-webkit-media-controls-panel {
          display: none !important;
        }

        &::-webkit-media-controls-play-button {
          display: none !important;
        }

        &::-webkit-media-controls-start-playback-button {
          display: none !important;
        }
      }
    }
  }

  .tailor-controls {
    background-color: rgba(34, 34, 34, 1);
    position: relative;
    width: 750rpx;
    // height: 704rpx; 
    margin-top: -2rpx;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;

    .aspect-ratio-selector {
      width: 100%;
      height: 152rpx;
      margin: 37rpx 0;
      display: flex;
      justify-content: center;
      align-items: flex-end;

      .aspect-ratio-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 20rpx;

        .ratio-border {
          border-radius: 4px;
          border: 1px solid rgba(153, 153, 153, 1);
          margin-bottom: 16rpx;

          &.ratio-border-active {
            border: 1px solid rgba(1, 246, 117, 1);
          }
        }

        &:nth-child(1) .ratio-border {
          // 9:16比例 - 竖屏
          width: 42rpx;
          height: 76rpx;
        }

        &:nth-child(2) .ratio-border {
          // 4:3比例
          width: 60rpx;
          height: 45rpx;
        }

        &:nth-child(3) .ratio-border {
          // 16:9比例 - 横屏
          width: 72rpx;
          height: 40rpx;
        }

        &:nth-child(4) .ratio-border {
          // 3:4比例
          width: 45rpx;
          height: 60rpx;
        }

        &:nth-child(5) .ratio-border {
          // 1:1比例 - 正方形
          width: 58rpx;
          height: 58rpx;
        }

        .ratio-option {
          width: 48rpx;
          height: 32rpx;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 24rpx;
          font-family: MiSans-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 32rpx;

          &.ratio-active {
            color: rgba(1, 246, 117, 1);
          }
        }
      }
    }

    .video-timeline {
      background-color: rgba(243, 245, 248, 1);
      border-radius: 8px;
      width: 686rpx;
      height: 112rpx;
      margin: 52rpx 0 0 32rpx;
      display: flex;
      flex-direction: row;
      position: relative;

      .time-display {
        background-color: rgba(243, 245, 248, 1);
        border-radius: 4px;
        height: 48rpx;
        width: 122rpx;
        z-index: 20;
        display: flex;
        flex-direction: column;
        box-shadow: 0 0 6rpx rgba(0, 0, 0, 0.2);

        .time-text {
          width: 90rpx;
          height: 28rpx;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 20rpx;
          font-family: MiSans-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 28rpx;
          margin: 10rpx 0 0 16rpx;
        }
      }

      .clip-duration-tip {
        position: absolute;
        top: -30rpx;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.7);
        border-radius: 6rpx;
        padding: 4rpx 16rpx;
        z-index: 20;
        display: flex;
        flex-direction: row;
        align-items: center;

        text {
          font-size: 20rpx;
          color: #FFFFFF;
        }

        .min-duration-tip {
          font-size: 18rpx;
          color: rgba(1, 246, 117, 1);
          margin-left: 8rpx;
        }
      }

      .timeline-button {
        background-color: rgba(51, 51, 51, 0.5);
        border-radius: 50%;
        height: 64rpx;
        width: 64rpx;
        position: absolute;
        left: 24rpx;
        top: 24rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 5;

        .play-icon {
          width: 64rpx;
          height: 64rpx;
        }
      }

      .timeline-divider {
        background-color: rgba(51, 51, 51, 1);
        width: 2rpx;
        height: 112rpx;
        position: absolute;
        left: 112rpx;
        top: 0;
        z-index: 5;
      }

      .timeline-prev {
        position: absolute;
        top: 0;
        height: 112rpx;
        width: 40rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: white;
        z-index: 100;
        cursor: ew-resize;
        box-shadow: 0 0 6rpx rgba(0, 0, 0, 0.3);
        // border-right: 2rpx solid rgba(1, 246, 117, 0.8);
        will-change: transform, left;
        touch-action: none;

        .prev-icon {
          width: 26rpx;
          height: 24rpx;
        }

        .marker-handle {
          position: absolute;
          right: 0;
          top: 40%;
          height: 20%;
          width: 6rpx;
          background-color: rgba(1, 246, 117, 1);
          border-radius: 3rpx;
        }
      }

      .timeline-slider-container {
        position: absolute;
        height: 96rpx;
        margin-top: 8rpx;
        overflow: hidden;
        z-index: 1;
        border-top: 2rpx solid rgba(1, 246, 117, 0.5);
        border-bottom: 2rpx solid rgba(1, 246, 117, 0.5);
        will-change: transform, width, left;

        .timeline-slider {
          width: 100%;
          height: 100%;
        }

        .timeline-progress {
          position: absolute;
          top: 0;
          left: 0;
          height: 6rpx;
          background-color: rgba(1, 246, 117, 1);
          z-index: 2;
        }

        .timeline-start-time,
        .timeline-end-time {
          position: absolute;
          bottom: 5rpx;
          font-size: 16rpx;
          color: #333;
          background-color: rgba(255, 255, 255, 0.7);
          padding: 2rpx 8rpx;
          border-radius: 4rpx;
          z-index: 11;
        }

        .timeline-start-time {
          left: 5rpx;
        }

        .timeline-end-time {
          right: 5rpx;
        }
      }

      .timeline-next {
        position: absolute;
        height: 112rpx;
        width: 40rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 10;
        cursor: ew-resize;
        box-shadow: 0 0 6rpx rgba(0, 0, 0, 0.3);
        // border-left: 2rpx solid rgba(1, 246, 117, 0.8);
        will-change: transform, left;

        .next-icon {
          width: 26rpx;
          height: 24rpx;
        }

        .marker-handle {
          position: absolute;
          left: 0;
          top: 40%;
          height: 20%;
          width: 6rpx;
          background-color: rgba(1, 246, 117, 1);
          border-radius: 3rpx;
        }
      }
    }

    .finish-button {
      background-image: linear-gradient(135deg,
          rgba(7, 227, 210, 1) 0,
          rgba(1, 247, 112, 1) 100%);
      border-radius: 8px;
      height: 96rpx;
      width: 686rpx;
      margin: 88rpx 0 0 32rpx;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .finish-text {
        width: 72rpx;
        height: 56rpx;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 36rpx;
        font-family: MiSans-Demibold;
        font-weight: normal;
        text-align: center;
        white-space: nowrap;
        line-height: 56rpx;
      }
    }

    .bottom-safe-area {
      width: 750rpx;
      height: 68rpx;
      margin-top: 12rpx;
    }

    .timeline-indicator {
      background-color: #FFFFFF;
      border-radius: 1px;
      position: absolute;
      top: -8rpx;
      width: 4rpx;
      height: 130rpx;
      z-index: 15;
      transform: translateX(-2rpx);
      pointer-events: none;
    }
  }
}
</style>