/* 
  处理模板里面的数据，适配画布缩放
    scale: 缩放比例
    data: 模板数据
  模板数据默认是1080*1920
*/
export const videoTemplate = (scale, templateData) => {
  console.log(templateData, 'templateData');

  return templateData.map(template => {
    return {
      ...template, metadata: template.metadata.map(meta => {
        let defineData = meta.define;
        defineData.height = defineData.height * scale;
        defineData.width = defineData.width * scale;
        // 处理标题位置   数组第0位为x轴，第1位为y轴  z轴不做处理
        defineData.transform.position[0] = defineData.transform.position[0] * scale;
        defineData.transform.position[1] = defineData.transform.position[1] * scale;
        // 原点缩放
        defineData.transform.anchor[0] = defineData.transform.anchor[0] * scale;
        defineData.transform.anchor[1] = defineData.transform.anchor[1] * scale;
        if (meta.type == 'title') {
          defineData.height = defineData.height;
          console.log(defineData.height, 'defineData.height');

        }

        // 字幕处理
        if (meta.type == 'caption') {

        }

        // 背景图片（暂不做处理）
        if (meta.type == "mask") {
        }

        // 身份栏
        if (meta.type == 'introduceCard') {
          // 处理内边距
          if (defineData.padding) {
            defineData.padding.bottom = defineData.padding.bottom * scale;
            defineData.padding.top = defineData.padding.top * scale;
            defineData.padding.left = defineData.padding.left * scale;
            defineData.padding.right = defineData.padding.right * scale;
          }
          // 字体大小
          if (defineData.fontSize) {
            defineData.fontSize = defineData.fontSize * scale;
          }
        }
        return meta;
      })
    }
  })
}

/* 
  逆向处理模板里面的数据，适配画布缩放
    scale: 缩放比例
    data: 模板数据
  模板数据默认是1080*1920
*/
export const videoTemplateReverse = (scale, metadata) => {
  let newMetadata = JSON.parse(JSON.stringify(metadata)).map(meta => {
    let defineData = meta.define;
    if (meta.type == 'title') {
      // 处理标题宽高

      defineData.height = defineData.height / scale;
      defineData.width = defineData.width / scale;
      console.log(defineData.transform.position[0], '1');
      console.log(defineData.transform.position[1], '1');
      // 处理标题位置   数组第0位为x轴，第1位为y轴  z轴不做处理
      defineData.transform.position[0] = defineData.transform.position[0] / scale;
      defineData.transform.position[1] = defineData.transform.position[1] / scale;
      // 原点缩放
      defineData.transform.anchor[0] = defineData.transform.anchor[0] / scale;
      defineData.transform.anchor[1] = defineData.transform.anchor[1] / scale;
    }

    // 字幕处理
    if (meta.type == 'caption') {
      defineData.height = defineData.height / scale;
      defineData.width = defineData.width / scale;
      // 处理标题位置   数组第0位为x轴，第1位为y轴  z轴不做处理
      defineData.transform.position[0] = defineData.transform.position[0] / scale;
      defineData.transform.position[1] = defineData.transform.position[1] / scale;
      // 原点缩放
      defineData.transform.anchor[0] = defineData.transform.anchor[0] / scale;
      defineData.transform.anchor[1] = defineData.transform.anchor[1] / scale;
    }

    // 背景图片（暂不做处理）
    if (meta.type == 'mask') {
      return meta;
    }

    // 身份栏
    if (meta.type == 'introduceCard') {
      defineData.height = defineData.height / scale;
      defineData.width = defineData.width / scale;
      // 处理标题位置   数组第0位为x轴，第1位为y轴  z轴不做处理
      defineData.transform.position[0] = (defineData.transform.position[0]) / scale;
      defineData.transform.position[1] = defineData.transform.position[1] / scale;

      // 原点缩放
      defineData.transform.anchor[0] = defineData.transform.anchor[0] / scale;
      defineData.transform.anchor[1] = defineData.transform.anchor[1] / scale;
      // 处理内边距
      if (defineData.padding) {
        defineData.padding.bottom = defineData.padding.bottom / scale;
        defineData.padding.top = defineData.padding.top / scale;
        defineData.padding.left = defineData.padding.left / scale;
        defineData.padding.right = defineData.padding.right / scale;
      }
      // 字体大小
      if (defineData.fontSize) {
        defineData.fontSize = defineData.fontSize / scale;
      }

      if (meta.materialId && meta.define.description.content) {
        meta.define.description.content = addLineBreaks(meta.define.description.content)
      }

      if (meta.materialId && meta.define.name.content) {
        meta.define.name.content = addLineBreaks(meta.define.name.content)
      }

      if (meta.materialId && meta.define.contents) {
        meta.contents = [addLineBreaks(meta.define.contents[0]), addLineBreaks(meta.define.contents[1])]
      }
    }

    if (meta.type == 'virtualman') {
      // 处理标题位置   数组第0位为x轴，第1位为y轴  z轴不做处理
      defineData.transform.position[0] = defineData.transform.position[0] / scale;
      defineData.transform.position[1] = defineData.transform.position[1] / scale;
      // 原点缩放
      defineData.transform.anchor[0] = defineData.width / 2;
      defineData.transform.anchor[1] = defineData.height / 2;

      // 倍率缩放
      defineData.transform.scalar[0] = defineData.transform.scalar[0] * meta.scale;
      defineData.transform.scalar[1] = defineData.transform.scalar[1] * meta.scale;
      meta.materialId = ''
    }
    return meta;
  })
  return newMetadata;
}

// 处理身份栏数据
export const handleIntroduceCardData = (scale, introduceCardData) => {


  let newData = introduceCardData.map(item => {
    let renderDefine = item.renderDefine;
    renderDefine.height = renderDefine.height * scale;
    renderDefine.width = renderDefine.width * scale;
    renderDefine.padding.bottom = renderDefine.padding.bottom * scale;
    renderDefine.padding.top = renderDefine.padding.top * scale;
    renderDefine.padding.left = renderDefine.padding.left * scale;
    renderDefine.padding.right = renderDefine.padding.right * scale;
    renderDefine.fontSize = renderDefine.fontSize * scale;
    return {
      ...item,
      renderDefine: renderDefine
    }
  })
  console.log(scale, newData, 'scale');
  return newData;
}

/**
 * 将文本每15个字符添加一个换行符
 * @param {string} text 需要处理的文本
 * @returns {string} 处理后的文本
 */
export const addLineBreaks = (text) => {
  if (!text) return '';

  // 将文本按每15个字符分段
  let result = '';

  for (let i = 0; i < text.length; i++) {
    result += text[i];
    if ((i + 1) % 15 === 0 && i < text.length - 1) {
      result += '\n';
    }
  }

  return result;
}