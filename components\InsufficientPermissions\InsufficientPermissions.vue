<template>
	<view>

		<view class="project_model_1" style="color: black; ">
			<view class="project_model_2" style="width: 85%;margin-top: -50rpx">
				<view style="font-weight: 600;font-size: 32rpx;text-align: center;">温馨提示</view>
				<view style="font-size: 28rpx;margin: 30rpx 0;color: #878787;text-align: center;">您暂时无此功能的权限,请联系我们进行开通</view>
				<view class="page_btn" style="margin-top: 50rpx">
					<view class="btn1" @click="close">关闭</view>
					<view class="btn2" @click="go_phoneMe">联系我们</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		data(){
			return{
			}
		},
		methods:{
			close(){
				this.$emit('closeModel',false)
			},
			go_phoneMe(){
				uni.navigateTo({
					url:'/subpkg/home_four/phoneMe'
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.page_btn {
		display: flex;
		justify-content: space-around;
		font-size: 28rpx;
	
		.btn1 {
			text-align: center;
			background: #e5e5e5;
			border-radius: 20rpx;
			width: 240rpx;
			padding: 25rpx 40rpx;
		}
	
		.btn2 {
			text-align: center;
			background: black;
			color: white;
			border-radius: 20rpx;
			width: 240rpx;
			padding: 25rpx 40rpx;
		}
	}
</style>