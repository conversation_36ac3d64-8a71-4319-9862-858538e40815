<template>
	<view class="page">


		<view style="display: flex;justify-content: space-around;">
			<video :src="page_src" controls :show-center-play-btn="false" :show-play-btn="true" :show-fullscreen-btn="false"
				:show-mute-btn="false" :show-progress="false" object-fit="cover" enable-progress-gesture="false"
				class="custom-video"></video>

		</view>


		<view style="">
			<view class="box_btn">
				<view class="btn1" @click="again">重新上传</view>
				<view class="btn2" @click="Confirm">我已确认</view>
			</view>
		</view>

		<view v-if="page_model" class="project_model_1" style="background: rgba(0, 0, 0, 0.9);">
			<view class="project_model_2" style="background: none;color: white;">
				<u-loading-icon mode="circle" size="80rpx"></u-loading-icon>
				<view style="text-align: center;margin-top: 30rpx;">视频正在上传···{{}}</view>
				<view style="text-align: center;margin: 20rpx 0;font-size: 24rpx;opacity: 0.8;">请勿熄屏或切换应用</view>
				<!--				<view style="display: flex;justify-content: space-around;">-->
				<!--					<view class="page_btn" @click="page_close">取消</view>-->
				<!--				</view>-->
			</view>
		</view>


	</view>
</template>

<script>
import COS from 'cos-wx-sdk-v5'
import {
	util
} from '../../../utils/file.js'
import {
	getUploadFileSts
} from '../../../api/numberUser/dr.js'
export default {
	data() {
		return {
			pageType: '',
			page_model: false,
			page_src: 'https://xiao-dr.oss-cn-beijing.aliyuncs.com/videos/1746503479263-y2Bc5qPd6bxl525dd8f0cb2ba2f63167449008c40875.mp4'
		}
	},
	onLoad(options) {
		this.pageType = options.type
		this.getOpenerEventChannel().on('get_path', (data) => {
			this.page_src = data.path
		})
	},
	methods: {
		again() {
			uni.navigateBack()
		},
		Confirm() {
			getUploadFileSts({
				scene: 'virtualman/authvideo',
				type: 'video',
				extName: 'mp4'
			}).then((res) => {
				console.log(res)
				this.uploadToCOS(res.data)
			})

		},
		async uploadToCOS(data) {
			this.page_model = true
			const {
				credentials,
				region,
				bucket,
				key,
				startTime,
				expiredTime
			} = data
			const cos = new COS({
				SecretId: credentials.tmpSecretId,
				SecretKey: credentials.tmpSecretKey,
				SecurityToken: credentials.sessionToken,
				StartTime: startTime,
				ExpiredTime: expiredTime
			});
			let that = this

			const arrayBuffer = await util.tempPathToArrayBuffer(that.page_src)
			// 上传文件
			cos.putObject({
				Bucket: bucket,
				/* 必须 */
				Region: region,
				/* 必须 */
				Key: key,
				/* 必须 */
				StorageClass: 'STANDARD', // 存储类型
				Body: arrayBuffer, // 上传文件对象
				onProgress: function (progressData) {
					console.log(JSON.stringify(progressData));
				}
			}, (err, data) => {
				this.page_model = false
				if (err) console.log('失败')
				else {
					uni.setStorageSync("authVideoUrl", `https://${bucket}.cos.${region}.myqcloud.com/${key}`)
					// that.$store.commit('setNumberLicensedVideos',
					// 	`https://${bucket}.cos.${region}.myqcloud.com/${key}`);
					uni.navigateBack({
						delta: 2
					})
					// uni.redirectTo({url: `/subpkg/index/numbe_user/index?type=${that.pageType}&currentTab=2`})
					// uni.navigateBack({url: `/subpkg/index/numbe_user/index?type=${that.pageType}&currentTab=2`})
				}
				console.log(err || data);
			});
		},

	}
}
</script>

<style scoped lang="scss">
.page {
	height: 100vh;
	background: black;

	.box_btn {
		width: 100vw;
		text-align: center;
		position: absolute;
		bottom: 40rpx;

		.btn1 {
			width: 40vw;
			padding: 20rpx 0;
			color: white;
			border: 2rpx white solid;
			text-align: center;
			border-radius: 20rpx;
			display: inline-block;
			margin-right: 40rpx;
		}

		.btn2 {
			width: 40vw;
			padding: 20rpx 0;
			color: black;
			border: 2rpx black solid;
			text-align: center;
			border-radius: 20rpx;
			background: white;
			display: inline-block;
		}
	}
}

.custom-video {
	width: 500rpx;
	height: 700rpx;
	margin-top: 40rpx;
}
</style>
