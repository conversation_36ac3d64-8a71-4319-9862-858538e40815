<template>
  <view class="wave-loader" :style="[{ '--bar-color': color, '--bar-width': barWidth, '--bar-spacing': barSpacing }, customStyle]">
    <view
        class="wave-bar"
        v-for="(bar, index) in 3"
        :key="index"
        :style="[{ animationDelay: `${index * 0.15}s` },barStyle]"
    ></view>
  </view>
</template>

<script>
export default {
  name: 'WaveLoading',
  props: {
    color: {
      type: String,
      default: '#ffffff'
    },
    barWidth: {
      type: String,
      default: '9rpx'
    },
    barSpacing: {
      type: String,
      default: '7rpx'
    },
    customStyle: {
      type: String,
      default: ''
    },
    barStyle:{
      type:String,
      default:''
    }
  }
}
</script>

<style lang="scss" scoped>
.wave-loader {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  margin: auto 0;
  width: 100%;
}

.wave-bar {
  width: var(--bar-width);
  height: 20rpx;
  margin-right: var(--bar-spacing);
  background: var(--bar-color, #ffffff);
  opacity: 0.2;
  animation: wave 0.7s ease-in-out infinite;
  transform-origin: center;
}

/* 动态设置颜色和宽度，使用变量覆盖 */
.wave-bar:last-child {
  margin-right: 0;
}

@keyframes wave {
  0%, 100% {
    transform: scaleY(0.5);
    opacity: 0.2;
  }
  50% {
    transform: scaleY(2);
    opacity: 1;
  }
}
</style>
