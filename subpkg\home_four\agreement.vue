<template>
  <view class="agreement-page">
    <scroll-view scroll-y class="content">
      <rich-text style="font-size: 26rpx;padding: 5rpx" :nodes="renderedHtml"></rich-text>
    </scroll-view>
  </view>
</template>

<script>
import {getUserAgreement} from "@/api/app/config";
import config from '@/config/index.js'
export default {
  data() {
    return {
      // 最终替换后渲染的 HTML 内容
      renderedHtml: '',
    }
  },
  onLoad(option) {
    // 先清空内容
    this.renderedHtml = '';
    this.setPageTitle(option.type);
    this.loadAgreement(option.type);
  },
  methods: {
    setPageTitle(type) {
      let title = '协议';
      if (type === 'userAgreement') {
        title = '用户协议';
      } else if(type==='policy'){
        title = '隐私协议';
      }
      uni.setNavigationBarTitle({ title });
    },
    async loadAgreement(type) {
      uni.showLoading({title:'加载中...'})
      getUserAgreement({type:type,appId:this.$appConfig.appInfo.appId}).then(res=>{
        this.renderedHtml = JSON.parse(res.data).content
      })
    },
    // 替换 {xxx} 占位符
    replacePlaceholders(template, data) {
      return template.replace(/\{(\w+)\}/g, (_, key) => data[key] || '');
    },
  },
};
</script>

<style scoped>
.agreement-page {
  padding: 20rpx;
}
.content {
  max-height: 100vh;
}
</style>
