<template>
  <view class="scale-slider-container">
    <view class="scale-value">{{ value }}%</view>
    <view class="slider-wrapper" @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd">
      <view class="slider-bar">
        <view class="slider-bar-active" :style="{ height: activeHeight + 'px' }"></view>
      </view>
      <view class="slider-button" :style="{ bottom: buttonPosition + 'px' }"></view>
      <view class="slider-touch-area" @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd"></view>
    </view>
    <view class="scale-reset" @click="resetScale">还原</view>
  </view>
</template>

<script>
export default {
  name: 'ScaleSlider',
  props: {
    // 当前值
    value: {
      type: Number,
      default: 100
    },
    // 最小值
    min: {
      type: Number,
      default: 50
    },
    // 最大值
    max: {
      type: Number,
      default: 150
    },
    // 步长
    step: {
      type: Number,
      default: 1
    },
    // 节流间隔，单位毫秒
    throttleTime: {
      type: Number,
      default: 50
    }
  },
  data() {
    return {
      isDragging: false,
      startY: 0,
      startValue: 0,
      // 滑动条高度 (需要在mounted中获取实际高度)
      sliderHeight: 280,
      // 节流相关
      lastEmitTime: 0,
      pendingValue: null
    }
  },
  computed: {
    // 计算按钮位置
    buttonPosition() {
      // 将value从[min, max]映射到[0, sliderHeight]
      return (this.value - this.min) / (this.max - this.min) * this.sliderHeight;
    },
    // 计算激活部分高度
    activeHeight() {
      return this.buttonPosition;
    }
  },
  mounted() {
    // 获取滑动条实际高度
    const query = uni.createSelectorQuery().in(this);
    query.select('.slider-wrapper').boundingClientRect(data => {
      if (data) {
        this.sliderHeight = data.height;
        console.log('滑动条高度:', this.sliderHeight);
      }
    }).exec();
  },
  methods: {
    // 触摸开始事件
    handleTouchStart(e) {
      this.isDragging = true;
      this.startY = e.touches[0].clientY;
      this.startValue = this.value;
      
      // 使用小程序兼容的方式获取元素位置
      const query = uni.createSelectorQuery().in(this);
      query.select('.slider-wrapper').boundingClientRect(data => {
        if (data) {
          // 获取触摸点相对于滑动条顶部的距离
          const touchY = e.touches[0].clientY - data.top;
          
          // 如果触摸点在滑动条范围内，直接调整到该位置
          if (touchY >= 0 && touchY <= data.height) {
            const touchRatio = 1 - (touchY / data.height); // 因为是从底部向上计算
            const newValue = this.min + touchRatio * (this.max - this.min);
            
            // 应用步长和限制
            const roundedValue = Math.max(this.min, Math.min(this.max, 
              Math.round(newValue / this.step) * this.step));
              
            if (roundedValue !== this.value) {
              this.emitValueWithThrottle(roundedValue);
            }
          }
        }
      }).exec();
      
      // 阻止页面滚动
      e.stopPropagation();
    },
    
    // 触摸移动事件
    handleTouchMove(e) {
      if (!this.isDragging) return;
      
      // 获取触摸点相对位置
      const moveY = e.touches[0].clientY - this.startY;
      
      // 反向移动 (向上增加，向下减少)
      const moveRatio = -moveY / this.sliderHeight;
      
      // 计算新值
      let newValue = this.startValue + moveRatio * (this.max - this.min);
      
      // 应用步长
      newValue = Math.round(newValue / this.step) * this.step;
      
      // 限制在最小和最大值之间
      newValue = Math.max(this.min, Math.min(this.max, newValue));
      
      // 使用节流发送值更新
      if (newValue !== this.value) {
        this.emitValueWithThrottle(newValue);
      }
      
      // 阻止页面滚动
      e.stopPropagation();
      
      // 在小程序环境中，preventDefault可能不可用，做安全检查
      if (e.preventDefault) {
        e.preventDefault();
      }
    },
    
    // 使用节流发送值更新
    emitValueWithThrottle(newValue) {
      const now = Date.now();
      
      // 记录待发送的值
      this.pendingValue = newValue;
      
      // 如果距离上次发送的时间小于节流时间，不立即发送
      if (now - this.lastEmitTime < this.throttleTime) {
        // 如果还没有设置节流定时器，设置一个
        if (!this.throttleTimer) {
          this.throttleTimer = setTimeout(() => {
            if (this.pendingValue !== null) {
              this.$emit('input', this.pendingValue);
              this.$emit('change', this.pendingValue);
              this.lastEmitTime = Date.now();
              this.pendingValue = null;
            }
            this.throttleTimer = null;
          }, this.throttleTime);
        }
      } else {
        // 如果已经过了节流时间，立即发送
        this.$emit('input', newValue);
        this.$emit('change', newValue);
        this.lastEmitTime = now;
        this.pendingValue = null;
      }
    },
    
    // 触摸结束事件
    handleTouchEnd(e) {
      this.isDragging = false;
      
      // 确保最后一个值被发送
      if (this.pendingValue !== null) {
        if (this.throttleTimer) {
          clearTimeout(this.throttleTimer);
          this.throttleTimer = null;
        }
        this.$emit('input', this.pendingValue);
        this.$emit('change', this.pendingValue);
        this.pendingValue = null;
      }
      
      // 在小程序环境中，安全地处理事件对象
      if (e) {
        if (e.stopPropagation) {
          e.stopPropagation();
        }
      }
    },
    
    // 重置比例
    resetScale() {
      this.$emit('input', 100);
      this.$emit('change', 100);
      this.$emit('reset');
    }
  }
}
</script>

<style scoped>
.scale-slider-container {
  position: fixed;
  top: 200rpx;
  left: 30rpx;
  width: 70rpx;
  height: 400rpx;
  background: #1f1f1f;
  border: 1rpx solid #3d3d3d;
  border-radius: 12rpx;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx 0;
  box-sizing: border-box;
}

.scale-value {
  font-size: 24rpx;
  color: white;
  text-align: center;
  font-weight: normal;
  margin-top: 10rpx;
  margin-bottom: 30rpx;
}

.slider-wrapper {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 100%;
  touch-action: none; /* 防止触摸事件引起页面滚动 */
}

.slider-touch-area {
  position: absolute;
  width: 50rpx; /* 宽一些，便于触摸 */
  height: 100%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 5;
}

.slider-bar {
  width: 6rpx;
  height: 100%;
  background-color: #2a2a2a;
  border-radius: 3rpx;
  position: relative;
  overflow: hidden;
  z-index: 10;
}

.slider-bar-active {
  width: 100%;
  background-color: #4D4D4D;
  position: absolute;
  bottom: 0;
}

.slider-button {
  width: 18rpx;
  height: 18rpx;
  border-radius: 50%;
  background-color: white;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  z-index: 15;
  /* 增大触摸区域，使用独立的伪元素选择器 */
}

/* 滑块按钮的触摸区域扩展 */
.slider-button::after {
  content: '';
  position: absolute;
  top: -15rpx;
  left: -15rpx;
  right: -15rpx;
  bottom: -15rpx;
}

.scale-reset {
  color: white;
  font-size: 24rpx;
  padding: 6rpx 0;
  text-align: center;
  margin-top: 30rpx;
  margin-bottom: 10rpx;
  opacity: 0.8;
}
</style> 