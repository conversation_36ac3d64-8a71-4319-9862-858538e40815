<template>
	<view class="page">
		<view class="card">
			<image src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/app_tab/project1_tran_dr_aduit.png"></image>
			<view class="text_1">审核中</view>
			<view class="text_2">您已经提交成功,审核通过后开始制作</view>
			<view style="text-align: left;display: inline-block;">
				<view class="text_3">审核预计需要一些时间，请耐心等待。</view>
				<view class="text_3">训练完成后，我们将通过通知告知您结果。</view>
<!--				<view class="text_3">您可以随时在“我的”页面查看申核和进度</view>-->
			</view>
		</view>
    <button class="btn" @click="toMe">去查看</button>
	</view>
</template>

<script>
	import {requestSubscribeRecord} from "@/utils/subscribeMsg";

  export default {
		data() {
			return {

			}
		},
    methods:{
      async toMe() {
        try {
          await requestSubscribeRecord(this.$appConfig.appInfo.wxTemplateId).catch(e => {
            console.log('订阅消息处理结果:', e); // todo 记录日志
          });
        } finally {
          uni.switchTab({
            url: '/pages/home_four/index'
          });
        }
      }
    }
	}
</script>

<style scoped lang="scss">
	.page{
		.card{
			text-align: center;
			margin-top: 100rpx;
			image{
				width: 300rpx;
				height: 300rpx;
			}
			.text_1{
				font-size: 32rpx;
				font-weight: 600;
				margin: 30rpx 20rpx;
			}
			.text_2{
				font-size: 26rpx;
				opacity: 0.8;
				margin-bottom: 60rpx;
			}
			.text_3{
				font-size: 26rpx;
				opacity: 0.8;
			}
			.text_3::before {
			  content: "";
			  display: inline-block;
			  width: 4px;
			  height: 4px;
			  background-color: black;
			  border-radius: 50%;
			  margin-right: 5px;
			  vertical-align: middle;
			  opacity: 0.8;
			}
		}
	}
  .btn{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 90%;
    border-radius: 20rpx;
    margin-top: 50rpx;
    background: black;
    color: white;

  }
</style>
