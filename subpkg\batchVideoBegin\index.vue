<template>
  <view class="works-page">
    <custom-navbar title="我的作品" :isBack="true" :isBlack="true"></custom-navbar>
    <view class="works-page-content">
      <!-- 作品信息区域 -->
      <view class="works-info">
        <text class="works-count">共计3个视频</text>
        <text class="works-date">2025-05-19 17:05:20</text>
      </view>

      <!-- 视频列表第一行 -->
      <view class="video-row">
        <!-- 第一个视频 -->
        <view class="video-card">
          <view class="video-overlay">
            <view class="video-header">
              <view class="duration-tag">
                <text class="duration-text">00:00:27</text>
              </view>
              <view class="play-button">
                <image class="play-icon"
                  src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG1c6a50e265fb5332c9f1fb2a8f65c59d.png" />
              </view>
            </view>

            <image class="play-large-icon" src="/static/播放按钮.png" />

            <view class="video-info">
              <text class="video-title">文明出行 人人有责</text>
              <text class="video-desc">随之生活水平的提高，旅...</text>
            </view>
          </view>
        </view>

        <!-- 第二个视频 -->
        <view class="video-card video-card-right">
          <view class="video-overlay">
            <view class="video-header">
              <view class="duration-tag">
                <text class="duration-text">00:00:30</text>
              </view>
              <view class="select-button"></view>
            </view>
            <image class="play-large-icon" src="/static/播放按钮.png" />

            <text class="video-title video-title-single">营养师教你三步吃出健康好身材，简单到不敢相信</text>
          </view>
        </view>
      </view>

      <!-- 视频列表第二行 -->
      <view class="video-single">
        <view class="video-overlay">
          <view class="video-header">
            <view class="duration-tag">
              <text class="duration-text">00:01:17</text>
            </view>
            <view class="select-button"></view>
          </view>
          <!-- 播放按钮 -->
          <image class="play-large-icon" src="/static/播放按钮.png" />

          <text class="video-title video-title-single">你以为汇率变化只是用户的事？其实你的钱包早就受...</text>
        </view>
      </view>
    </view>
    <!-- 底部操作按钮 -->
    <view class="action-buttons" :style="{ paddingBottom: safeAreaBottom + 'px' }">
      <view class="regenerate-btn" @click="goBatchVideoConfig">
        <text class="btn-text">再次生成</text>
      </view>
      <view class="download-btn">
        <text class="btn-text">批量下载</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      safeAreaBottom: 0
    };
  },
  onLoad() {
    this.safeAreaBottom = uni.getSystemInfoSync().safeAreaInsets.bottom + uni.upx2px(12);
  },
  methods: {
    goBatchVideoConfig() {
      uni.navigateTo({
        url: '/subpkg/batchVideoConfig/index'
      })
    }
  }
};
</script>

<style lang="scss">
.works-page {
  background-color: #f3f5f8;
  width: 100%;
  min-height: 100vh;



  .works-page-content {
    padding: 30rpx 32rpx 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    // 作品信息
    .works-info {
      margin-top: 32rpx;

      .works-count {
        display: block;
        font-size: 32rpx;
        color: #333333;
        font-weight: 600;
        line-height: 48rpx;
      }

      .works-date {
        display: block;
        font-size: 24rpx;
        color: #999999;
        font-weight: 500;
        line-height: 32rpx;
      }
    }

    // 视频行
    .video-row {
      display: flex;
      justify-content: space-between;
      margin-top: 24rpx;
      width: 100%;
    }

    // 视频卡片
    .video-card {
      width: 332rpx;
      height: 432rpx;
      border-radius: 16rpx;
      background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG5f9342fc1a18893ab6bd5471a220dd2a.png) no-repeat;
      background-size: cover;
      overflow: hidden;

      &.video-card-right {
        background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG1a99e040172baa3d2a297dc694eceba9.png) no-repeat;
        background-size: cover;
      }
    }

    // 单个视频
    .video-single {
      width: 332rpx;
      height: 432rpx;
      border-radius: 16rpx;
      margin-top: 24rpx;
      background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG16b8ee0563c63b6cc311d73d1418363c.png) no-repeat;
      background-size: cover;
      overflow: hidden;
    }

    // 视频遮罩
    .video-overlay {
      position: relative;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.3);
      border-radius: 16rpx;
      padding: 16rpx;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
    }

    // 视频头部信息
    .video-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 56rpx;
    }

    // 时长标签
    .duration-tag {
      background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG37130bbbc37ff0541c6dec6057ffe5e2.png) no-repeat;
      background-size: 100% 100%;
      width: 154rpx;
      height: 56rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .duration-text {
        color: #ffffff;
        font-size: 24rpx;
        font-weight: 500;
        text-align: center;
      }
    }

    // 播放按钮
    .play-button {
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
      background-image: linear-gradient(135deg, #07e3d2 0%, #01f770 100%);
      display: flex;
      align-items: center;
      justify-content: center;

      .play-icon {
        width: 24rpx;
        height: 24rpx;
      }
    }

    // 选择按钮
    .select-button {
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.3);
      border: 1px solid #ffffff;
    }

    // 大播放图标
    .play-large-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 64rpx;
      height: 64rpx;
      display: block;
    }

    // 视频信息
    .video-info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-top: auto;

      .video-title {
        color: #ffffff;
        font-size: 24rpx;
        font-weight: 500;
        line-height: 32rpx;
        margin-bottom: 8rpx;
      }

      .video-desc {
        color: rgba(255, 255, 255, 0.7);
        font-size: 24rpx;
        font-weight: 500;
        line-height: 32rpx;
      }
    }

    // 单行标题
    .video-title-single {
      margin-top: auto;
      margin-bottom: 24rpx;
      width: 298rpx;
      font-size: 24rpx;
      color: #ffffff;
      font-weight: 600;
      line-height: 32rpx;
    }
  }

  // 底部按钮区域
  .action-buttons {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    background-color: #ffffff;
    border-radius: 32rpx 32rpx 0 0;
    padding: 24rpx 48rpx;

    // 再次生成按钮
    .regenerate-btn {
      width: 310rpx;
      height: 96rpx;
      background-image: linear-gradient(135deg, #222328 19.837089%, #0f0f0f 100%);
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .btn-text {
        color: #ffffff;
        font-size: 36rpx;
        font-weight: 600;
      }
    }

    // 批量下载按钮
    .download-btn {
      width: 312rpx;
      height: 96rpx;
      background-image: linear-gradient(135deg, #07e3d2 0%, #01f770 100%);
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .btn-text {
        color: #333333;
        font-size: 36rpx;
        font-weight: 600;
      }
    }
  }
}
</style>