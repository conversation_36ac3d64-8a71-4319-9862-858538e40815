<template>
  <view class="page">
    <u-popup :show="model_show" @close="model_show = false" mode="center" :round="16">
      <view class="name_model">
        <view class="title">
          请填写数字人名称
        </view>
        <u--input placeholder="请填写数字人名称" border="surround" v-model="value" color="black"></u--input>
        <view class="btn_save" @click="save">
          确认
        </view>
      </view>
    </u-popup>

    <view style="display: flex;justify-content: space-around;">
      <video :src='video_url' :controls="false" :show-progress="false" :show-fullscreen-btn="false"
        :show-play-btn="false" :show-center-play-btn="false" :show-loading="false" object-fit="cover"
        class="card_center">
      </video>
    </view>


    <view style="font-size: 24rpx;color: #999999;margin-top: 20rpx;">
      为获得最佳、最逼真的数字人,请确认您的视频符合以下要求
    </view>


    <view :class="!item.check ? 'page_text' : 'page_text_2'" v-for="item in list" @click="check_(item)">
      <view>{{ item.name }}</view>


      <!-- <view v-if="!item.check" style="width: 36rpx;height: 36rpx;border: 2rpx white solid;border-radius: 4rpx;">
      </view>
      <view v-else>
        <u-icon name="checkbox-mark" color="#2fe62c" size="36rpx"></u-icon>
      </view>
       -->

      <view>
        <view class="check_1" v-if="!item.check">
        </view>
        <image class="check_2" v-else src="/static/index/changed.png">
        </image>
      </view>

    </view>

    <view class="page_btn">
      <view class="btn1" @click="again_upload">重新上传</view>
      <view v-if="count == 3" class="btn3" @click="model_show = true">我已确认</view>
      <view v-else class="btn2">我已确认</view>
    </view>


    <view v-if="page_model" class="project_model_1" style="background: rgba(0, 0, 0, 0.9);z-index: 999">
      <view class="project_model_2" style="background: none;color: white;padding: 40rpx;">
        <u-loading-icon mode="circle" size="80rpx"></u-loading-icon>

        <!-- 主要进度信息 -->
        <view style="text-align: center;margin-top: 30rpx;font-size: 32rpx;font-weight: bold;">
          视频正在上传 {{ uploadProgress.percent }}%
        </view>

        <!-- 进度条 -->
        <view style="margin: 30rpx 0;">
          <view style="background: rgba(255,255,255,0.2);height: 8rpx;border-radius: 4rpx;overflow: hidden;">
            <view style="background: #07c160;height: 100%;border-radius: 4rpx;transition: width 0.3s ease;"
              :style="{ width: uploadProgress.percent + '%' }"></view>
          </view>
        </view>

        <!-- 详细进度信息 -->
        <view style="font-size: 24rpx;opacity: 0.9;line-height: 1.6;">
          <view style="display: flex;justify-content: space-between;margin-bottom: 10rpx;">
            <text>已上传:</text>
            <text>{{ formatFileSize(uploadProgress.loaded) }} / {{ formatFileSize(uploadProgress.total) }}</text>
          </view>
          <view style="display: flex;justify-content: space-between;margin-bottom: 10rpx;">
            <text>上传速度:</text>
            <text>{{ uploadProgress.speedText }}</text>
          </view>
          <view style="display: flex;justify-content: space-between;margin-bottom: 10rpx;">
            <text>预估剩余:</text>
            <text>{{ uploadProgress.remainingTimeText }}</text>
          </view>
        </view>

        <view style="text-align: center;margin-top: 20rpx;font-size: 24rpx;opacity: 0.8;">
          请勿熄屏或切换应用
        </view>

        <!--        <view style="display: flex;justify-content: space-around;">-->
        <!--          <view class="page_btn" @click="page_close">取消</view>-->
        <!--        </view>-->
      </view>
    </view>

    <projectModel v-if="again_model" title="重新上传" content="是否确定重新上传" @btn_save="btn_save"
      @btn_close="again_model = false">
    </projectModel>

  </view>
</template>

<script>
import COS from 'cos-wx-sdk-v5'
import {
  util
} from '../../../utils/file.js'
import {
  getUploadFileSts
} from '../../../api/numberUser/dr.js'
import {
  getAppBaseInfo
} from '../../../api/app/config.js'

export default {
  data() {
    return {
      // fakeProgressTimer: null, // 移除假进度条定时器，现在使用真实上传进度
      cancelUpload: null,
      taskId: null, // 上传任务ID，用于暂停/恢复任务
      cos: null, // COS对象，在onLoad中初始化
      cosCredentials: null, // COS凭证信息，在onLoad中获取
      page_model: false,
      page_number: 0,
      // 网络重试相关
      isNetworkError: false, // 是否发生网络错误
      retryCount: 0, // 重试次数
      maxRetryCount: 5, // 最大重试次数
      retryTimer: null, // 重试定时器
      networkCheckTimer: null, // 网络检测定时器
      // 上传进度详细信息
      uploadProgress: {
        percent: 0, // 进度百分比
        speed: 0, // 上传速度 (bytes/s)
        loaded: 0, // 已上传字节数
        total: 0, // 总字节数
        remainingTime: 0, // 预估剩余时间 (秒)
        speedText: '', // 速度文本显示
        remainingTimeText: '', // 剩余时间文本显示
      },
      video_url: '',
      value: '',
      list: [{
        name: '说话前嘴巴闭合1秒',
        check: false
      },
      {
        name: '口齿清晰,声音洪亮,表情自然',
        check: false
      },
      {
        name: '脸部没有遮挡,没有侧脸,头部没有离开过画面',
        check: false
      },
      ],
      count: 0,
      page_current: 0,
      model_show: false,
      delta: 2,
      again_model: false
    }
  },
  watch: {
    list(newVal) {
      console.log(newVal)
    }
  },
  onHide() {
    // 页面隐藏时暂停上传任务
    this.pauseUploadTask();
  },
  onUnload() {
    // 页面销毁时暂停上传任务和清理网络监控
    this.pauseUploadTask();
    this.clearNetworkMonitoring();
  },
  async onLoad(option) {
    uni.showLoading({
      title: '加载中'
    });

    if (option && option.current) this.page_current = option.current

    if (option && option.type == 'lite') {
      this.delta = 2
    }
    this.video_url = this.$store.state.numberUserVideo

    // 在onLoad中获取凭证并创建COS对象
    await this.initCOSWithCredentials()
    uni.hideLoading()
  },
  methods: {
    // 在onLoad中获取凭证并初始化COS对象
    async initCOSWithCredentials() {
      try {
        console.log('开始获取COS凭证...')
        const res = await getUploadFileSts({
          scene: 'miniprogram-temp',
          type: 'video',
          extName: 'mp4'
        })

        const {
          credentials,
          region,
          bucket,
          key,
          startTime,
          expiredTime
        } = res.data

        // 存储凭证信息到data中，供后续上传使用
        this.cosCredentials = {
          credentials,
          region,
          bucket,
          key,
          startTime,
          expiredTime
        }

        // 创建COS对象
        this.createCOSInstance(credentials, startTime, expiredTime)

      } catch (error) {
        console.error('获取COS凭证失败:', error)
        uni.showToast({
          title: '初始化失败，请重试',
          icon: 'none'
        })
      }
    },

    // 创建COS对象
    createCOSInstance(credentials, startTime, expiredTime) {
      this.cos = new COS({
        SecretId: credentials.tmpSecretId,
        SecretKey: credentials.tmpSecretKey,
        SecurityToken: credentials.sessionToken,
        StartTime: startTime,
        ExpiredTime: expiredTime,
        EnableTracker: true,
        // 分块上传配置 - 针对视频文件优化
        SliceSize: 1024 * 1024 * 5, // 5MB分块大小，适合视频文件
        ChunkParallelLimit: 3, // 并发上传3个分块
        ChunkRetryTimes: 3, // 分块重试3次
        CopyChunkParallelLimit: 10, // 复制分块并发数
        ProgressInterval: 500, // 进度回调间隔500ms，更频繁的进度更新
      });
      console.log('COS对象创建成功')
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 格式化时间
    formatTime(seconds) {
      if (seconds === 0 || !isFinite(seconds)) return '计算中...';
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = Math.floor(seconds % 60);

      if (hours > 0) {
        return `${hours}小时${minutes}分钟${secs}秒`;
      } else if (minutes > 0) {
        return `${minutes}分钟${secs}秒`;
      } else {
        return `${secs}秒`;
      }
    },

    // 更新上传进度信息
    updateProgressInfo(progressData) {
      const { percent, speed, loaded, total } = progressData;

      // 计算剩余时间
      const remainingBytes = total - loaded;
      const remainingTime = speed > 0 ? remainingBytes / speed : 0;

      this.uploadProgress = {
        percent: Math.floor(percent * 100),
        speed: speed || 0,
        loaded: loaded || 0,
        total: total || 0,
        remainingTime: remainingTime,
        speedText: this.formatFileSize(speed || 0) + '/s',
        remainingTimeText: this.formatTime(remainingTime),
      };

      // 同时更新原有的page_number用于兼容
      this.page_number = this.uploadProgress.percent;
    },

    // 暂停上传任务
    pauseUploadTask() {
      if (this.cos && this.taskId) {
        try {
          console.log('暂停上传任务，taskId:', this.taskId);
          this.cos.pauseTask(this.taskId);
        } catch (error) {
          console.error('暂停上传任务失败:', error);
        }
      }
    },

    // 恢复上传任务
    resumeUploadTask() {
      if (this.cos && this.taskId) {
        try {
          console.log('恢复上传任务，taskId:', this.taskId);
          this.cos.restartTask(this.taskId);
        } catch (error) {
          console.error('恢复上传任务失败:', error);
        }
      }
    },

    // 检测网络连接状态
    async checkNetworkStatus() {
      try {
        // 获取小程序基础信息用于网络检测
        if (typeof uni !== 'undefined' && uni.getAccountInfoSync) {
          const info = uni.getAccountInfoSync();
          const appId = info.miniProgram.appId || '';

          // 使用getAppBaseInfo接口检测网络连接
          const res = await getAppBaseInfo({ appId });

          if (res && res.data) {
            console.log('网络连接正常，接口响应成功');
            return true;
          } else {
            console.log('网络连接异常，接口响应异常');
            return false;
          }
        } else {
          // 如果无法获取appId，使用备用检测方法
          console.log('无法获取appId，使用备用网络检测');
          return new Promise((resolve) => {
            uni.request({
              url: 'https://www.baidu.com',
              method: 'HEAD',
              timeout: 5000,
              success: () => {
                console.log('备用网络检测：连接正常');
                resolve(true);
              },
              fail: () => {
                console.log('备用网络检测：连接异常');
                resolve(false);
              }
            });
          });
        }
      } catch (error) {
        console.log('网络检测失败:', error);
        return false;
      }
    },

    // 判断是否为网络错误
    isNetworkRelatedError(error) {
      if (!error) return false;
      return error.err.error.indexOf('request:fail') !== -1
    },

    // 开始网络监控和自动重试
    startNetworkMonitoring() {
      if (this.networkCheckTimer) {
        clearInterval(this.networkCheckTimer);
      }

      console.log('开始网络监控，每3秒检测一次网络状态');

      this.networkCheckTimer = setInterval(async () => {
        if (this.isNetworkError && this.taskId) {
          console.log(`第${this.retryCount}次重试，检测网络状态...`);
          const isOnline = await this.checkNetworkStatus();

          if (isOnline) {
            console.log('网络已恢复，尝试继续上传');
            this.isNetworkError = false;
            this.resumeUploadTask();
            this.clearNetworkMonitoring();

            // 显示恢复提示
            // uni.showToast({
            //   title: '网络已恢复，继续上传',
            //   icon: 'success',
            //   duration: 2000
            // });
          } else {
            // 显示重试提示
            uni.showToast({
              title: `网络异常，正在重试(${this.retryCount}/${this.maxRetryCount})`,
              icon: 'loading',
              duration: 2000
            });
            console.log('网络仍未恢复，继续等待...');
          }
        }
      }, 3000); // 每3秒检测一次网络状态
    },

    // 清理网络监控
    clearNetworkMonitoring() {
      if (this.networkCheckTimer) {
        clearInterval(this.networkCheckTimer);
        this.networkCheckTimer = null;
      }
      if (this.retryTimer) {
        clearTimeout(this.retryTimer);
        this.retryTimer = null;
      }
    },

    // 处理上传错误并自动重试
    handleUploadError(error) {
      console.error('上传发生错误:', error);

      if (this.isNetworkRelatedError(error)) {
        console.log('检测到网络相关错误，开始自动重试机制');
        this.isNetworkError = true;

        if (this.retryCount < this.maxRetryCount) {
          this.retryCount++;
          console.log(`第${this.retryCount}次重试，最大重试次数：${this.maxRetryCount}`);

          // 暂停当前上传任务
          this.pauseUploadTask();

          // 开始网络监控
          this.startNetworkMonitoring();

          // 显示重试提示
          uni.showToast({
            title: `网络异常，正在重试(${this.retryCount}/${this.maxRetryCount})`,
            icon: 'loading',
            duration: 2000
          });

          console.log(`网络异常自动重试，当前第${this.retryCount}次，最大${this.maxRetryCount}次`);

          return true; // 表示错误已被处理，不需要终止上传
        } else {
          console.log('已达到最大重试次数，停止重试');
          uni.showToast({
            title: '网络异常，请检查网络后重新上传',
            icon: 'none',
            duration: 3000
          });
          this.clearNetworkMonitoring();
          return false; // 表示需要终止上传
        }
      } else {
        // 非网络错误，直接失败
        console.log('非网络相关错误，直接失败');
        uni.showToast({
          title: '上传失败: ' + (error.message || '未知错误'),
          icon: 'none'
        });
        return false;
      }
    },

    btn_save() {
      this.$store.commit('SetNumberUserVideo', '')

      uni.navigateBack({
        delta: this.delta - 1
      })
    },
    again_upload() {
      this.again_model = true
    },
    page_close() {
      // 暂停上传任务和清理网络监控
      this.pauseUploadTask();
      this.clearNetworkMonitoring();
      this.page_model = false
      this.cancelUpload = null
      this.taskId = null
      this.retryCount = 0
      this.isNetworkError = false
    },


    async uploadToCOS() {
      // 使用已经在onLoad中获取的凭证信息
      if (!this.cos || !this.cosCredentials) {
        console.error('COS对象或凭证未初始化')
        uni.showToast({
          title: 'COS未初始化，请重新进入页面',
          icon: 'none'
        })
        return
      }

      const {
        region,
        bucket,
        key
      } = this.cosCredentials

      let that = this
      console.log("准备上传腾讯cos")
      try {
        // 初始化进度信息和重试状态
        that.page_number = 0;
        that.retryCount = 0;
        that.isNetworkError = false;
        that.clearNetworkMonitoring(); // 清理之前的监控

        that.uploadProgress = {
          percent: 0,
          speed: 0,
          loaded: 0,
          total: 0,
          remainingTime: 0,
          speedText: '0 B/s',
          remainingTimeText: '计算中...',
        };

        const networkTimeoutTip = setTimeout(() => {
          if (that.page_model) {
            uni.showToast({
              title: '当前网络不佳，建议更换网络或耐心等待', icon: 'none', duration: 3000
            });
          }
        }, 30000);

        // 使用已创建的COS对象上传文件
        this.cos.uploadFile({
          Bucket: bucket,
          /* 必须 */
          Region: region,
          /* 必须 */
          Key: key,
          /* 必须 */
          StorageClass: 'STANDARD', // 存储类型
          SliceSize: 1024 * 1024 * 5,
          FilePath: this.$store.state.numberUserVideo,
          onTaskReady: function (taskId) {
            // 保存任务ID，用于暂停/恢复
            that.taskId = taskId;
            console.log('上传任务已准备，taskId:', taskId);
          },
          onProgress: function (progressData) {           // 使用真实上传进度
            console.log('视频上传进度:', JSON.stringify(progressData));
            // 更新详细的进度信息
            that.updateProgressInfo(progressData);
          },
          AsyncLimit: 3,
        }, (err, data) => {
          console.log(err);

          console.log(data, 'data');

          clearTimeout(networkTimeoutTip);

          if (err) {
            // 使用新的错误处理机制
            const shouldContinue = that.handleUploadError(err);
            if (shouldContinue) {
              // 如果是网络错误且在重试范围内，不关闭上传界面
              return;
            } else {
              // 其他错误或超过重试次数，关闭上传界面
              that.page_model = false;
              that.clearNetworkMonitoring();
              // 重置进度信息和任务ID
              that.uploadProgress = {
                percent: 0,
                speed: 0,
                loaded: 0,
                total: 0,
                remainingTime: 0,
                speedText: '',
                remainingTimeText: '',
              };
              that.page_number = 0;
              that.taskId = null;
              that.retryCount = 0;
              that.isNetworkError = false;
            }
          } else {
            // 上传成功，清理所有状态
            that.page_model = false;
            that.clearNetworkMonitoring();
            that.retryCount = 0;
            that.isNetworkError = false;
            that.taskId = null;

            console.log(`https://${bucket}.cos.${region}.myqcloud.com/${key}`);

            that.$store.commit('SetNumberUserVideo', `https://${bucket}.cos.${region}.myqcloud.com/${key}`);
            uni.setStorageSync('virtualmanName', that.value)
            uni.setStorageSync('crop', this.page_current)

            uni.navigateBack({
              delta: that.delta
            })
          }
          console.log(err || data);
        });
      } catch (error) {
        // 处理文件读取错误
        console.error('文件读取失败:', error);
        uni.showToast({
          title: '当前网络不佳，请切换网络或稍后重试',
          icon: 'none'
        });
        this.page_number = 0
      }
    },
    check_(item) {
      item.check = !item.check
      this.count = 0
      this.list.map(item => {
        if (item.check) this.count++
      })
    },
    go_save() {
      uni.navigateTo({
        url: '/subpkg/index/save_video/index'
      })
    },
    save() {
      if (!this.value || this.value.length > 10) {
        uni.showToast({
          title: '数字人名称在1到10字符以内',
          icon: 'none'
        });
        return; // 直接返回，避免后续逻辑执行
      }
      this.model_show = false
      this.page_model = true
      uni.setStorageSync('virtualmanName', this.value)

      // 直接使用已经在onLoad中创建的COS对象进行上传
      this.cancelUpload = this.uploadToCOS()

    }
  }
}
</script>

<style scoped lang="scss">
.page {
  height: 100vh;
  background: black;
  padding: 20rpx;

  .card_center {

    border: 4rpx solid;
    background-image: $project_1_bg;

    width: 400rpx;
    height: 600rpx;
    border-radius: 20rpx;
  }

  .project_btn {
    background: #3ee551;
    color: black;
    position: absolute;
    bottom: 80rpx;
  }

  .page_text {
    margin-top: 32rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 48rpx;

    height: 100rpx;

    color: white;
    margin: 20rpx 0;
    font-size: 24rpx;
    border: 2rpx #000000 solid;

    background: #222222;
    border-radius: 20rpx;
  }

  .page_text_2 {
    margin-top: 32rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 48rpx;
    height: 100rpx;
    color: white;
    margin: 20rpx 0;
    font-size: 24rpx;
    border: 2rpx #000000 solid;
    background: #222222;


    // box-shadow: 0 0 0 2px linear-gradient(135deg, rgba(7, 227, 210, 1), rgba(1, 247, 112, 1));
    border: 2rpx solid rgba(0, 170, 77, 1.0);
    border-radius: 20rpx;
    background: rgb(12, 60, 40);
  }

  .page_btn {
    display: flex;
    justify-content: space-around;
    margin-top: 60rpx;

    .btn1 {
      padding: 24rpx 100rpx;
      text-align: center;
      font-weight: 600;
      font-size: 32rpx;
      border-radius: 20rpx;

      background: white;
    }

    .btn2 {
      padding: 24rpx 100rpx;
      text-align: center;
      font-weight: 600;
      font-size: 32rpx;
      border-radius: 20rpx;

      background: white;


      color: #A8A8A8;

    }

    .btn3 {
      padding: 24rpx 100rpx;
      text-align: center;
      font-weight: 600;
      font-size: 32rpx;
      border-radius: 20rpx;

      background: $project_1_bg;
    }
  }
}

.check_1 {
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.3);
  border: 2rpx solid #FFFFFF;
  border-radius: 50%;
}


.check_2 {
  width: 40rpx;
  height: 40rpx;
}

.name_model {
  width: 654rpx;
  text-align: center;
  padding: 48rpx;
  color: #333333;

  .title {
    margin-bottom: 32rpx;
    font-weight: 600;
    font-size: 36rpx;
  }

  .btn_save {
    height: 96rpx;
    line-height: 96rpx;
    background: #EFEFEF;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    margin-top: 32rpx;
    font-weight: 600;
    font-size: 36rpx;
  }
}
</style>