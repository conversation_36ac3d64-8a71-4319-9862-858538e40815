<template>
  <view class="material-container">
    <view class="not-use-material" @click="selectMaterialType(0)">
      <view class="not-use-material-left">
        <image class="not-use-material-icon" src="/static/bushiyongsucai.png"></image>
        <text class="not-use-material-text">不使用素材</text>
      </view>
      <view class="ai-check-icon" v-if="selectedType === 0" style="margin-right: 48rpx;">
        <image src="/static/goxuanzhong.png" mode="aspectFit"></image>
      </view>
    </view>

    <view class="ai-material-selector">
      <view class="ai-material-item" @click="selectMaterialType(1)">
        <view class="ai-material-left">
          <image class="ai-material-icon" src="/static/aizhinengpeitu.png" mode="aspectFit"></image>
          <text class="ai-material-text">AI智能配图</text>
        </view>
        <view class="ai-check-icon" v-if="selectedType === 1">
          <image src="/static/goxuanzhong.png" mode="aspectFit"></image>
        </view>
      </view>

      <!-- 素材来源区域 -->
      <view class="material-source-section" :class="{ 'hidden': selectedType === 0 }" v-if="selectedType === 1">
        <view class="material-source-title">素材来源</view>

        <!-- 素材来源选项 -->
        <view class="material-source-options">
          <view class="material-source-option" :class="{ 'material-source-active': materialOrigin === 'user' }"
            @click="selectSourceType('user')">
            我的素材
          </view>
          <view class="material-source-option" :class="{ 'material-source-active': materialOrigin === 'official' }"
            @click="selectSourceType('official')">
            公共素材库
          </view>
          <!-- <view class="material-source-option"
            :class="{ 'material-source-active': materialOrigin === 'userAndOfficial' }"
            @click="selectSourceType('userAndOfficial')">
            我的素材+公共素材库
          </view> -->
        </view>

        <!-- AI 智能配图提示框 -->
        <view class="ai-prompt-box" v-if="materialOrigin === 'official'">
          <image class="ai-prompt-icon" src="/static/aitubiao.png" mode="aspectFit"></image>
          <view class="ai-prompt-text">
            系统将智能匹配公共素材库
          </view>
          <view class="ai-prompt-text">
            为您的视频智能上图片或视频素材
          </view>
        </view>

        <!-- 我的素材进行添加 -->
        <view class="material-scroll-container" v-if="materialOrigin === 'user'">
          <view class="my-material-add">
            <view class="my-material-add-box" @click="openAddSourcePopup">
              <image src="/static/添加素材.png" class="my-material-add-icon"></image>
              <text class="my-material-add-text">添加素材</text>
            </view>
            <!-- 遍历显示已添加的素材 -->
            <view class="my-material-add-image" v-for="item in materialList" :key="item.id">
              <!-- {{ (item.path).includes('.mp4') ? item.path + '?x-oss-process=video/snapshot,t_1000,f_jpg,w_800,h_450' :
                item.path }} -->
              <image
                :src="(item.path.indexOf('.mp4') !== -1) ? `${item.path}?x-oss-process=video/snapshot,t_1000,f_jpg,w_800,h_450` : item.path"
                class="material-image">
              </image>
              <image src="/static/删除.png" class="delete-icon" @click="deleteMaterial(item.id)"></image>
              <!-- 添加loading遮罩 -->
              <view class="loading-overlay" v-if="item.loading">
                <u-loading-icon mode="circle" size="30"></u-loading-icon>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 素材展示位置区域 -->
      <view class="display-position-section" :class="{ 'hidden': selectedType === 0 }" v-if="selectedType === 1">
        <view class="display-position-title">素材展示位置</view>

        <!-- 位置选择区域 -->
        <view class="display-position-options">
          <!-- 画面底部选项 -->
          <view class="display-bottom-option" :class="{ 'position-selected': positionType === 0 }"
            @click="selectPosition(0)">
            <view class="display-bottom-container">
              <image class="display-bottom-image" src="/static/meinv.png" mode="aspectFit"></image>
              <view class="display-bottom-label">
                <image src="/static/相册.png" class="icon"></image>
                <view>素材</view>
              </view>
            </view>
            <text class="display-bottom-text">画面底部</text>
            <view class="position-select-indicator" v-if="positionType === 0"></view>
          </view>

          <!-- 画面全屏选项 -->
          <view class="display-fullscreen-option" :class="{ 'position-selected': positionType === 1 }"
            @click="selectPosition(1)">
            <view class="display-fullscreen-container">
              <image class="display-fullscreen-image" src="/static/meinv.png" mode="aspectFit"></image>
              <view class="display-fullscreen-label">
                <image src="/static/素材全背景.png" class="display-fullscreen-background"></image>
                <image src="/static/相册.png" class="icon" style="z-index: 999;"></image>
                <text style="z-index: 999;">素材</text>
              </view>
            </view>
            <text class="display-fullscreen-text">画面全屏</text>
            <view class="position-select-indicator" v-if="positionType === 1"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 确定按钮 -->
    <view class="confirm-button-container">
      <view class="confirm-button" @click="confirmSelection">
        <text>确 定</text>
      </view>
    </view>

    <!-- 选择添加途径 -->
    <u-popup :show="showAddSourcePopup" mode="bottom" :round="20" @close="showAddSourcePopup = false">
      <view class="add-source-popup">
        <view class="add-source-header">
          <text class="add-source-title">请选择添加途径</text>
          <image src="/static/关闭.png" class="add-source-close" @click="showAddSourcePopup = false"></image>
        </view>

        <view class="add-source-option" @click="handleSourceOptionClick('library')">
          <text>从「我的素材库」中选择</text>
        </view>

        <view class="add-source-option" @click="handleSourceOptionClick('channel')">
          <text>从「我加入的频道」中选择</text>
        </view>

        <view class="add-source-option" @click="handleSourceOptionClick('upload')">
          <text>上传视频/图片</text>
        </view>
      </view>
    </u-popup>

    <!-- 频道选择器组件 -->
    <channel-selector :show="channelSelectorVisible" :channelList="channelList" @close="channelSelectorVisible = false"
      @select="handleChannelSelect">
    </channel-selector>
  </view>
</template>

<script>
import { handleMediaUpload } from "@/utils/upload-media.js";
import { getMeterialResult } from "@/api/myMaterials";
import { getGroup } from '@/api/numberUser/copy.js'
import ChannelSelector from '@/subpkg/index/components/channel.vue'

export default {
  components: {
    ChannelSelector
  },
  data() {
    return {
      // 选择的素材类型：0-不使用素材，1-AI智能配图
      selectedType: 1,
      // 展示位置：0-画面底部，1-画面全屏
      positionType: 0,
      // 预览图片路径
      previewImages: {
        model1: '/static/home/<USER>/model1.png',
        model2: '/static/home/<USER>/model2.png'
      },
      materialOrigin: 'user',
      // 添加途径弹窗控制
      showAddSourcePopup: false,
      // 素材列表
      materialList: [],
      // 安全区域高度
      safeArea: 0,
      // 用户选择的素材
      userMaterialInfo: [],
      // 轮询计时器
      pollTimers: {},
      // 最大轮询次数
      maxPollCount: 30,
      // 频道选择相关
      channelSelectorVisible: false,
      channelList: [],
    }
  },
  // 页面加载时执行
  onLoad(options) {
    // 如果有传入参数，则初始化选择状态
    if (options) {
      this.selectedType = options.type ? parseInt(options.type) : 1;
      this.materialOrigin = options.materialOrigin || 'user';
      this.positionType = options.position ? parseInt(options.position) : 0;
    }

    // 从事件通道获取已选择的素材数据
    const eventChannel = this.getOpenerEventChannel();
    eventChannel.on('init-material-data', (data) => {
      console.log('接收到之前选择的素材数据:', data);

      if (data) {
        // 设置素材位置
        if (data.materialPosition === 'bottom') {
          this.positionType = 0;
        } else if (data.materialPosition === 'full-screen') {
          this.positionType = 1;
        }

        // 设置素材来源
        this.materialOrigin = data.materialOrigin || 'user';

        // 如果有用户素材信息，添加到列表中
        if (data.userMaterialInfo && data.userMaterialInfo.length > 0) {
          this.userMaterialInfo = data.userMaterialInfo;

          // 创建素材列表项
          this.materialList = data.userMaterialInfo.map(item => ({
            id: item.id,
            path: item.coverUrl || item.fileUrl,
            type: item.type
          }));
        }
      }
    });

    // 监听从我的素材库返回的选中素材
    uni.$on('user-material-selected', (materialData) => {
      console.log('从素材库接收到选中的素材:', materialData);
      this.userMaterialInfo.push(...materialData);

      // 如果收到素材，自动切换到用户素材模式
      if (materialData && materialData.length > 0) {
        this.materialOrigin = 'user';

        // 清空之前的素材列表
        // this.materialList = [];

        // 将选中的素材添加到列表中
        materialData.forEach(item => {
          this.materialList.push({
            id: item.id,
            path: item.coverUrl,
            type: item.type
          });
        });
      }
    });

    // 获取频道列表
    this.getChannelList();
  },
  // 页面卸载时执行
  onUnload() {
    // 移除事件监听器
    uni.$off('user-material-selected');

    // 清除所有轮询计时器
    Object.keys(this.pollTimers).forEach(id => {
      clearInterval(this.pollTimers[id]);
    });
    this.pollTimers = {};
  },
  methods: {
    // 选择素材类型
    selectMaterialType(type) {
      // 如果点击的是"不使用素材"，则设置selectedType为0
      if (type === 0) {
        this.selectedType = 0;
        // 保持显示素材区域但添加隐藏类，以实现动画效果
        // 不需要移除v-if，因为CSS动画会处理隐藏效果
      } else {
        this.selectedType = 1;
      }
      console.log(`选择素材类型: ${type === 0 ? '不使用素材' : 'AI智能配图'}`);
    },

    // 选择素材来源
    selectSourceType(type) {
      this.materialOrigin = type;
    },

    // 选择展示位置
    selectPosition(position) {
      this.positionType = position;
      console.log(`选择展示位置: ${position === 0 ? '画面底部' : '画面全屏'}`);
    },

    // 打开添加途径弹窗
    openAddSourcePopup() {
      this.showAddSourcePopup = true;
    },

    // 获取频道列表
    getChannelList() {
      getGroup().then(res => {
        this.channelList = res.data;
      }).catch(err => {
        console.error('获取频道列表失败:', err);
      });
    },

    // 处理添加途径选项点击
    handleSourceOptionClick(source) {
      this.showAddSourcePopup = false;

      switch (source) {
        case 'library':
          // 从我的素材库中选择
          console.log('跳转到我的素材库选择');
          // 跳转到素材库，并传递选择模式参数
          uni.navigateTo({
            url: '/subpkg/myMaterials/index?select=true&maxSelect=9&returnSource=selectMaterial'
          });
          break;
        case 'channel':
          // 从我加入的频道中选择
          this.channelSelectorVisible = true;
          break;
        case 'upload':
          // 上传视频/图片
          this.uploadMedia();
          break;
      }
    },

    // 处理频道选择
    handleChannelSelect(channel) {
      // 隐藏频道选择弹窗
      this.channelSelectorVisible = false;

      // 跳转到message页面，传入频道信息，并指定只显示素材
      uni.navigateTo({
        url: '/subpkg/home_three/message?from=selectMaterial',
        success: (res) => {
          // 通过事件通道传送数据
          res.eventChannel.emit('get_message', {
            ...channel,
            selectMode: true // 添加标记，表示是选择素材模式
          });

          // 监听返回的素材
          res.eventChannel.on('selected_materials', (materials) => {
            if (materials && materials.length) {
              console.log('从频道选择的素材:', materials);

              // 处理返回的素材
              materials.forEach(item => {
                // 将素材添加到素材列表
                this.materialList.push({
                  id: item.outId || item.id,
                  path: item.content,
                  type: 'image' // 默认为图片类型，可根据实际情况调整
                });

                // 添加到用户素材信息
                this.userMaterialInfo.push({
                  id: item.outId || item.id,
                  outId: item.outId || item.id,
                  coverUrl: item.content,
                  fileUrl: item.content,
                  type: 'image' // 默认为图片类型，可根据实际情况调整
                });
              });


            }
          });
        }
      });
    },

    // 上传媒体文件
    async uploadMedia() {
      try {
        uni.showLoading({
          title: '文件上传中...'
        });

        // 调用上传方法
        const { fileUrl, materialId } = await handleMediaUpload();

        console.log(fileUrl, materialId, 'materialId');


        uni.hideLoading();

        if (fileUrl) {
          // 将选中的图片添加到素材列表中，设置loading状态
          const newMaterial = {
            id: materialId,
            path: fileUrl,
            type: 'image', // 根据文件类型可能需要判断
            loading: true
          };

          this.materialList.push(newMaterial);

          // 开始轮询检查上传结果
          this.startPolling(materialId);
        }
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '上传失败，请重试',
          icon: 'none'
        });
        console.error('上传失败:', error);
      }
    },

    // 开始轮询检查保存结果
    startPolling(materialId) {
      let pollCount = 0;

      // 创建轮询定时器
      this.pollTimers[materialId] = setInterval(async () => {
        try {
          // 轮询次数+1
          pollCount++;

          // 超过最大轮询次数，显示失败
          if (pollCount > this.maxPollCount) {
            clearInterval(this.pollTimers[materialId]);
            delete this.pollTimers[materialId];

            // 查找素材并标记失败
            const materialIndex = this.materialList.findIndex(item => item.id === materialId);
            if (materialIndex !== -1) {
              this.materialList[materialIndex].loading = false;
              this.materialList[materialIndex].failed = true;

              uni.showToast({
                title: '素材处理失败',
                icon: 'none'
              });
            }
            return;
          }

          // 获取素材保存结果
          const result = await getMeterialResult(materialId);
          console.log('轮询素材结果:', result);

          // 如果处理完成
          if (result.data) {
            clearInterval(this.pollTimers[materialId]);
            delete this.pollTimers[materialId];

            // 查找素材并更新状态
            const materialIndex = this.materialList.findIndex(item => item.id === materialId);
            if (materialIndex !== -1) {
              this.materialList[materialIndex].loading = false;

              // 更新素材信息
              if (result.data.fileUrl) {
                this.materialList[materialIndex].path = this.materialList[materialIndex].path;
                this.materialList[materialIndex].id = result.data.outId;
                result.data.id = result.data.outId;
              }
              console.log(this.materialList[materialIndex], 'materialList');

              // 添加到用户素材信息
              this.userMaterialInfo.push(result.data);
            }

            // 检查是否所有素材都已处理完成
            this.checkAllMaterialsReady();
          }
        } catch (error) {
          console.error('轮询素材状态失败:', error);
        }
      }, 3000); // 每3秒轮询一次
    },

    // 检查所有素材是否都已准备好
    checkAllMaterialsReady() {
      const allReady = this.materialList.every(material => !material.loading);
      if (allReady) {
        console.log('所有素材都已处理完成');
      }
    },

    // 选择媒体文件 - 替换原来的chooseMedia方法
    chooseMedia() {
      // 直接调用uploadMedia方法
      this.uploadMedia();
    },

    // 删除素材
    deleteMaterial(id) {
      const index = this.materialList.findIndex(item => item.id === id);
      if (index !== -1) {
        // 如果有轮询计时器，清除它
        if (this.pollTimers[id]) {
          clearInterval(this.pollTimers[id]);
          delete this.pollTimers[id];
        }

        // 从列表中移除
        this.materialList.splice(index, 1);

        // 也从userMaterialInfo中移除
        const materialInfoIndex = this.userMaterialInfo.findIndex(item => item.id === id);
        if (materialInfoIndex !== -1) {
          this.userMaterialInfo.splice(materialInfoIndex, 1);
        }
      }
    },

    // 确认选择
    confirmSelection() {
      // 构建符合要求的参数格式
      const materialPosition = this.selectedType === 0 ? 'none' :
        (this.positionType === 0 ? 'bottom' : 'full-screen');

      // 获取对应的文字描述
      let positionText = '';
      let typeText = '';

      if (this.selectedType === 0) {
        typeText = '不使用素材';
        positionText = '';
      } else {
        typeText = 'AI智能配图';
        positionText = this.positionType === 0 ? '画面底部' : '画面全屏';
      }

      // 素材来源文字
      let originText = '';
      if (this.materialOrigin === 'official') {
        originText = '公共素材库';
      } else if (this.materialOrigin === 'user') {
        originText = '我的素材';
      } else if (this.materialOrigin === 'userAndOfficial') {
        originText = '我的素材+公共素材库';
      }

      // 确保用户素材信息完整
      const processedUserMaterialInfo = this.userMaterialInfo.map(item => ({
        id: item.id,
        outId: item.outId || item.id,
        coverUrl: item.coverUrl || item.path,
        fileUrl: item.fileUrl || item.path,
        type: item.type || 'image'
      }));

      // 返回选择结果
      const result = {
        // 是否使用AI
        useAI: this.selectedType === 1,
        // 素材位置：bottom-底部，full-screen-全屏，none-关闭智能匹配素材
        materialPosition: materialPosition,
        // 素材来源：official-官方素材库，user-自定义素材
        materialOrigin: this.materialOrigin,
        // 文字描述
        typeText: typeText,
        positionText: positionText,
        originText: originText,
        // 完整显示文字
        displayText: this.selectedType === 0 ? typeText : `${typeText}(${originText}, ${positionText})`,
        // 用户选择的素材信息
        userMaterialInfo: processedUserMaterialInfo
      };

      console.log('确认选择:', result);

      // 向父组件传递结果
      this.$emit('confirm', result);

      // 通过事件总线通知父页面
      uni.$emit('material-selected', result);

      // 返回上一页
      uni.navigateBack({
        delta: 1
      });
    }
  }
}
</script>

<style scoped lang="scss">
// 整体容器
.material-container {
  min-height: 100vh;
  width: 100%;
  padding: 40rpx;
  box-sizing: border-box;
  background-color: #F3F5F8;
}

// 不使用素材选项
.not-use-material {
  background-color: #F3F5F8;
  height: 128rpx;
  width: 100%;
  padding: 24rpx 0 24rpx 48rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.not-use-material-left {
  display: flex;
  align-items: center;
}

.not-use-material-icon {
  width: 80rpx;
  height: 80rpx;
}

.not-use-material-text {
  margin-left: 24rpx;
  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
  font-style: normal;
  flex-grow: 1;
}

// AI智能配图区域
.ai-material-selector {
  margin-top: 16rpx;
  background-color: #fff;
  padding: 32rpx;
  box-sizing: border-box;
  border-radius: 24rpx;
}

.ai-material-item {
  width: 100%;
  height: 112rpx;
  background-color: #fff;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  padding: 0 16rpx;
  box-sizing: border-box;
  justify-content: space-between;
  border-bottom: 1rpx solid #e7e7e7;
}

.ai-material-left {
  display: flex;
  align-items: center;
}

.ai-material-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 16rpx;
}

.ai-material-text {
  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
}

.ai-check-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: #00d16c;
  border-radius: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.ai-check-icon image {
  width: 100%;
  height: 100%;
}

// 素材来源区域
.material-source-section {
  background-color: #fff;
  max-height: 500rpx;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
  opacity: 1;
}

.material-source-section.hidden {
  max-height: 0;
  opacity: 0;
}

.material-source-title {
  margin-top: 32rpx;
  font-weight: 600;
  font-size: 32rpx;
  color: #333333;
}

// 素材来源选项
.material-source-options {
  padding: 0 8rpx;
  margin-top: 16rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 555rpx;
  height: 64rpx;
  background-color: #F3F5F8;
  border-radius: 40rpx;
}

.material-source-option {
  padding: 10rpx 24rpx;
  border-radius: 32rpx;
  font-size: 24rpx;
  color: #999999;
}

.material-source-active {
  background-color: #000;
  color: #fff;
}

// AI智能配图提示框
.ai-prompt-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 192rpx;
  background: #F3F5F8;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}


.ai-prompt-icon {
  width: 64rpx;
  height: 64rpx;
}

.ai-prompt-text {
  font-weight: 500;
  font-size: 20rpx;
  color: #999999;
  margin-top: 8rpx;
}

.material-scroll-container {
  width: 100%;
  max-height: 400rpx;
  overflow-y: auto;
  padding-bottom: 20rpx;
  margin-top: 32rpx;
  -webkit-overflow-scrolling: touch; /* iOS流畅滚动 */
  scrollbar-width: thin; /* 细滚动条 */
}

.material-scroll-container::-webkit-scrollbar {
  width: 6rpx;
}

.material-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10rpx;
}

.material-scroll-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10rpx;
}

.my-material-add {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(auto-fill, 160rpx);
  /* 固定每列宽度为160rpx */
  gap: 20rpx;
  /* 设置元素间距 */
  justify-content: start;
  /* 确保左对齐 */
  padding-right: 10rpx; /* 增加右侧填充，避免与滚动条重叠 */
  padding-bottom: 80rpx;
}

.my-material-add-box {
  width: 160rpx;
  height: 160rpx;
  background-color: #EFEFEF;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .my-material-add-icon {
    width: 48rpx;
    height: 48rpx;
  }

  .my-material-add-text {
    font-size: 24rpx;
    color: #999999;
  }
}

.my-material-add-image {
  width: 160rpx;
  height: 160rpx;
  background-color: #EFEFEF;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .delete-icon {
    width: 48rpx;
    height: 48rpx;
    position: absolute;
    top: 0;
    right: 0;
    filter: brightness(0) invert(1);
  }
}

// 素材展示位置区域
.display-position-section {
  margin-top: 16rpx;
  max-height: 500rpx;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
  opacity: 1;
}

.display-position-section.hidden {
  max-height: 0;
  opacity: 0;
}

.display-position-title {
  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
}

// 素材展示位置选项容器
.display-position-options {
  margin-top: 16rpx;
  display: flex;
  justify-content: space-between;
  padding: 32rpx;
  background-color: #F3F5F8;
  border-radius: 24rpx;
}

// 画面底部选项
.display-bottom-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding-bottom: 10rpx;
}

.display-bottom-container {
  width: 256rpx;
  height: 344rpx;
  border-radius: 10rpx;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  margin-bottom: 10rpx;
  background-color: #e6f7ff;
}

.display-bottom-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.display-bottom-label {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 136rpx;
  background: url('/static/素材底部背景.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  color: #333333;

  .icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 0;
  }
}

.display-bottom-label image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.display-bottom-label text {
  font-size: 24rpx;
  color: #666;
}

.display-bottom-text {
  font-size: 24rpx;
  color: #666666;
  text-align: center;
}

// 画面全屏选项
.display-fullscreen-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding-bottom: 10rpx;
}

.display-fullscreen-container {
  width: 256rpx;
  height: 344rpx;
  border-radius: 10rpx;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
  background-color: #e6f7ff;
}

.display-fullscreen-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.display-fullscreen-label {
  position: absolute;
  width: 256rpx;
  height: 344rpx;

  left: 0;
  top: 0;
  display: flex;
  flex-direction: column;
  z-index: 5;
  justify-content: center;
  align-items: center;

  .icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 0;
  }

  .display-fullscreen-background {
    position: absolute;
    width: 256rpx;
    height: 344rpx;
    margin-right: 0;
  }
}

.display-fullscreen-label image {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.display-fullscreen-label text {
  font-size: 24rpx;
  color: #666;
}

.display-fullscreen-text {
  font-weight: 600;
  font-size: 24rpx;
  color: #666666;
  text-align: center;
}

// 确定按钮容器
.confirm-button-container {
  margin-top: auto;
  padding: 0;
  position: relative;
}

// 确定按钮
.confirm-button {
  width: 100%;
  height: 96rpx;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  background-color: #000;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-top: 26rpx;
}

.confirm-button text {
  font-size: 34rpx;
  color: #FFFFFF;
}

.button-size-text {
  position: absolute;
  bottom: 10rpx;
  font-size: 22rpx;
  font-weight: normal;
  color: #ff414c;
}

// 选择指示器
.position-select-indicator {
  position: absolute;
  bottom: 0;
  width: 60rpx;
  height: 6rpx;
  background-color: #00d16c;
  border-radius: 3rpx;
}

// 选中状态样式
.position-selected {

  .display-bottom-text,
  .display-fullscreen-text {
    color: #00d16c;
    font-weight: 700;
  }
}

/* 添加途径弹窗样式 */
.add-source-popup {
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 40rpx 40rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
}

.add-source-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  border-bottom: 1rpx solid #F3F5F8;
}

.add-source-title {
  font-weight: 600;
  font-size: 32rpx;
  color: #333333;
  line-height: 48rpx;
}

.add-source-close {
  width: 48rpx;
  height: 48rpx;
}

.add-source-option {
  padding: 32rpx 48rpx;
  background-color: #F3F5F8;
  margin: 16rpx 40rpx;
  border-radius: 16rpx;
}

.add-source-option text {
  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
}

.add-source-bottom {
  width: 100%;
  height: 68rpx;
  background-color: #F3F5F8;
  margin-top: 40rpx;
}

/* 素材图片样式 */
.material-image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
  object-fit: cover;
}

/* 添加loading遮罩样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9;
}
</style>