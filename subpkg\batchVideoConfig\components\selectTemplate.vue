<template>
  <view class="template-selection">
    <!-- 全选/取消全选区域 -->
    <view class="selection-header">
      <text class="select-all" @click="selectAll">全选</text>
      <text class="cancel-select" @click="cancelAll" v-show="selectedCount > 0">取消全部选中</text>
    </view>

    <!-- 模板列表网格 -->
    <scroll-view class="template-scroll" scroll-y @scrolltolower="loadMore"
      :style="{ height: templateScrollHeight + 'px' }">
      <view class="template-grid">
        <!-- 骨架屏 -->
        <template v-if="loading && templates.length === 0">
          <view class="template-card-skeleton" v-for="i in 6" :key="'skeleton-' + i">
            <view class="template-card-content-skeleton">
              <view class="skeleton-animation"></view>
            </view>
            <view class="template-name-skeleton">
              <view class="skeleton-animation"></view>
            </view>
          </view>
        </template>

        <!-- 实际模板 -->
        <template v-else>
          <view class="template-card" v-for="(template, index) in templates" :key="template.id">
            <view class="template-card-content" @click="handleToggleSelect(index, template)">
              <image class="template-card-img" :src="template.preUrl" />
              <view :class="['check-icon', { 'active': template.selected }]">
                <image v-if="template.selected" class="check-mark" src="/static/goxuanzhong.png" />
              </view>
            </view>
            <view class="template-name-container">
              <view class="template-name" @click.stop="openVoiceSelector(template)">
                <text>{{ getVoiceName(template.voiceId) }}</text>
                <image style="width: 24rpx;height: 24rpx;margin-left: 4rpx;" src="/static/右边箭头.png"></image>
              </view>
              <view class="voice-badge" v-if="template.voiceId">
                <text class="voice-name"></text>
                <!-- <text class="voice-speed">{{ template.speed }}倍速</text> -->
              </view>
            </view>
          </view>
        </template>
      </view>

      <!-- 底部加载状态 -->
      <view class="loading-status">
        <!-- 加载更多的骨架屏 -->
        <view v-if="loading && templates.length > 0" class="loading-more-skeleton">
          <view class="loading-more-line">
            <view class="skeleton-animation"></view>
          </view>
        </view>

        <view class="loading-text" v-else-if="!hasMore && templates.length > 0">没有更多数据了</view>
        <view class="loading-text" v-else-if="!hasMore && templates.length === 0">暂无模板数据</view>
        <view class="loading-text" v-else>上拉加载更多</view>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="action-bar" :style="{ paddingBottom: safeAreaBottom + 'px' }">
      <!-- 已选数量 -->
      <view class="selected-count" @click="() => showPopup = true">
        <view class="count-wrapper">
          <text class="count-number">{{ selectedCount }}</text>
          <text class="count-label">已选</text>
        </view>
      </view>

      <!-- 下一步按钮 -->
      <view class="next-step-btn" @click="nextStep">
        <text class="btn-text">下一步</text>
      </view>
    </view>

    <!-- 信息弹窗 -->
    <u-popup :show="showPopup" :round="20" @close="() => showPopup = false">
      <view class="popup-content" style="height: 60vh">

      </view>
    </u-popup>

    <!-- 音乐选择弹窗 -->
    <u-popup :show="musicModelBottom" @close="musicModelBottom = false" :round="20">
      <view class="page_modelShow">
        <view class="model_sound" style="margin-bottom: 20rpx;">
          <view class="model_title">语速: {{ musicValue.toFixed(1) }}</view>
          <view>
            <view style="display: flex;justify-content: space-between;align-items: center;">
              <view>慢速</view>
              <view style="width: 70vw;">
                <u-slider v-model="musicValue" activeColor="#2cdd8a" step="0.1" min="0.7" max="1.3"
                  blockColor="#21BD74"></u-slider>
              </view>
              <view>快速</view>
            </view>
          </view>
        </view>

        <view class="model-box-list">
          <view v-for="item in musicList" :key="item.outId" :class="{
            'model_box': currentVoice !== item.outId,
            'model_box_active': currentVoice === item.outId
          }" style="justify-content: space-between;" @click="selectVoice(item)">
            <view style="display: flex;align-items: center;">
              <!-- 播放按钮 -->
              <view class="preview-audio" @click.stop>
                <image v-if="playingVoiceId === item.outId" @click="stopPreviewVoice()"
                  src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/app_tab/project1_home4_music_playing.gif"
                  class="preview-icon"></image>
                <image v-else @click="previewVoice(item)" src="/static/home/<USER>/get_radio_bf.png"
                  class="preview-icon"></image>
              </view>
              <view class="model_voicelist">
                <view style="display: flex;align-items: center;">
                  <view>{{ item.name }}</view>
                  <image v-if="item.type === 'pro'" mode="heightFix" style="height: 30rpx;margin-left: 10rpx;"
                    src="/static/index/pro_vip.png"></image>
                </view>
              </view>
            </view>
            <image v-if="currentVoice === item.outId" src="/static/index/changed.png"
              style="width: 40rpx;height: 40rpx;">
            </image>
          </view>
        </view>

        <view class="project_btn" @click="confirmVoice">确定</view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { getTemplateList } from '@/api/numberUser/userType'
import { getVoiceList } from '@/api/numberUser/userType'

export default {
  data() {
    return {
      templates: [],
      // 分页相关
      pageNum: 1,
      pageSize: 999,
      total: 0,
      hasMore: true,
      loading: false,
      templateScrollBottom: 0,
      safeAreaBottom: 0,
      templateScrollHeight: 0,
      showPopup: false,

      // 音乐弹窗相关
      musicModelBottom: false,
      musicList: [],
      musicValue: 1,
      currentVoice: '',
      currentTemplate: null,

      // 音频播放相关
      playingVoiceId: '',
      innerAudioContext: null
    };
  },
  props: {
    selectedTemplates: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    selectedCount() {
      return this.templates.filter(item => item.selected).length;
    }
  },
  watch: {
    selectedTemplates: {
      handler(newVal) {
        if (this.templates.length > 0) {
          // 获取所有已选模板的ID
          const selectedIds = newVal.map(template => template.id);

          // 更新模板的选中状态
          this.templates.forEach(template => {
            template.selected = selectedIds.includes(template.id);
          });
        }
      },
      immediate: true
    }
  },
  created() {
    // 创建音频上下文
    this.innerAudioContext = uni.createInnerAudioContext({
      useWebAudioImplement: true
    });
    // 监听音频播放结束事件
    this.innerAudioContext.onEnded(() => {
      this.playingVoiceId = '';
    });
  },
  async mounted() {
    this.safeAreaBottom = uni.getSystemInfoSync().safeAreaInsets.bottom + uni.upx2px(24);
    this.templateScrollBottom = uni.upx2px(104) + this.safeAreaBottom;
    // 获取音频列表
    await this.fetchVoiceList();
    this.fetchTemplates();
    this.calculateScrollHeight();
  },
  beforeDestroy() {
    // 销毁音频上下文
    if (this.innerAudioContext) {
      this.innerAudioContext.destroy();
    }
  },
  methods: {
    // 获取模板列表
    async fetchTemplates(loadMore = false) {
      if (this.loading) return;

      this.loading = true;
      if (!loadMore) {
        this.pageNum = 1;
      }

      try {
        const res = await getTemplateList({
          pageNum: this.pageNum,
          pageSize: this.pageSize
        });

        // 获取所有已选模板的ID
        const selectedIds = this.selectedTemplates.map(template => template.id);

        let newTemplates = [];
        if (loadMore) {
          newTemplates = [...this.templates, ...res.rows.map(item => ({
            ...item,
            selected: selectedIds.includes(item.id),
            voiceId: item.voiceId || (this.musicList.length > 0 ? this.musicList[0].outId : ''),
            speed: item.speed || 1
          }))];
        } else {
          newTemplates = res.rows.map(item => ({
            ...item,
            selected: selectedIds.includes(item.id),
            voiceId: item.voiceId || (this.musicList.length > 0 ? this.musicList[0].outId : ''),
            speed: item.speed || 1
          }));
        }

        this.templates = newTemplates;
        this.total = res.total || 0;
        this.hasMore = this.templates.length < this.total;
      } catch (error) {
        // 请求拦截器已经处理了错误提示，这里不需要再显示
        console.log('获取模板列表失败', error);
      } finally {
        this.loading = false;
      }
    },

    // 获取音频列表
    async fetchVoiceList() {
      try {
        const res = await getVoiceList({
          pageNum: 1,
          pageSize: 100
        });
        this.musicList = res.rows || [];

        // 为已有模板设置默认音乐
        if (this.musicList.length > 0 && this.templates.length > 0) {
          this.templates.forEach(template => {
            if (!template.voiceId) {
              template.voiceId = this.musicList[0].outId;
              template.speed = 1;
            }
          });
        }
      } catch (error) {
        console.log('获取音频列表失败', error);
      }
    },

    // 根据音频ID获取音频名称
    getVoiceName(voiceId) {
      const voice = this.musicList.find(item => item.outId === voiceId);
      return voice ? voice.name : '未选择声音';
    },

    // 打开音乐选择弹窗
    openVoiceSelector(template) {
      this.currentTemplate = template;
      this.currentVoice = template.voiceId || '';
      this.musicValue = template.speed || 1;
      this.musicModelBottom = true;
    },

    // 选择音乐
    selectVoice(item) {
      this.currentVoice = item.outId;
    },

    // 确认音乐选择
    confirmVoice() {
      // 停止播放音频
      this.stopPreviewVoice();

      if (!this.currentVoice) {
        uni.showToast({
          title: '请选择声音',
          icon: 'none'
        });
        return;
      }

      // 更新模板的音乐信息
      if (this.currentTemplate) {
        const templateIndex = this.templates.findIndex(item => item.id === this.currentTemplate.id);
        if (templateIndex > -1) {
          // 更新当前模板的音乐信息
          this.templates[templateIndex].voiceId = this.currentVoice;
          this.templates[templateIndex].speed = this.musicValue;

          // 如果模板已被选中，也需要更新selectedTemplates
          if (this.templates[templateIndex].selected) {
            const selectedIndex = this.selectedTemplates.findIndex(item => item.id === this.currentTemplate.id);
            if (selectedIndex > -1) {
              const updatedTemplates = [...this.selectedTemplates];
              updatedTemplates[selectedIndex] = {
                ...updatedTemplates[selectedIndex],
                voiceId: this.currentVoice,
                speed: this.musicValue
              };
              this.$emit('saveTemplates', updatedTemplates);
            }
          }
        }
      }

      // 关闭弹窗
      this.musicModelBottom = false;
    },

    // 播放声音预览
    previewVoice(item) {
      // 如果有正在播放的音频，先停止
      if (this.playingVoiceId) {
        this.innerAudioContext.stop();
      }

      this.playingVoiceId = item.outId;

      // 设置音频源并播放
      if (item.demoUrl) {
        this.innerAudioContext.src = item.demoUrl;
        this.innerAudioContext.play();

        // 监听播放结束
        this.innerAudioContext.onEnded(() => {
          this.playingVoiceId = '';
        });
      } else {
        uni.showToast({
          title: '音频不可用',
          icon: 'none'
        });
        this.playingVoiceId = '';
      }
    },

    // 停止预览声音
    stopPreviewVoice() {
      if (this.innerAudioContext) {
        this.innerAudioContext.stop();
      }
      this.playingVoiceId = '';
    },

    // 加载更多模板
    loadMore() {
      if (!this.hasMore || this.loading) return;
      this.pageNum++;
      this.fetchTemplates(true);
    },

    // 全选
    selectAll() {
      this.templates.forEach(template => {
        template.selected = true;
      });
      // 向父组件发送所有选中的模板对象
      this.$emit('saveTemplates', [...this.templates]);
    },

    // 取消全选
    cancelAll() {
      this.templates.forEach(template => {
        template.selected = false;
      });
      this.$emit('saveTemplates', []);
    },

    // 切换选中状态
    handleToggleSelect(index, template) {
      const isSelected = this.selectedTemplates.some(item => item.id === template.id);

      if (isSelected) {
        // 如果已经选中，则移除
        const filteredTemplates = this.selectedTemplates.filter(item => item.id !== template.id);
        this.$emit('saveTemplates', filteredTemplates);
      } else {
        // 如果未选中，则添加
        const newSelectedTemplates = [...this.selectedTemplates, { ...template, selected: true }];
        this.$emit('saveTemplates', newSelectedTemplates);
      }
    },

    // 下一步
    nextStep() {
      // 检查是否有选中的模板
      if (this.selectedCount === 0) {
        uni.showToast({
          title: '请至少选择一个模板',
          icon: 'none'
        });
        return;
      }
      // 切换到下一步
      uni.$emit('batch-video-next-step');
    },

    // 计算滚动列表自适应高度
    calculateScrollHeight() {
      const systemInfo = uni.getSystemInfoSync();
      const windowHeight = systemInfo.windowHeight;
      const statusBarHeight = systemInfo.statusBarHeight;
      this.templateScrollHeight = windowHeight - statusBarHeight - uni.upx2px(80) - uni.upx2px(162) - uni.upx2px(112) - uni.upx2px(104) - this.safeAreaBottom;
    }
  }
};
</script>

<style lang="scss">
.template-selection {
  width: 100%;
  background-color: #f3f5f8;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  // 全选/取消全选区域
  .selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 40rpx 0 24rpx;

    .select-all {
      font-size: 32rpx;
      color: #333333;
      line-height: 48rpx;
    }

    .cancel-select {
      font-size: 28rpx;
      color: #666666;
      line-height: 40rpx;
    }
  }

  // 模板网格
  .template-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16rpx;
    margin-bottom: 16rpx;
    justify-items: start; // 确保元素从左边开始排列
  }

  // 骨架屏动画
  @keyframes skeleton-loading {
    0% {
      background-position: -200% 0;
    }

    100% {
      background-position: 200% 0;
    }
  }

  .skeleton-animation {
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
    border-radius: 4rpx;
  }

  // 骨架屏模板卡片
  .template-card-skeleton {
    position: relative;
    width: 218rpx;
    margin-bottom: 8rpx;

    .template-card-content-skeleton {
      position: relative;
      width: 218rpx;
      height: 296rpx;
      border-radius: 8rpx;
      overflow: hidden;
    }

    // 骨架屏模板名称
    .template-name-skeleton {
      width: 178rpx;
      height: 32rpx;
      margin-top: 8rpx;
      margin-left: 8rpx;
    }
  }

  // 加载更多骨架屏
  .loading-more-skeleton {
    padding: 20rpx 0;

    .loading-more-line {
      width: 200rpx;
      height: 20rpx;
      margin: 0 auto;
    }
  }

  // 加载状态
  .loading-status {
    text-align: center;
    padding: 20rpx 0;

    .loading-text {
      font-size: 24rpx;
      color: #999;
    }
  }

  // 模板卡片
  .template-card {
    position: relative;
    width: 218rpx;
    margin-bottom: 20rpx;

    .template-card-content {
      position: relative;
      width: 218rpx;
      height: 296rpx;
      border-radius: 8rpx;
      overflow: hidden;

      .template-card-img {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
      }
    }

    // 模板名称容器
    .template-name-container {
      display: flex;
      flex-direction: column;
      margin-top: 8rpx;

      .template-name {
        width: 178rpx;
        font-size: 24rpx;
        color: #333333;
        line-height: 32rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-left: 8rpx;
        display: flex;
        align-items: center;
      }

      .voice-badge {
        margin-top: 4rpx;
        margin-left: 8rpx;
        background-color: #f0f9f4;
        border-radius: 4rpx;
        padding: 2rpx 6rpx;
        display: inline-flex;
        align-items: center;
        max-width: 190rpx;

        .voice-name {
          font-size: 20rpx;
          color: #2cdd8a;
          margin-right: 4rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 120rpx;
        }

        .voice-speed {
          font-size: 20rpx;
          color: #2cdd8a;
          white-space: nowrap;
        }
      }
    }
  }

  // 选择图标
  .check-icon {
    position: absolute;
    top: 16rpx;
    right: 16rpx;
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    border: 1px solid #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;

    &.active {
      background-image: linear-gradient(135deg, #07e3d2 0%, #01f770 100%);
      border: none;
    }

    .check-mark {
      width: 24rpx;
      height: 24rpx;
    }
  }

  // 底部操作栏
  .action-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #ffffff;
    border-radius: 16rpx 16rpx 0 0;
    padding: 24rpx 48rpx;
    z-index: 10;

    // 已选数量
    .selected-count {
      width: 80rpx;
      height: 80rpx;

      .count-wrapper {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 12rpx;

        .count-number {
          font-size: 24rpx;
          color: #ffffff;
          font-weight: 600;
          line-height: 32rpx;
        }

        .count-label {
          font-size: 20rpx;
          color: #ffffff;
          line-height: 28rpx;
        }
      }
    }

    // 下一步按钮
    .next-step-btn {
      width: 180rpx;
      height: 80rpx;
      background-image: linear-gradient(135deg, #222328 19.837089%, #0f0f0f 100%);
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .btn-text {
        color: #ffffff;
        font-size: 28rpx;
        font-weight: 600;
      }
    }
  }
}

// 音乐选择弹窗
.page_modelShow {
  padding: 40rpx;

  .model_sound {
    border-radius: 20rpx;
  }

  .model_title {
    font-weight: 600;
    font-size: 32rpx;
    text-align: center;
    margin-bottom: 30rpx;
  }

  .model-box-list {
    height: 40vh;
    overflow-y: scroll;
  }

  .model_box,
  .model_box_active {
    display: flex;
    align-items: center;
    background: #F3F5F8;
    border-radius: 20rpx;
    padding: 24rpx 20rpx;
    margin-top: 20rpx;
    border: 2rpx solid transparent;
  }

  .model_box_active {
    background: rgb(204, 252, 234);
    border: 2rpx solid #21BD74;
  }

  .model_voicelist {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #333333;
    margin-left: 24rpx;
  }

  .preview-audio {
    display: flex;
    align-items: center;
    justify-content: center;

    .preview-icon {
      width: 60rpx;
      height: 60rpx;
    }
  }

  .project_btn {
    margin-top: 40rpx;
    background: black;
    color: white;
    // height: 90rpx;
    // line-height: 90rpx;
    text-align: center;
    border-radius: 16rpx;
    font-size: 32rpx;
    font-weight: 600;
  }
}
</style>