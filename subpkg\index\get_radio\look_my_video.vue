<template>
	<view>
		<video :src="page_url" object-fit="contain" style="width: 100vw;height: calc(100vh - 200rpx);">
		</video>

		<view class="bottom_box">
			<view style="display: flex;justify-content: space-around;">
				<view class="btn1" @click="handleDownload">
					<view>下载视频</view>
				</view>
				<view class="btn2" @click="get_video">
					再次生成
				</view>
			</view>
			<view class="bottom_text">{{ type === 'Human' ? '以上为数字人智能剪辑样片，可使用数字人生成更多视频' : '视频生成后仅保留15天，请及时下载' }}</view>
		</view>

		<!-- 下载进度弹窗 -->
		<view v-if="isDownloading" class="download-progress-mask">
			<view class="download-progress-container">
				<view class="progress-title">视频下载中</view>
				<view class="progress-bar-container">
					<view class="progress-bar" :style="{ width: downloadProgress + '%' }"></view>
				</view>
				<view class="progress-tip">请勿熄屏或切换应用 {{ downloadProgress }}%</view>
				<view class="cancel-download-btn" @click="cancelDownload">取消下载</view>
			</view>
		</view>

		<!-- 下载结果弹窗 -->
		<u-popup :show="showResultPopup" :safe-area-inset-bottom="false" mode="center" round="16" @close="closeResultPopup">
			<view class="download-result-popup">

				<view class="result-title">{{ downloadResult === 'success' ? '保存成功' : '保存失败' }}</view>
				<view class="result-message">{{ resultMessage }}</view>
				<view class="result-btn" @click="closeResultPopup">确定</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
export default {
	data() {
		return {
			page_url: '',
			type: '',
			downloadProgress: 0, // 下载进度
			isDownloading: false, // 是否正在下载
			downloadTask: null, // 下载任务引用
			showResultPopup: false, // 是否显示结果弹窗
			downloadResult: '', // 下载结果：success 或 fail
			resultMessage: '' // 结果消息
		}
	},
	onLoad(option) {
		if (option) {
			this.page_url = decodeURIComponent(option.url)
			this.type = option.type
		}
	},
	methods: {
		handleDownload() {
			this.downloadVideoToAlbum(this.page_url)
				.then(res => {
					console.log('视频保存成功', res);
				})
				.catch(err => {
					console.error('视频保存失败', err);
				});
		},

		// 取消下载
		cancelDownload() {
			if (this.downloadTask) {
				this.downloadTask.abort(); // 取消下载任务
				this.isDownloading = false;
				this.downloadTask = null;

				// uni.showToast({
				// 	title: '已取消下载',
				// 	icon: 'none'
				// });
			}
		},

		// 关闭结果弹窗
		closeResultPopup() {
			this.showResultPopup = false;
		},

		// 显示结果弹窗
		showResult(isSuccess, message) {
			this.downloadResult = isSuccess ? 'success' : 'fail';
			this.resultMessage = message || (isSuccess ? '视频已保存到相册' : '保存视频失败，请重试');
			this.showResultPopup = true;
		},

		// 下载视频到相册
		downloadVideoToAlbum(videoUrl) {
			let that = this
			return new Promise((resolve, reject) => {
				// 1. 检查权限
				uni.getSetting({
					success(res) {
						if (!res.authSetting['scope.writePhotosAlbum']) {
							// 2. 请求权限
							uni.authorize({
								scope: 'scope.writePhotosAlbum',
								success() {
									// 授权成功，开始下载
									that.downloadAndSave(videoUrl, resolve, reject);
								},
								fail(authErr) {
									// 用户拒绝了授权
									if (authErr.errMsg.includes('auth deny')) {
										uni.showModal({
											title: '提示',
											content: '需要相册权限才能保存视频',
											confirmText: '去设置',
											success(modalRes) {
												if (modalRes.confirm) {
													uni.openSetting();
												}
												reject(new Error('用户拒绝授权'));
											}
										});
									} else {
										reject(authErr);
										that.showResult(false, '授权失败，无法保存视频');
									}
								}
							});
						} else {
							// 已有权限，直接下载
							that.downloadAndSave(videoUrl, resolve, reject);
						}
					},
					fail(err) {
						reject(err);
						that.showResult(false, '获取权限信息失败');
					}
				});
			});
		},

		// 下载并保存视频
		downloadAndSave(videoUrl, resolve, reject) {

			if (this.isDownloading) {
				// uni.showToast({
				// 	title: '视频正在下载中，请稍后再试',
				// 	icon: 'none'
				// });
				return;
			}
			// 重置下载进度并显示进度条
			this.downloadProgress = 0;
			this.isDownloading = true;

			// 1. 下载视频文件
			this.downloadTask = uni.downloadFile({
				url: videoUrl,
				success: (downloadRes) => {
					if (downloadRes.statusCode !== 200) {
						this.isDownloading = false;
						this.showResult(false, `下载失败，错误码：${downloadRes.statusCode}`);
						reject(new Error('视频下载失败'));
						return;
					}

					// 2. 保存到相册
					uni.saveVideoToPhotosAlbum({
						filePath: downloadRes.tempFilePath,
						success: () => {
							// 稍微延迟隐藏进度条，让用户看到100%的状态
							setTimeout(() => {
								this.isDownloading = false;
								this.showResult(true);
								resolve({
									success: true,
									savedPath: downloadRes.tempFilePath
								});
							}, 200);
						},
						fail: (saveErr) => {
							this.isDownloading = false;
							let errorMsg = '保存失败';
							if (saveErr.errMsg) {
								// 提取更有用的错误信息
								if (saveErr.errMsg.includes('fail file not exist')) {
									errorMsg = '文件不存在，保存失败';
								} else if (saveErr.errMsg.includes('fail auth deny')) {
									errorMsg = '授权失败，无法保存视频';
								} else {
									errorMsg = `保存失败: ${saveErr.errMsg}`;
								}
							}
							this.showResult(false, errorMsg);
							reject(saveErr);
						}
					});
				},
				fail: (downloadErr) => {
					this.isDownloading = false;
					let errorMsg = `下载失败 ${downloadErr.errMsg}`;

					// 判断是否为用户主动取消
					if (downloadErr.errMsg && downloadErr.errMsg.includes('fail abort')) {
						errorMsg = '下载已取消';
					}

					this.showResult(false, errorMsg);
					reject(downloadErr);
				}
			});

			// 监听下载进度变化
			this.downloadTask.onProgressUpdate((res) => {
				this.downloadProgress = res.progress;
			});
		},

		get_video() {
			uni.navigateTo({
				url: '/subpkg/index/get_radio/index'
			})
		}
	}
}
</script>

<style scoped lang="scss">
.bottom_box {
	width: 100vw;
	height: 220rpx;
	background: black;
	text-align: center;
	padding: 20rpx;
	border-top-right-radius: 20rpx;
	border-top-left-radius: 20rpx;
	position: fixed;
	bottom: 0;
	left: 0;

	.btn1 {

		padding: 22rpx;
		width: 45vw;
		border-radius: 16rpx;
		background: white;
		font-size: 30rpx;
		font-weight: 600;
	}

	.btn2 {
		padding: 22rpx;
		width: 45vw;
		border-radius: 16rpx;
		background: $project_1_bg;
		font-size: 30rpx;
		font-weight: 600;
	}

	.bottom_text {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.5);
		margin-top: 20rpx;
	}
}

// 下载进度遮罩层样式
.download-progress-mask {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background-color: rgba(0, 0, 0, 0.6);
	z-index: 999;
	display: flex;
	align-items: center;
	justify-content: center;
}

// 下载进度容器
.download-progress-container {
	width: 70%;
	background-color: #fff;
	border-radius: 20rpx;
	padding: 40rpx 30rpx;
	text-align: center;
}

// 进度条标题
.progress-title {
	font-size: 32rpx;
	font-weight: 600;
	margin-bottom: 40rpx;
}

// 进度条容器
.progress-bar-container {
	width: 100%;
	height: 16rpx;
	background-color: #f0f0f0;
	border-radius: 10rpx;
	overflow: hidden;
	margin-bottom: 20rpx;
}

// 进度条
.progress-bar {
	height: 100%;
	background: linear-gradient(90deg, #F7D389, #FEE8C2);
	border-radius: 10rpx;
	transition: width 0.3s;
}

// 进度文本
.progress-text {
	font-size: 28rpx;
	margin-bottom: 20rpx;
}

// 提示文本
.progress-tip {
	font-size: 24rpx;
	color: #919191;
	margin-bottom: 30rpx;
}

// 取消下载按钮
.cancel-download-btn {
	display: inline-block;
	width: 70%;
	padding: 16rpx 40rpx;
	background-color: #f2f2f2;
	color: #333;
	border-radius: 30rpx;
	font-size: 28rpx;
	margin-top: 20rpx;
}

// 下载结果弹窗样式
.download-result-popup {
	width: 560rpx;
	padding: 50rpx 30rpx;
	background-color: #fff;
	border-radius: 24rpx;
	display: flex;
	flex-direction: column;
	align-items: center;

	.result-icon {
		margin-bottom: 30rpx;
	}

	.result-title {
		font-size: 36rpx;
		font-weight: 600;
		margin-bottom: 20rpx;
	}

	.result-message {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 40rpx;
		text-align: center;
		padding: 0 20rpx;
	}

	.result-btn {
		width: 80%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		background: linear-gradient(90deg, #000000, #252525);
		border-radius: 40rpx;
		font-size: 30rpx;
		font-weight: 600;
		color: #ffffff;
	}
}
</style>