<template>
  <view class="edit-page" :style="{ paddingBottom: editPageBottom + 'px' }">
    <!-- 自定义导航栏 -->
    <custom-navbar title="输入文案" :show-back-btn="true" background="#f3f5f8" :isBlack="true"></custom-navbar>

    <!-- 文本编辑区域 -->
    <view class="content-wrapper" :style="{ height: contentHeight + 'px' }">
      <!-- 标题输入区域 -->
      <view class="title-input-box" v-if="type == 1">
        <u--input placeholder="请输入标题" border="surround" v-model="page_title" class="title-input"></u--input>
      </view>

      <!-- 正文文本区域 -->
      <view class="text-content-box">
        <textarea ref="textarea" class="content-textarea" v-model="page_textarea" placeholder="请输入正文内容" border="false"
          :maxlength="800" @blur="handleBlur">
        </textarea>
        
        <!-- 底部工具栏 -->
        <view class="bottom-toolbar">
          <view class="tool-buttons">
            <view class="tool-item" @click="show = true">
              <text class="tool-text">停顿</text>
            </view>
            <view class="tool-item" @click="clearAll">
              <text class="tool-text">清除</text>
            </view>
          </view>
          <view class="word-count">
            <text class="current-count">{{ page_textarea.length }}</text>
            <text class="separator">/</text>
            <text class="max-count">800</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 完成按钮 -->
    <view class="submit-button" @click="over">
      <text class="submit-text">完成</text>
    </view>

    <!-- 停顿选择弹窗 -->
    <u-popup :show="show" :round="20" @close="() => show = false" mode="bottom">
      <view class="pause-popup">
        <view class="popup-title">请选择停顿时长</view>
        <view v-for="item in time_data" :key="item.value" class="pause-option" @click="change_time(item)">
          <view class="option-text">
            {{ item.name }}
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      type: '',
      page_textarea: "",
      page_title: "",
      show: false,
      time_data: [
        { name: '短停顿(0.1s)', value: '0.1s' },
        { name: '中停顿(0.3s)', value: '0.3s' },
        { name: '长停顿(0.5s)', value: '0.5s' },
      ],
      safeAreaBottom: 0,
      editPageBottom: 0,
      contentHeight: 0,
      selectionEnd: 0
    }
  },
  onLoad(option) {
    this.safeAreaBottom = uni.getSystemInfoSync().safeAreaInsets.bottom
    this.editPageBottom = this.safeAreaBottom + uni.upx2px(40)
    this.calculateContentHeight()
    this.type = option?.type || ''
    this.getOpenerEventChannel().on('post_data', ({ data }) => {
      this.page_title = data.title || ''
      this.page_textarea = data.content
      console.log(data)
    })
  },
  methods: {
    handleBlur(e) {
      // 记录光标位置
      this.selectionEnd = e.detail.cursor
    },
    change_time(item) {
      this.show = false
      // 在光标位置插入停顿标记
      const str = `:${item.value}:`
      this.page_textarea = this.page_textarea.slice(0, this.selectionEnd) + str + this.page_textarea.slice(this.selectionEnd)
      // 更新光标位置
      this.selectionEnd += str.length
    },
    clearAll() {
      uni.showModal({
        title: '提示',
        content: '确定要清空所有内容吗？',
        success: (res) => {
          if (res.confirm) {
            this.page_textarea = ''
            this.selectionEnd = 0
          }
        }
      })
    },
    over() {
      this.getOpenerEventChannel().emit('get_content', {
        content: this.page_textarea,
        title: this.page_title
      })
      uni.navigateBack()
    },
    calculateContentHeight() {
      const systemInfo = uni.getSystemInfoSync();
      const statusBarHeight = systemInfo.statusBarHeight;
      const navBarHeight = uni.upx2px(80); // 默认导航栏高度
      const contentHeight = systemInfo.windowHeight - statusBarHeight - navBarHeight - uni.upx2px(148) - this.editPageBottom - uni.upx2px(98);
      this.contentHeight = contentHeight;
    }
  }
}
</script>

<style lang="scss">
.edit-page {
  background-color: #F3F5F8;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 40rpx;
  box-sizing: border-box;

  .content-wrapper {
    background-color: #FFFFFF;
    border-radius: 24rpx;
    margin: 32rpx;
    padding: 32rpx;
    // flex: 1;
    display: flex;
    flex-direction: column;

    .title-input-box {
      margin-bottom: 48rpx;

      .title-input {
        font-size: 28rpx;
        font-weight: 600;
        color: #333333;
      }
    }

    .text-content-box {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      // height: 60vh;

      .content-textarea {
        font-size: 32rpx;
        color: #333333;
        font-family: 'MiSans-Medium';
        flex: 1;
        width: 100%;
      }
      
      .bottom-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20rpx;

        .tool-buttons {
          display: flex;
          gap: 20rpx;
        }

        .tool-item {
          display: flex;
          align-items: center;
          background-color: #F3F5F8;
          border-radius: 8rpx;
          padding: 12rpx 24rpx;
          cursor: pointer;

          .tool-icon {
            width: 24rpx;
            height: 24rpx;
            margin-right: 8rpx;
          }

          .tool-text {
            font-size: 24rpx;
            color: #333333;
            // font-weight: 600;
            line-height: 32rpx;
          }
        }

        .word-count {
          display: flex;
          align-items: center;

          .current-count {
            font-size: 24rpx;
            color: #666666;
            line-height: 32rpx;
          }

          .separator {
            font-size: 24rpx;
            color: #666666;
            line-height: 32rpx;
          }

          .max-count {
            font-size: 24rpx;
            color: #CCCCCC;
            line-height: 32rpx;
          }
        }
      }
    }
  }

  .submit-button {
    background: #000000;
    border-radius: 16rpx;
    height: 96rpx;
    margin: 0 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .submit-text {
      color: #FFFFFF;
      font-size: 36rpx;
      font-weight: 600;
      line-height: 56rpx;
    }
  }

  .pause-popup {
    padding: 30rpx 40rpx;

    .popup-title {
      text-align: center;
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 20rpx;
    }

    .pause-option {
      margin: 50rpx 0;
      text-align: center;

      .option-text {
        font-size: 28rpx;
        color: #333333;
      }
    }
  }
}
</style>