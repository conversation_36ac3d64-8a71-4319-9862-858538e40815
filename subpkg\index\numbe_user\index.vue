<template>
  <view class="page">
    <view v-if="page_type === 'lite'" class="page_bg"></view>
    <image v-else src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/get_radio_bg.png" class="page_bg"></image>

    <!-- 使用自定义导航栏组件 -->
    <custom-navbar :title="page_type === 'lite' ? '定制数字人-极速版' : '定制数字人-专业版'" :showBack="true" :isBlack="true"
      :showBackBtn="true" @back="go_back">
    </custom-navbar>

    <view class="title_text" style="margin-top: 40rpx;">
      <view>
        1.数字人定制
      </view>
    </view>
    <view class="title_text" style="font-size: 28rpx;margin-top: 20rpx;">
      形象训练
      <image class="bg_icon" mode="aspectFit"
        src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/get_rradio_bg_icon.png"></image>
    </view>

    <view class="page_card">
      <view v-if="video_src">
        <view style="position: absolute;bottom: 20rpx;right: 40rpx;" @click="go_tutorial">
          <view style="display: flex;padding: 10rpx;border-radius: 20rpx;border: 2rpx solid #919191;font-size: 24rpx;">
            <u-icon name="reload"></u-icon>
            <text>更换</text>
          </view>
        </view>
        <view style="position: relative;" @click="look_video('0')">

          <image class="video_text" :class="isLandscape ? 'video_text_landscape' : ''" mode="aspectFit"
            :src="video_src + url_end"></image>

          <view class="icon_center">
            <u-icon name="play-right-fill" size="36" color="white"></u-icon>
          </view>
        </view>
      </view>
      <view class="card_1" v-else @click="go_tutorial">
        <image :src="page_img_statr + 'project1_home1_number_user_index_sc_pro.png'"></image>
        <view>点击上传训练视频</view>
      </view>
    </view>


    <view class="title_text"> 声音克隆</view>
    <view class="page_card_1">
      <u-subsection :list="list" :current="page_current" active-color="#000000" inactive-color="#919191"
        @change="down_change"></u-subsection>
      <view v-if="page_current == 0">
        <view v-if="!music_src" class="card_1" @click="get_video_url">
          <image :src="page_img_statr + 'project1_home1_number_user_index_tq_pro.png'"></image>
          <view>点击提取训练视频的声音</view>
        </view>
        <view v-else>
          <view style="position: absolute;bottom: 20rpx;right: 40rpx;" @click="this.model_show = true">
            <view
              style="display: flex;padding: 10rpx;border-radius: 20rpx;border: 2rpx solid #919191;font-size: 24rpx;">
              <u-icon name="reload"></u-icon>
              <text>更换</text>
            </view>
          </view>
          <view class="card_1" @click="start_music" v-if="!isPlaying">
            <image style="width: 100rpx;height: 100rpx;" src="/static/home/<USER>/shengyinluzhi.png"></image>
            <view>点击试听</view>
          </view>
          <view class="card_1" @click="stop_music" v-else>
            <image style="width: 100rpx;height: 100rpx;" src="/static/home/<USER>/zanting.png"></image>
            <view>点击暂停</view>
          </view>
        </view>
      </view>

      <view v-if="page_current == 1">
        <view class="card_1" @click="this.model_show = true" v-if="!music_src">
          <image style="width: 100rpx;height: 100rpx;" src="/static/home/<USER>/shengyinluzhi.png"></image>
          <view>点击录制</view>
        </view>
        <view v-else>
          <view style="position: absolute;bottom: 20rpx;right: 40rpx;" @click="this.model_show = true">
            <view
              style="display: flex;padding: 10rpx;border-radius: 20rpx;border: 2rpx solid #919191;font-size: 24rpx;">
              <u-icon name="reload"></u-icon>
              <text>更换</text>
            </view>
          </view>
          <view class="card_1" @click="start_music" v-if="!isPlaying">
            <image style="width: 100rpx;height: 100rpx;" src="/static/home/<USER>/shengyinluzhi.png"></image>
            <view>点击试听</view>
          </view>
          <view class="card_1" @click="stop_music" v-else>
            <image style="width: 100rpx;height: 100rpx;" src="/static/home/<USER>/zanting.png"></image>
            <view>点击暂停</view>
          </view>
        </view>
      </view>


    </view>


    <view class="title_text">数字人授权</view>
    <view class="page_card_1">
      <view v-if="video1_src">
        <view style="position: absolute;bottom: 20rpx;right: 40rpx;z-index: 999" @click="go_video">
          <view style="display: flex;padding: 10rpx;border-radius: 20rpx;border: 2rpx solid #919191;font-size: 24rpx;">
            <u-icon name="reload"></u-icon>
            <text>更换</text>
          </view>
        </view>
        <view style="position: relative;" @click="look_video('1')">
          <image class="video_text" :src="video1_src + url_end">
          </image>
          <view class="icon_center">
            <u-icon name="play-right-fill" size="36" color="white"></u-icon>
          </view>
        </view>


      </view>
      <view class="card_1" style="height:190rpx;margin-top: -5rpx" @click="go_video" v-else>
        <image :src="page_img_statr + 'project1_home1_number_user_index_pz_pro.png'"></image>
        <view>拍摄授权视频</view>

        <view style="display: flex;align-items: center;margin-top: 10rpx;">
          <text class="dot"></text>
          <view class="content_text">将现场拍摄授权视频,用作形象和声音的克隆</view>
        </view>
        <view style="display: flex;align-items: center;">
          <text class="dot"></text>
          <view class="content_text">录制时,请流畅,一字不落的念出提示框中的内容</view>
        </view>
      </view>
    </view>

    <view class="page_button" @click="startTraining">
      <view class="btn_1">开始训练</view>
      <view class="btn_2" v-if="page_type == 'pro'">(预计消耗 {{ dictValue }} 算力)</view>
    </view>

    <u-popup :show="show_video" :round="20" @close="down_close" mode="center">
      <view class="center_card">

        <view class="card_title">
          <view style="width: 40rpx;"></view>
          <view>视频预览</view>
          <view class="close-btn" @click="show_video = false">
            <u-icon name="close" size="18"></u-icon>
          </view>
        </view>
        <video style="width: 350rpx;height: 560rpx;border-radius: 20rpx;"
          :src="video_type == '0' ? video_src : video1_src" controls></video>
      </view>
    </u-popup>


    <projectModel v-if="model_show" title="提示" content="请确保您录制或上传的音频已经过授权且符合法律法规。" @btn_save="get_sound2" :btn="false">
      <view class="project_btn" @click="get_sound2">确定</view>
    </projectModel>
    <projectModel v-if="change_tab_show" title="温馨提示" content="切换后,当前提取的结果会被清空，是否确认切换？" save="继续切换"
      @btn_save="clearAudio" @btn_close="cancelChangeTab">
    </projectModel>

    <view v-if="model_loding" class="project_model_1" style="background: rgba(0, 0, 0, 0.9);">
      <view class="project_model_2" style="background: none;color: white;">
        <u-loading-icon mode="circle" size="80rpx"></u-loading-icon>
        <view style="text-align: center;margin-top: 30rpx;">{{ uploading ? '文件上传中' : '音频正在提取...' }}</view>
        <view style="text-align: center;margin: 20rpx 0;font-size: 24rpx;opacity: 0.8;">请勿熄屏或切换应用</view>
        <!-- 上传进度显示 -->
        <view style="text-align: center;margin-top: 10rpx;" v-if="uploading && uploadProgress > 0">
          <text>{{ Math.round(uploadProgress) }}%</text>
        </view>
        <!-- <view style="display: flex;justify-content: space-around;">
          <view class="page_btn" @click="() =>{model_loding = false;page_url = null}">取消</view>
        </view> -->
      </view>
    </view>


  </view>
</template>

<script>
import {
  getTrainPerson,
  getUploadFileSts
} from '../../../api/numberUser/dr.js'

import {
  handleVideoFileToAudio_too
} from '../../../utils/video-to-audio.js'

import { page_img_statr } from '../../../api/getData.js'
import COS from 'cos-wx-sdk-v5'
import { util } from '../../../utils/file.js'

export default {
  data() {
    return {
      url_end: '?ci-process=snapshot&time=0&format=jpg',
      isSubmitting: false, //防止重复提交
      dictValue: 500, // 基础算力消耗，默认500
      video_type: '',
      change_tab_show: false,
      show_video: false,
      list: ['提取声音', '录制声音', '不克隆声音'],
      page_current: 0,
      video_src: '',
      video1_src: '',

      music_src: '',

      model_show: false,
      model_loding: false,
      page_type: '',

      innerAudioContext: null, // 音频上下文
      isPlaying: false, // 是否正在播放
      tempIndex: 0,
      voiceTaskId: '',
      voiceName: '',
      page_url: null,
      virtualmanName: '',
      page_img_statr: '',
      isLandscape: false,
      voiceTaskList: [],
      uploading: false,
      uploadProgress: 0,
      post_url: '', // COS上传后的URL
      selectedLanguage: '', // 选择的语种
      tempFilePath: '' // 下载的临时文件路径
    }
  },
  computed: {
    // 计算当前算力消耗
    currentDictValue() {
      // 基础算力 + 声音模型数量*500
      return 500 + (this.voiceTaskList.length * 500);
    }
  },
  onLoad(option) {
    this.page_img_statr = page_img_statr
    this.$store.commit('SetNumberUserMusic', '')
    this.$store.commit('SetNumberUserVideo', '')
    
    // 创建音频上下文
    this.innerAudioContext = uni.createInnerAudioContext({
      useWebAudioImplement: true
    })
    // 监听音频自然结束
    this.innerAudioContext.onEnded(() => {
      this.isPlaying = false
    })

    this.page_type = option.type
    
    // 更新算力值
    this.updateDictValue();

    // 监听voice_selected事件，接收从save_sound.vue传递过来的数据
    uni.$on('voice_selected', (data) => {
      console.log(this.page_current, 'this.page_current');
      this.voiceTaskList = data;
      // 更新算力值
      this.updateDictValue();
    })
  },
  onUnload() {
    // 页面卸载时销毁音频
    if (this.innerAudioContext) {
      this.innerAudioContext.destroy()
    }
    console.log('onUnload');

    // 移除事件监听
    uni.$off('voice_selected')
  },
  onShow() {
    this.isSubmitting=false
    this.video_src = this.$store.state.numberUserVideo
    if (this.video_src.split('//')[1] && this.video_src.split('//')[1].substring(0, 7) != 'vod-sja') {
      this.video_src = ''
    }
    if (this.video_src) {
      this.getVideoInfo(this.video_src)
    }

    this.video1_src = uni.getStorageSync("authVideoUrl")

    if (this.video1_src.split('//')[1] && this.video1_src.split('//')[1].substring(0, 7) != 'vod-sja') {
      this.video1_src = ''
    }

    this.music_src = this.$store.state.numberUserMusic
    this.virtualmanName = uni.getStorageSync('virtualmanName')
    
    // 更新算力值
    this.updateDictValue();
  },
  onHide() {
    this.innerAudioContext.stop()
  },
  methods: {
    // 更新算力值
    updateDictValue() {
      if (this.page_current === 2) {
        // 不克隆声音，只有基础算力
        this.dictValue = 500;
      } else {
        // 克隆声音，基础算力 + 声音模型数量*500
        this.dictValue = 500 + (this.voiceTaskList.length * 500);
      }
    },
    
    getVideoInfo(url) {
      uni.getVideoInfo({
        src: url,
        success: (res) => {
          this.isLandscape = res.width > res.height
        }
      })
    },

    cancelChangeTab() {
      console.log('cancelChangeTab');

      this.change_tab_show = false;
      this.page_current = this.tempIndex
    },
    clearAudio() {
      uni.removeStorageSync('soundData')
      this.$store.commit('SetNumberUserMusic', '')
      this.voiceTaskId = null
      this.music_src = null
      this.voiceTaskList = [] // 清空声音模型列表
      this.change_tab_show = false
      
      // 更新算力值
      this.updateDictValue()
    },

    // 下载音频文件
    async downloadAudioFile(url) {
      return new Promise((resolve, reject) => {
        uni.downloadFile({
          url: url,
          success: (res) => {
            if (res.statusCode === 200) {
              console.log('下载成功', res.tempFilePath);
              resolve(res.tempFilePath);
            } else {
              reject(new Error('下载失败，状态码：' + res.statusCode));
            }
          },
          fail: (err) => {
            console.error('下载失败', err);
            reject(err);
          }
        });
      });
    },

    // 上传到COS
    uploadToCOS(filePath) {
      if (!filePath) {
        uni.showToast({
          title: '音频文件不存在',
          icon: 'none'
        })
        return Promise.reject(new Error('文件不存在'));
      }

      this.uploadProgress = 0;

      return new Promise((resolve, reject) => {
        getUploadFileSts({
          scene: 'miniprogram-temp',
          type: 'music',
          extName: 'mp3'
        }).then((res) => {
          this.processUpload(res.data, filePath)
            .then(cosUrl => resolve(cosUrl))
            .catch(err => reject(err));
        }).catch(err => {
          console.error('获取上传凭证失败:', err);
          uni.showToast({
            title: '获取上传凭证失败',
            icon: 'none'
          });
          this.uploadProgress = 0;
          reject(err);
        });
      });
    },

    async processUpload(data, filePath) {
      const {
        credentials,
        region,
        bucket,
        key,
        startTime,
        expiredTime
      } = data;

      try {
        const arrayBuffer = await util.tempPathToArrayBuffer(filePath);
        this.uploadProgress = 10; // 文件准备完成，显示10%进度

        return new Promise((resolve, reject) => {
          const cos = new COS({
            SecretId: credentials.tmpSecretId,
            SecretKey: credentials.tmpSecretKey,
            SecurityToken: credentials.sessionToken,
            StartTime: startTime,
            ExpiredTime: expiredTime
          });

          cos.putObject({
            Bucket: bucket,
            Region: region,
            Key: key,
            StorageClass: 'STANDARD',
            Body: arrayBuffer,
            onProgress: (progressData) => {
              console.log('上传进度', progressData);
              // 将进度从10%到90%，保留最后10%用于处理
              this.uploadProgress = 10 + Math.floor(progressData.percent * 0.8);
            }
          }, (err, data) => {
            if (err) {
              console.error('上传失败:', err);
              uni.showToast({
                title: '音频上传失败',
                icon: 'none'
              });
              this.uploadProgress = 0;
              reject(err);
            } else {
              console.log('上传成功');
              const cosUrl = `https://${bucket}.cos.${region}.myqcloud.com/${key}`;
              this.post_url = cosUrl;
              this.uploadProgress = 100; // 上传完成，显示100%
              resolve(cosUrl);
            }
          });
        });
      } catch (err) {
        console.error('文件转换失败:', err);
        uni.showToast({
          title: '文件处理失败',
          icon: 'none'
        });
        this.uploadProgress = 0;
        throw err;
      }
    },

    async get_video_url() {
      if (!this.video_src) {
        uni.showToast({
          title: '请先上传视频',
          icon: 'none'
        });
        return;
      }

      try {
        // 1. 显示加载中
        this.model_loding = true;
        this.uploading = false;

        // 2. 从视频中提取音频，获取音频URL
        this.page_url = await handleVideoFileToAudio_too(this.video_src);
        console.log(this.page_url, 'this.page_url');

        if (!this.page_url) {
          throw new Error('音频提取失败');
        }

        // 3. 下载音频文件到本地临时路径
        this.tempFilePath = await this.downloadAudioFile(this.page_url);

        // 4. 上传音频到COS
        this.uploading = true; // 切换到上传状态
        await this.uploadToCOS(this.tempFilePath);

        // 5. 上传完成，等待一小段时间显示100%进度
        setTimeout(() => {
          // 隐藏加载提示
          this.model_loding = false;
          this.uploading = false;

          // 6. 导航到save_sound页面，只携带post_url参数
          uni.navigateTo({
            url: `/subpkg/index/get_sound/save_sound?type=${this.page_type}&free=1&post_url=${encodeURIComponent(this.post_url)}&currentTab=0`
          });
        }, 500);
      } catch (error) {
        console.error('处理音频失败:', error);
        uni.showToast({
          title: '处理音频失败 请稍后重试',
          icon: 'none'
        });
        this.model_loding = false;
        this.uploading = false;
      }
    },

    down_change(index) {
      console.log(index, 'index');

      // 如果切换到与当前相同的选项卡，不做任何操作
      if (index === this.page_current) return;

      this.tempIndex = this.page_current;
      this.page_current = index;
      
      // 如果有声音数据，显示确认弹窗
      if (this.music_src || this.voiceTaskList.length > 0) {
        this.change_tab_show = true;
      } else {
        // 如果没有声音数据，直接清空并切换
        this.$store.commit('SetNumberUserMusic', '');
        this.voiceTaskList = [];
        this.updateDictValue();
      }
    },

    go_back() {
      uni.navigateBack()
    },
    go_tutorial() {
      uni.navigateTo({
        url: `/subpkg/index/tutorial/index?type=${this.page_type}`
      })
    },
    go_video() {
      uni.navigateTo({
        url: `/subpkg/index/authorization_video/index?type=${this.page_type}`
      })
    },
    get_sound2() {
      this.model_show = false
      uni.navigateTo({
        url: `/subpkg/index/get_sound/index?type=${this.page_type}&free=1`
      })
    },

    look_video(type) {
      this.video_type = type
      this.show_video = true
    },

    //试听
    start_music() {
      this.isPlaying = true
      this.innerAudioContext.src = this.$store.state.numberUserMusic;
      this.innerAudioContext.play();
    },

    //暂停
    stop_music() {
      this.isPlaying = false
      this.innerAudioContext.stop()
    },
    //开始训练
    async startTraining() {

      if (!this.virtualmanName || this.virtualmanName.length == 0) {
        uni.showToast({ title: '请先上传形象信息', icon: 'none' })
        return
      }
      if (!this.video_src) {
        uni.showToast({ title: '请上传形象视频', icon: 'none' })
        return
      }
      if (!this.video1_src) {
        uni.showToast({ title: '请上传授权视频', icon: 'none' })
        return
      }
      if ((this.page_current == 1 || this.page_current == 0) && this.voiceTaskList.length <= 0) {
        uni.showToast({ title: '请录制声音或选择不克隆声音', icon: 'none' })
        return
      }

      if (this.isSubmitting) return; // 正在提交中，阻止后续点击
      this.isSubmitting = true;

      let tranData = {
        videoUrl: this.$store.state.numberUserVideo,
        authVideoUrl: this.video1_src,
        virtualmanName: this.virtualmanName
      }

      if (this.page_current != 2) {
        tranData.voiceTaskList = this.voiceTaskList
      }

      if (this.page_type == 'pro') {
        tranData.type = 'pro'
        tranData.crop = this.$store.state.drVideoCropInfo
        console.log("设置裁剪参数" + JSON.stringify(tranData.crop))
      } else {
        tranData.type = 'lite'
      }
      uni.showLoading({ title: '上传中...' })
      try {
        await getTrainPerson(tranData);
        uni.removeStorageSync('soundData');
        uni.removeStorageSync('virtualmanName');
        this.$store.commit('SetNumberUserMusic', '');
        this.$store.commit('SetNumberUserVideo', '');
        uni.navigateTo({
          url: `/subpkg/index/numbe_user/waitReciew?type=${this.page_type}`
        });
      } catch (e) {
        uni.showToast({ title: '上传失败', icon: 'none' });
      } finally {
        uni.hideLoading();
        this.isSubmitting = false;
      }
    }
  }
}
</script>

<style scoped lang="scss">
.page_bg {
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: -1;
  top: 0;
  left: 0;
}

.page {
  // background-image: url('/static/page_bgd.png');
  background-size: 100% 100%;
  padding: 0 40rpx;
  box-sizing: border-box;
  min-height: 100vh;
  padding-bottom: 150rpx;

  .video_text {
    width: 153rpx;
    height: 272rpx;
    border-radius: 20rpx;
  }

  .icon_center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .title_text {
    font-weight: 800;
    font-size: 32rpx;
    position: relative;

    background: linear-gradient(to right, #975403, #311B01);


    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    -webkit-text-fill-color: transparent;

  }

  .bg_icon {
    width: 40vw;
    position: absolute;
    right: 0;
    top: -50rpx;
  }

  .page_card {
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);

    position: relative;
    height: 300rpx;
    margin: 20rpx 0;
    text-align: center;
    padding: 14rpx 0;
    display: flex;
    justify-content: space-around;
    align-items: center;
    border-radius: 20rpx;
  }

  .card_1 {
    font-size: 28rpx;
    margin-top: 50rpx;
    font-weight: 600;
    height: 150rpx;

    .dot {
      display: inline-block;
      width: 10rpx;
      height: 10rpx;
      border-radius: 50%;
      background: #919191;
      margin-right: 10rpx;
    }

    .content_text {
      font-size: 24rpx;
      color: #919191;
    }

    image {
      width: 64rpx;
      height: 64rpx;
    }
  }

  .page_card_1 {
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    position: relative;
    margin: 20rpx 0;
    text-align: center;
    padding: 40rpx;

    border-radius: 20rpx;
  }

  .center_card {
    width: 500rpx;
    height: 740rpx;
    background: white;
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    position: relative;

    .card_title {
      position: absolute;
      top: 20rpx;
      left: 0;
      display: flex;
      justify-content: space-between;
      width: 100%;
      padding: 0 20rpx;
      font-size: 32rpx;
    }
  }

  .page_button {
    background: linear-gradient(136deg, #FEE8C2 0%, #F7D389 45%);
    text-align: center;
    border-radius: 20rpx;
    padding: 20rpx 0;

    position: fixed;
    width: 80vw;
    left: 10vw;
    bottom: 40rpx;

    .btn_1 {
      font-size: 32rpx;
      color: #000000;
      font-weight: 600;
    }

    .btn_2 {
      font-size: 26rpx;
      color: #686868;
    }
  }

  .page_btn {
    border: 2rpx white solid;
    padding: 10rpx 40rpx;
    display: inline-block;
    border-radius: 20rpx;
    margin-top: 20rpx;
  }
}

.gradient-underline {
  position: relative;
  display: inline;
}

.gradient-underline::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -10rpx;
  /* 调整下划线位置 */
  width: 100%;
  height: 6rpx;
  /* 下划线粗细 */

  background: linear-gradient(135deg, #07E3D2 0%, #01F770 100%);
  ;
}

.project_btn {
  font-weight: bold;
  padding: 30rpx;
  font-size: 30rpx;
  background: black;
}

.video_text {
  width: 153rpx;
  height: 272rpx;
  border-radius: 20rpx;
  background: #000;
  display: block;
  margin: 0 auto;
}

.video_text_landscape {
  width: 272rpx; // 横屏时宽度更大
  height: 153rpx; // 高度变小
  object-fit: contain;
  background: #000;
}

.close-btn {
  width: 54rpx;
  height: 54rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
</style>
