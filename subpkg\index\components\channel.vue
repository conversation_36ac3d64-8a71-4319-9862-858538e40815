<template>
	<u-popup :show="show" @close="handleClose" :round="20">
		<view class="channel-popup">
			<view class="channel-title">选择频道</view>
			<template v-if="channelList.length > 0">
				<view class="channel-item" v-for="(item, index) in channelList" :key="index" @click="handleChannelSelect(item)">
					<view class="channel-icon">
						<image :src="item.icon"></image>
					</view>
					<view class="channel-info">
						<view class="channel-name">{{ item.title }}</view>
						<view class="channel-desc ellipsis">{{ item.description }}</view>
					</view>
				</view>
			</template>
			<template v-else>
				<view class="empty-channel">
					<u-icon name="info-circle" size="60rpx" color="#ccc"></u-icon>
					<view class="empty-text">暂未加入频道</view>
				</view>
			</template>
		</view>
	</u-popup>
</template>

<script>
export default {
	name: "ChannelSelector",
	props: {
		show: {
			type: Boolean,
			default: false
		},
		channelList: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {}
	},
	methods: {
		handleClose() {
			this.$emit('update:show', false)
			this.$emit('close')
		},
		handleChannelSelect(item) {
			this.$emit('select', item)
			this.handleClose()
		}
	}
}
</script>

<style lang="scss" scoped>
.channel-popup {
	width: 100%;
	max-height: 800rpx;
	padding: 30rpx;
	background-color: #fff;
	border-radius: 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.channel-title {
	font-size: 32rpx;
	font-weight: 600;
	text-align: center;
	margin-bottom: 30rpx;
}

.channel-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1px solid #f5f5f5;
	width: 100%;

	&:active {
		background-color: #f9f9f9;
	}
}

.channel-icon {
	width: 80rpx;
	margin-right: 20rpx;
	display: flex;
	justify-content: center;

	image {
		width: 60rpx;
		height: 60rpx;
	}
}

.channel-info {
	flex: 1;
	overflow: hidden;
}

.channel-name {
	font-size: 28rpx;
	font-weight: 500;
	margin-bottom: 6rpx;
}

.channel-desc {
	font-size: 24rpx;
	color: #999;
}

.empty-channel {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80rpx 0;
	width: 100%;

	.empty-text {
		font-size: 28rpx;
		color: #999;
		margin-top: 20rpx;
	}
}

.ellipsis {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
</style>
