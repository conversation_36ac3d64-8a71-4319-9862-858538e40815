<template>
	<u-popup :show="show" :safe-area-inset-bottom="false" mode="center" @close="handleClose" round="24" :maskCloseAble="false" :closeOnClickOverlay="false">
		<view class="fail-reason-modal">
			<view class="title">{{title}}</view>
			<view class="content">{{content}}</view>
			<slot></slot>
			
			<view class="page_btn">
				<view class="btn1" @click="handleCancel">{{cancelText}}</view>
				<view class="btn2" @click="handleConfirm">{{confirmText}}</view>
			</view>
		</view>
	</u-popup>
</template>

<script>
export default {
	name: 'FailReasonModal',
	props: {
		show: {
			type: Boolean,
			default: false
		},
		title: {
			type: String,
			default: '提示'
		},
		content: {
			type: String,
			default: ''
		},
		cancelText: {
			type: String,
			default: '取消'
		},
		confirmText: {
			type: String,
			default: '确定'
		}
	},
	methods: {
		handleClose() {
			this.$emit('update:show', false);
			this.$emit('close');
		},
		handleCancel() {
			this.$emit('update:show', false);
			this.$emit('cancel');
		},
		handleConfirm() {
			this.$emit('update:show', false);
			this.$emit('confirm');
		}
	}
}
</script>

<style lang="scss" scoped>
.fail-reason-modal {
	width: 88vw;
	padding: 45rpx;
	background: white;
	border-radius: 24rpx;
	z-index: 999;
}

.title {
	font-size: 34rpx;
	font-weight: 600;
	text-align: center;
	color: #333333;
}

.content {
	font-size: 30rpx;
	color: #545454;
	margin: 40rpx 0 60rpx 0;
	text-align: center;
}

.page_btn {
	display: flex;
	justify-content: space-around;
	align-items: center;
	margin-top: 32rpx;
	
	.btn1 {
		text-align: center;
		background: #e5e5e5;
		border-radius: 20rpx;
		width: 240rpx;
		padding: 20rpx 35rpx;
	}
	
	.btn2 {
		text-align: center;
		background: black;
		color: white;
		border-radius: 20rpx;
		width: 240rpx;
		padding: 20rpx 35rpx;
	}
}
</style> 