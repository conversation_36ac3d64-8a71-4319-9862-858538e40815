<template>
  <view class="page"
        :style="{ backgroundImage: `url(${backgroundImage})`, backgroundRepeat: 'no-repeat', backgroundSize: 'contain' }">

    <!--    <logingModel></logingModel>-->

    <view :style="{ height: '404rpx', position: 'relative', marginTop: '580rpx' }">

      <view class="user_video" @click="get_radio">
        <image class="user_video_gif" src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/fitment/合成 1.gif"></image>
        <image class="user_video_img" src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/形状结合@2x.png"></image>
        <view class="user_video_content">
          <view class="title">创作数字人视频</view>
          <view class="text">开启你的创作之旅</view>
        </view>
      </view>
      <!-- 声音克隆 -->
      <view class="clone" @click="go_getSound">
        <image src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/icon-声音克隆@2x.png">
        </image>
        <view class="title">声音克隆</view>
        <view class="title" style="font-weight: 500;font-size: 20rpx;font-family: ChillRoundGothic_Medium;">克隆音色，精准还原
        </view>
      </view>
      <view class="box_video" @click="go_batchVideo">
        <image src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/icon-批量视频@2x.png">
        </image>
        <view class="title">批量视频</view>
        <view class="text">高效量产，一键智作</view>
      </view>

      <view class="number_user_box1" @click="addNumberUser">
        <image
            src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/<EMAIL>">
        </image>
        <view class="title">定制数字人</view>
        <view class="text">形象克隆,分身有数</view>
      </view>

      <view class="official" @click="go_get_message">
        <image src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/icon-视频文案提取@2x.png"></image>
        <view class="title">视频文案提取</view>
        <view class="text">智能解析，文案速记</view>
      </view>
    </view>
    <view class="page_tool">
      <image src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/icon-工具助手@2x.png"></image>
      <view class="text_1">工具</view>
      <view>助手</view>
    </view>

    <!-- 	<scroll-view scroll-y="true" class="scroll-y" @scrolltolower="get_again">
    </scroll-view> -->

    <view class="box_list">
      <view v-for="item in listData" class="list_content" :key="item.id" @click="go_copy(item)">
        <image class="list_content_img" :src="item.icon"></image>
        <view class="list_content_text">
          <view class="ellipsis">{{ item.name }}</view>
          <view class="ellipsis-sub">{{ item.description }}</view>
        </view>
      </view>
    </view>

    <TabBar
        :tabItems="[{ iconActive: 'https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/app_tab/lv-index-icon-checked.png' }]"
        :index="0" active-color="#41b7f0" inactive-color="white"  custom-style="background:#061529" />
    <InsufficientPermissions v-if="page_model" @closeModel="close"></InsufficientPermissions>

    <vipVersion :pageShow="page_show" @show_model="show_model" @down_close="page_show = false" source="home"
                :fastPowerRange="configInfo.fastPowerRange" :numberType="number_type"></vipVersion>
    <globalLoginModal />
  </view>

</template>

<script>
import {
  getAppConfig,
  getUserType
} from '../../../api/numberUser/userType.js'
import {
  page_img_statr
} from '../../../api/getData.js'
import { mapGetters, mapActions } from 'vuex'
import TabBar from "@/components/fixedTabBar/index.vue";

export default {
  components: { TabBar },
  props: {
    configInfo: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      pageReady: false,
      page_show: false,
      model_login: false,
      listData: [],
      page_model: false,
      number_type: '',
      appName: '',
      page_img_statr: '',
      appId: ''
    }
  },
  computed: {
    ...mapGetters('user', ['isLogin', 'userInfo'])
  },
  watch: {
    '$appConfig.appInfo': {
      handler(newAppInfo) {
        if (newAppInfo && newAppInfo.appName) {
          this.appName = newAppInfo.appName;
        }
      },
      deep: true,  // 如果需要深度监听
      immediate: true  // 是否立即执行一次
    }
  },
  async mounted() {
    // uni.hideTabBar()
    this.page_img_statr = page_img_statr
    this.appId = uni.getAccountInfoSync().miniProgram.appId
    console.log(this.appId, 'this.appId');

    this.get_tool()
  },
  computed: {
    backgroundImage() {
      return 'https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/e068ee91830620e49608ef607ad1e85.png'
    }
  },
  onShow() {
    this.number_type = ''
    this.page_show = false
  },
  methods: {
    // 检查登录状态，如未登录则弹出登录框
    checkLoginStatus(targetUrl, options = {}) {
      if (!targetUrl && !options.callback) return false;

      return this.$checkLogin({
        success: (userData) => {
          // 登录成功，根据参数处理后续操作
          if (options.callback && typeof options.callback === 'function') {
            // 如果提供了回调函数，直接调用
            options.callback(userData);
          } else if (targetUrl) {
            // 如果提供了目标URL，则跳转
            uni.navigateTo({
              url: targetUrl,
              success: options.success,
              fail: options.fail,
              complete: options.complete
            });
          }
        },
        cancel: () => {
          // 用户取消登录，不执行后续操作
          uni.showToast({
            title: options.cancelTip || '需要登录后才能访问',
            icon: 'none'
          });

          if (options.onCancel && typeof options.onCancel === 'function') {
            options.onCancel();
          }
        },
        fail: (err) => {
          // 登录失败
          if (options.onFail && typeof options.onFail === 'function') {
            options.onFail(err);
          }
        }
      });
    },

    go_get_message() {
      this.checkLoginStatus('/subpkg/index/extractionCopy', {
        cancelTip: '需要登录后才能使用文案提取'
      });
    },

    go_batchVideo() {
      uni.navigateTo({
        url: '/subpkg/batchVideoHome/index'
      })
    },

    get_again() {

    },

    close(value) {
      this.page_model = value
    },

    show_model(value) {
      this.page_show = false
      this.page_model = value
    },

    addNumberUser() {
      this.checkLoginStatus(null, {
        callback: () => {
          this.number_type = ''
          if (this.configInfo.enableFastClone == '0') {
            this.page_show = true
          } else {
            uni.navigateTo({
              url: '/subpkg/index/numbe_user/index?type=pro'
            })
          }
        },
        cancelTip: '需要登录后才能定制数字人'
      });
    },

    go_copy(item) {
      console.log('item', item);
      // 直接跳转到文案页面，不检查登录状态
      uni.navigateTo({
        url: '/subpkg/index/page_copy?source=home',
        success: (res) => {
          res.eventChannel.emit('get_message', item)
        }
      });
    },

    go_getSound() {
      this.checkLoginStatus(null, {
        callback: () => {
          this.number_type = 'sound'
          if (this.configInfo.enableFastClone == '0') {
            this.page_show = true
          } else {
            uni.navigateTo({
              url: '/subpkg/index/get_sound/index?type=pro'
            })
          }
        },
        cancelTip: '需要登录后才能使用声音克隆'
      });
    },

    get_tool() {
      this.$api.get_authorization({ ext: this.appId }).then(res => {
        this.listData = res.rows
      })
    },

    get_radio() {
      this.checkLoginStatus('/subpkg/index/get_radio/index', {
        cancelTip: '需要登录后才能创作数字人视频'
      });
    }
  }
}
</script>

<style scoped lang="scss">

.page {
  padding: 32rpx;
  color:  #ffffff;
  font-size: 28rpx;
  background: #050a18;
  box-sizing: border-box;
  min-height: 100vh;
  padding-bottom: 100rpx;
  // 添加科技感背景渐变
  background-image: linear-gradient(135deg, #050a18 0%, #0a1931 50%, #101f3a 100%);
  position: relative;
  overflow: hidden;
}

// 添加背景装饰元素
.page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 10% 20%, rgba(0, 170, 255, 0.1) 0%, transparent 20%),
  radial-gradient(circle at 90% 80%, rgba(157, 78, 221, 0.1) 0%, transparent 20%);
  z-index: 0;
  pointer-events: none;
}

// 网格线背景
.page::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(rgba(0, 170, 255, 0.05) 1px, transparent 1px),
  linear-gradient(90deg, rgba(0, 170, 255, 0.05) 1px, transparent 1px);
  background-size: 40rpx 40rpx;
  z-index: 0;
  pointer-events: none;
}

.box_video {
  width: 270rpx;
  height: 188rpx;
  padding: 28rpx 16rpx 32rpx 16rpx;
  position: absolute;
  right: 0;
  bottom: 0;
  background-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/result.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;

  .title {
    font-weight: 800;
    font-size: 28rpx;
    color:  #ffffff;
  }

  .text {
    font-weight: 400;
    font-size: 20rpx;
    color: rgba(255, 255, 255, 0.8);
  }

  image {
    width: 36rpx;
    height: 36rpx;
    filter: drop-shadow(0 0 5px rgba(0, 170, 255, 0.7));
  }
}


.number_user_box1 {
  width: 302rpx;
  height: 200rpx;
  padding: 32rpx 16rpx;
  position: absolute;
  left: 0;
  top: 0;
  // background: #404040;
  // border-radius: 24rpx 24rpx 24rpx 24rpx;
  // border-image: linear-gradient(135deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)) 2 2;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  // align-items: center;
  background-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/定制数字人2倍.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin-bottom: 12rpx;

  .title {
    font-weight: 800;
    font-size: 28rpx;
    color: #FFFFFF;
  }

  .text {
    font-weight: 500;
    font-size: 20rpx;
    color: rgba(255, 255, 255, 0.8);
  }

  image {
    width: 36rpx;
    height: 36rpx;
  }
}

.official {
  width: 270rpx;
  height: 188rpx;
  padding: 28rpx 16rpx 32rpx 16rpx;
  position: absolute;
  left: 0;
  bottom: 0;
  // background: #404040;
  // border-radius: 24rpx 24rpx 24rpx 24rpx;
  // border-image: linear-gradient(135deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)) 2 2;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  // align-items: center;
  background-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/视频文案提取2倍.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .title {
    font-size: 28rpx;
    color: #FFFFFF;
    font-weight: 600;
  }

  .text {
    font-weight: 500;
    font-size: 20rpx;
    color: rgba(255, 255, 255, 0.8);
  }

  image {
    width: 36rpx;
    height: 36rpx;
  }
}

.mini-box {
  padding: 10rpx 20rpx;
  background: #404040;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  border-image: linear-gradient(135deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)) 2 2;
  text-align: center;
  margin-top: 16rpx;
  background-image: url('/static/index/sykl.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.page_tool {
  font-weight: 600;
  font-size: 36rpx;
  color: #FFFFFF;
  margin: 40rpx 0;
  display: flex;
  align-items: center;
  font-family: ChillRoundGothic_Bold;

  image {
    width: 40rpx;
    height: 40rpx;
    margin-right: 10rpx;
    filter: drop-shadow(0 0 5px rgba(0, 170, 255, 0.7));
  }

  // .text_1 {
  //   font-weight: bold;
  //   color: #01F675;
  //   line-height: 56rpx;
  //   text-align: left;
  //   font-style: normal;
  //   text-transform: none;
  //   margin-right: 4rpx;
  // }
}


.bg_number_user {
  position: absolute;
  left: 0rpx;
  bottom: 40rpx;
  width: 100%;
  height: 144rpx;

}

.bg_text {
  font-family: ChillRoundGothic_Bold;
  font-size: 40rpx;
  position: absolute;
  left: 0;
  bottom: 200rpx;
}

.bg_img {
  width: 300rpx;
  position: absolute;
  right: 0;
  bottom: 0;
}


.box_list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;

  .list_content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 30rpx;
    width: 332rpx;
    height: 140rpx;
    background-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/工具助手2倍.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    position: relative;

    .list_content_text {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;
    }

    .list_content_img {
      position: absolute;
      bottom: 16rpx;
      right: 16rpx;
      width: 48rpx;
      height: 48rpx;
    }
  }
}

.box_list::after {
  content: '';
  width: 320rpx;
}


.box_1 {
  width: 90vw;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;

  .first-text {
    border-radius: 30rpx;
    width: 59vw;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;
    background: rgb(37, 234, 209);
    color: black;
    height: 140rpx;
  }

  .add {
    width: 30vw;
    color: white;
    background: black;
    display: inline-block;
    border-radius: 50%;
    width: 60rpx;
    height: 60rpx;
    text-align: center;
    font-size: 40rpx;
  }
}

.box_2 {
  width: 90vw;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user_video {
  // background: $project_1_bg;
  position: absolute;
  left: 208rpx;
  top: 75rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 36rpx;
  width: 276rpx;
  height: 276rpx;
  border-radius: 50%;
  color: #333333;
  font-weight: 600;
  font-size: 32rpx;
  font-family: ChillRoundGothic_Medium;
  overflow: hidden;
  z-index: 3;

  .user_video_gif {
    z-index: 1;
    width: 110%;
    height: 110%;
    position: absolute;
    top: -65%;
    left: -65%;
    transform: translate(55%, 55%);
  }

  .user_video_content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    gap: 10rpx;
    z-index: 2;

    .title {
      font-weight: 800;
      font-size: 28rpx;
      color: #FFFFFF;
    }

    .text {
      font-size: 20rpx;
      color: rgba(255, 255, 255, 0.8);
      line-height: 28rpx;
    }
  }

  .user_video_img {
    width: 72rpx;
    height: 72rpx;
    z-index: 2;
  }
}

.clone {
  // background: $project_1_bg;
  position: absolute;
  width: 302rpx;
  height: 200rpx;
  top: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  padding: 32rpx 16rpx;
  border-radius: 20rpx;
  font-weight: 800;
  color: #FFFFFF;
  background-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/result (1).png');
  background-size: 100% 100%;
  background-repeat: no-repeat;

  image {
    width: 36rpx;
    height: 36rpx;
  }

  .title {
    font-weight: 800;
    font-size: 28rpx;
    color: #FFFFFF;
  }

  .text {
    font-weight: 500;
    font-size: 20rpx;
    color: rgba(255, 255, 255, 0.8);
  }


}

.scroll-y {
  height: 400rpx;
}

.ellipsis {
  font-weight: bold;
  font-size: 28rpx;
  color: #FFFFFF;
  line-height: 40rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.ellipsis-sub {
  font-weight: 400;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
}
</style>
