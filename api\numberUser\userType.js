import { requests } from '../../utils/request.js'

export const getUserType = (data) => {
	return requests({
		url: '/user/appconfig',
		method: 'get',
		data: data
	})
}
export const getAppConfig = (data) => {
	return requests({
		url: '/user/appconfigv2',
		method: 'get',
		data: data
	})
}

export const getVoiceList = (data) => {
	return requests({
		url: '/user/voice/list',
		method: 'get',
		data: data
	})
}

export const getCreationList = (data) => {
	return requests({
		url: '/user/creation/list',
		method: 'get',
		data: data
	})
}


export const postTemplateSave = (data) => {
	return requests({
		url: '/user/template/save',
		method: 'post',
		data: data
	})
}

export const getTemplateList = (data) => {
	return requests({
		url: '/user/template/list',
		method: 'get',
		data: data
	})
}


export const getPower = (data) => {
	return requests({
		url: '/user/power',
		method: 'get',
		data: data
	})
}

export const postVoiceName = (data) => {
	return requests({
		url: '/user/voice/rename',
		method: 'post',
		data: data
	})
}






















