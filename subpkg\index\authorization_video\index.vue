<template>
	<view style="background: black;height: 100vh;">
		<!-- 授权要求模态框 -->
		<projectModel :btn="false" v-if="showAuthModal" title="授权要求" content="请视频本人,一字不落的念出授权文案,以完成授权录制.">
			<view :class="countdown > 0 ? 'page_btn' : 'page_btn_2'" @click="closeAuthModal">
				好的
				<text v-if="countdown > 0">({{countdown}}s)</text>
			</view>
		</projectModel>


		<projectModel :btn="false" v-if="showPermissionModal" title="权限提示" :content="permissionMessage">
			<view class="page_btn_2" @click="handlePermissionAction">
				{{permissionActionText}}
			</view>
		</projectModel>


		<!-- 摄像头组件 -->
		<camera v-if="hasCameraPermission" device-position="front" flash="off" style="width:100vw;height:100vh">
		</camera>

		<!-- 授权文案 -->
		<view class="page_image" v-if="hasCameraPermission">
			我是XXX(真实姓名),我授权{{appName}}使用视频中的肖像权、声音,为我生成定制数字人及声音,并在本人{{appName}}账号中创作使用。
		</view>

		<!-- 录制按钮 -->
		<view class="play_btn" v-if="hasCameraPermission">
			<image v-if="!isRecording" @tap="startRecord" src="/static/home/<USER>/mti-luzhi.png"></image>
			<image v-if="isRecording" @tap="stopRecord" src="/static/home/<USER>/luzhi.png"></image>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				cameraContext: null,
				isRecording: false,
				showAuthModal: false,
				showPermissionModal: false,
				countdown: 3,
				appName: '',
        pageType:'',
				hasCameraPermission: false,
        hasMicrophonePermission: false,
				permissionMessage: '',
				permissionActionText: '去设置',
        countdownTimer:null
			}
		},
		onLoad(options) {
			this.appName = this.$appConfig.appInfo.appName
      this.pageType = options.type
			this.startCountdown();
			this.checkPermissions();
		},
    onUnload() {
      clearInterval(this.countdownTimer);
    },
		methods: {
			// 开始倒计时
      startCountdown() {
        this.countdownTimer = setInterval(() => {
          this.countdown--;
          if (this.countdown <= 0) {
            clearInterval(this.countdownTimer);
            this.showAuthModal = false;
          }
        }, 1000);
      },

      // 检查所有所需权限
      async checkPermissions() {
        await this.checkCameraPermission();
        await this.checkMicrophonePermission();
      },
      
			// 检查摄像头权限
			async checkCameraPermission() {
				try {
					const res = await uni.getSetting();
					if (!res.authSetting['scope.camera']) {
						// 未授权，尝试获取权限
						await this.requestCameraPermission();
					} else {
						// 已授权
						this.hasCameraPermission = true;
						this.initCamera();

						this.showAuthModal = true
					}
				} catch (error) {
					console.error('获取权限设置失败:', error);
					this.showPermissionPrompt('获取摄像头权限失败，请重试');
				}
			},

      // 检查麦克风权限
      async checkMicrophonePermission() {
        try {
          const res = await uni.getSetting();
          if (!res.authSetting['scope.record']) {
            // 未授权，尝试获取权限
            await this.requestMicrophonePermission();
          } else {
            // 已授权
            this.hasMicrophonePermission = true;
          }
        } catch (error) {
          console.error('获取麦克风权限设置失败:', error);
          this.showPermissionPrompt('获取麦克风权限失败，请重试');
        }
      },

			// 请求摄像头权限
			async requestCameraPermission() {
				try {
					const res = await uni.authorize({
						scope: 'scope.camera'
					});
					this.hasCameraPermission = true;
					this.initCamera();
				} catch (error) {
					console.error('授权失败:', error);
					if (error.errMsg.includes('auth deny') || error.errMsg.includes('auth denied')) {
						this.showPermissionPrompt('需要摄像头权限才能录制视频，请前往设置手动开启');
					} else {
						this.showPermissionPrompt('获取摄像头权限失败，请重试');
					}
				}
			},

      // 请求麦克风权限
      async requestMicrophonePermission() {
        try {
          const res = await uni.authorize({
            scope: 'scope.record'
          });
          this.hasMicrophonePermission = true;
        } catch (error) {
          console.error('麦克风授权失败:', error);
          if (error.errMsg.includes('auth deny') || error.errMsg.includes('auth denied')) {
            this.showPermissionPrompt('需要麦克风权限才能录制视频，请前往设置手动开启');
          } else {
            this.showPermissionPrompt('获取麦克风权限失败，请重试');
          }
        }
      },

			// 显示权限提示
			showPermissionPrompt(message) {
				this.permissionMessage = message;
				this.showPermissionModal = true;

				this.showAuthModal = false
			},

			// 处理权限操作
			handlePermissionAction() {
				this.showPermissionModal = false;
				uni.openSetting({
					success: (res) => {
						if (res.authSetting['scope.camera']) {
							this.hasCameraPermission = true;
							this.initCamera();
						} else {
							this.showPermissionPrompt('您仍未授权摄像头权限，将无法使用录制功能');
              return;
						}
            
            if (res.authSetting['scope.record']) {
              this.hasMicrophonePermission = true;
            } else {
              this.showPermissionPrompt('您仍未授权麦克风权限，将无法使用录制功能');
            }
					},
					fail: () => {
						this.showPermissionPrompt('打开设置失败，请手动前往设置');
					}
				});
			},

			// 初始化摄像头
			initCamera() {
				this.cameraContext = uni.createCameraContext();
			},

			// 关闭授权模态框
			closeAuthModal() {
				if (this.countdown <= 0) {
					this.showAuthModal = false;
				}
			},

			// 开始录制
			startRecord() {
				if (!this.hasCameraPermission) {
					this.showPermissionPrompt('需要摄像头权限才能录制视频');
					return;
				}
        
        if (!this.hasMicrophonePermission) {
          this.showPermissionPrompt('需要麦克风权限才能录制视频');
          return;
        }

				this.isRecording = true;
				this.cameraContext.startRecord({
					timeoutCallback: () => {
						uni.showModal({
							content: '录像已超过最大时长',
							showCancel: false
						});
						this.stopRecord();
					}
				});
			},

			// 停止录制
			stopRecord() {
        let that = this
				this.isRecording = false;
				this.cameraContext.stopRecord({
					success: (res) => {
						uni.navigateTo({
							url: `/subpkg/index/authorization_video/save?type=${that.pageType}`,
							success: (navRes) => {
								navRes.eventChannel.emit('get_path', {
									path: res.tempVideoPath
								});
							}
						});
					},
					fail: (error) => {
						console.error('停止录制失败:', error);
						uni.showToast({
							title: '停止录制失败',
							icon: 'none'
						});
					}
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	.page_image {
		width: 80vw;
		height: 20vh;
		position: absolute;
		top: 20rpx;
		left: 10vw;
		color: white;
		font-size: 38rpx;
		line-height: 60rpx;
		z-index: 10;
	}

	.play_btn {
		position: absolute;
		bottom: 50rpx;
		left: calc(50vw - 50rpx);
		z-index: 10;

		image {
			width: 135rpx;
			height: 135rpx;
		}
	}

	.page_btn {
		width: 100%;
		background: #e8e8e8;
		color: #919191;
		text-align: center;
		padding: 20rpx;
		margin-top: 20rpx;
		border-radius: 20rpx;
	}

	.page_btn_2 {
		width: 100%;
		background: black;
		color: white;
		text-align: center;
		padding: 20rpx;
		margin-top: 20rpx;
		border-radius: 20rpx;
	}
</style>
