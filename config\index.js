import { getAppBaseInfo } from "@/api/app/config";

const ENV_MAP = {
    develop: 'development',
    trial: 'development',
    release: 'production'
};

const baseConfig = {
    development: {
        baseUrl: 'http://localhost:8080/app',
        wsUrl: 'ws://localhost:8080/ws',
        debug: true
    },
    production: {
        baseUrl: 'https://xiaoa.co/prod-api/app',
        wsUrl: 'wss://xiaoa.co/ws',
        debug: false
    }
};

export async function getAppConfig() {
    let appId = '';
    let envVersion = 'develop';
    let version = '';
    let appName = '';
    let ossPath ='https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/app_tab'
    let wxTemplateId ='';     //通知模板id

    if (typeof uni !== 'undefined' && uni.getAccountInfoSync) {
        const info = uni.getAccountInfoSync();
        appId = info.miniProgram.appId || '';
        version = info.miniProgram.version || '1.0.0';
        envVersion = info.miniProgram.envVersion || 'develop';

        const res = await getAppBaseInfo({ appId });
        appName = res.data.name || '';
        wxTemplateId = res.data.wxTemplateId
    }

    const currentEnv = ENV_MAP[envVersion] || 'development';

    return {
        ...baseConfig[currentEnv],
        appInfo: {
            appId,
            envVersion,
            wxTemplateId,
            version,
            appName,
            ossPath
        }
    };
}
