<template>
	<view class="page">

		<vipVersion @show_model="show_model" :pageShow="page_show" @down_close="page_show = false" source="me—list"
			:fastPowerRange="fastPowerRange"  :numberType="number_type"></vipVersion>
		<InsufficientPermissions v-if="page_model" @closeModel="close"></InsufficientPermissions>

		<view class="box">
			<view  class="music_box" @click="add_music">
				<view style="display: flex;align-items: center;">
					<view class="add">
						+
					</view>
					<view>新建声音</view>
				</view>
			</view>

			<view v-for="item in list" class="music_box" >
				<view class="music_view">

					<image v-if="page_music_id == item.outId" @click="stop_music(item)" :src="page_img_statr + 'project1_home4_music_playing.gif'"></image>
					<image  v-else @click="play_music(item)" src="/static/home/<USER>/get_radio_bf.png"></image>


					<view>{{item.name}}</view>
					<image v-if="item.type == 'pro'" mode="heightFix" style="height: 30rpx;"
						src="/static/index/pro_vip.png"></image>
				</view>

				<text @click="() =>{edit_model = true;page_item = item}" style="color: #666666;">. . .</text>

			</view>
		</view>


		<u-popup :show="edit_model" :round="20" mode="bottom" @close="edit_model = false">
				<view class="bottom_model">
					<view style="text-align: center;font-weight: 600;">操作</view>

					<view class="tool" @click="edit">
						<u-icon name="edit-pen" size="50rpx"></u-icon>
						<view>编辑</view>
					</view>
					<view class="tool" @click="page_del">
						<u-icon name="edit-pen" size="50rpx"></u-icon>
						<view>删除</view>
					</view>
				</view>
		</u-popup>


		<projectModel
		  v-if="edit_name"
		  title="音频名称"
		  @btn_close="btn_close"
		  @btn_save="btn_save"
		>
			<view style="margin: 40rpx 0;">
				<u--input
			   	    :border="false"
				    placeholder="请输入内容"
				    border="surround"
				    v-model="page_name"
				    @change="change"
					:maxlength="8"
				  ></u--input>
			</view>
		</projectModel>



		<projectModel
		  v-if="del_name"
		  title="删除音频"
		  :content="'是否确认删除当前音频 ' + page_name "
		  @btn_close="del_name = false"
		  @btn_save="save_del"
		>
		</projectModel>

	</view>
</template>

<script>
	import {requests}  from '../../utils/request.js'
	import {page_img_statr} from '../../api/getData.js'
	import {
		getUserType,
		getVoiceList,
		postVoiceName
	} from '../../api/numberUser/userType.js'
	export default {
		data() {
			return {
				page_img_statr:'',
				edit_model:false,
				del_name:false,
				list: [],
				page_show: false,
				page_item:'',
				page_model: false,
				number_type:'',
				edit_name:false,

				page_name:'',

				innerAudioContext: null, // 音频上下文
				page_music_id:''

			}
		},
		onLoad() {
			this.page_img_statr = page_img_statr
			this.get_voice_list()
			// 创建音频上下文
			this.innerAudioContext = uni.createInnerAudioContext({
      useWebAudioImplement: true
    })
			// 监听音频自然结束
			this.innerAudioContext.onEnded(() => {
				this.page_music_id = ''
			})
		},
		onHide() {
			this.innerAudioContext.stop()
			clearInterval(this.page_setInterval)
		},
		onUnload() {
			// 页面卸载时销毁音频
			if (this.innerAudioContext) {
				this.innerAudioContext.destroy()
			}
		},
		onShow() {
			this.page_show = false
		},

		methods: {
			play_music(item){
				this.page_music_id = item.outId
				this.innerAudioContext.src = item.demoUrl;
				this.innerAudioContext.play();
			},
			stop_music(){
				this.page_music_id = ''
				this.innerAudioContext.stop();
			},


			save_del(){
				requests({
					url:`/user/voice/remove/${this.page_item.id}`
				}).then(res =>{
          uni.showToast({title:'删除成功',icon:'none'})
					this.get_voice_list()
				})
				this.del_name = false
			},
			page_del(){
				this.del_name = true
				this.edit_model = false
			},

			get_voice_list(){
				getVoiceList({
					pageNum: 1,
					pageSize: 100
				}).then(res => {
					this.list = res.rows
				})
			},

			btn_close(){
				this.edit_name = false
				this.edit_model = true
			},
			btn_save(){
				postVoiceName({
					name:this.page_name,
					id:this.page_item.id
				}).then(res =>{
          uni.showToast({title:'修改成功',icon:'none'})
					this.get_voice_list()
				})
				this.edit_name = false
			},
			edit(){
				this.edit_name = true
				this.edit_model = false
				this.page_name = this.page_item.name
			},

			close(value) {
				this.page_model = value
			},
			show_model(value) {
				this.page_show = false
				this.page_model = value
			},


			add_music(){
				this.number_type = 'sound'
				getUserType().then(res => {
					if (res.data.enableFastClone == '0') {
						this.page_show = true
						this.fastPowerRange = res.data.fastPowerRange
					} else {
						uni.navigateTo({
							url:'/subpkg/index/get_sound/index?type=pro'
						})
					}
				})
			}



		}
	}
</script>

<style scoped lang="scss">
	.page {
		font-size: 28rpx;
		padding: 40rpx;
		.box {
			.add {
				width: 80rpx;
				height: 80rpx;
				border-radius: 20rpx;
				background: $project_1_bg;
				text-align: center;
				line-height: 70rpx;
				font-size: 60rpx;
				color: white;
				margin-right: 40rpx;
			}
		}

		.music_box {
			border-radius: 24rpx;
			margin-top: 20rpx;
			padding: 20rpx 40rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			background: #F3F5F8;

			.music_view {
				display: flex;
        justify-content: center;
				align-items: center;

				view {
					margin: 0 20rpx;
				}

				image {
					width: 80rpx;
					height: 80rpx;
					margin-right: 20rpx;
				}
        text{
          display: inline-block;

          padding: 20rpx;
          align-self: end;
        }
			}
		}
		.bottom_model{
			min-height: 200rpx;
			padding: 40rpx 20rpx;
			background: $project_1_bg_color;
			border-radius: 40rpx;

			.tool{
				margin-top: 40rpx;
				display: flex;
				align-items: center;
				background: white;
				padding:30rpx 40rpx;
				margin-bottom: 40rpx;
				border-radius: 20rpx;
				view{
					margin-left: 10rpx;
				}
			}
		}
	}
</style>
