<template>
  <view class="material-container">
    <!-- 素材展示位置选择器 -->
    <view class="display-position">
      <view class="position-title">
        <view class="title-text">素材展示位置</view>
        <image class="help-icon" src="/static/问号.png" @click="showPositionInfo"></image>
      </view>
      <view class="position-selector">
        <view class="selector-btn" :class="{ 'inactive': assetInfo.position !== 'full' }"
          @click="setDisplayMode('full')">
          <view class="btn-text">全屏</view>
        </view>
        <view class="selector-btn" :class="{ 'inactive': assetInfo.position !== 'half' }"
          @click="setDisplayMode('half')">
          <view class="btn-text">半屏</view>
        </view>
      </view>
    </view>

    <!-- 素材列表 -->
    <view class="material-section">
      <view class="section-title">我的素材（共计{{ assetInfo.selectedIds.length }}个）</view>
      <view class="material-grid">
        <view class="material-item" v-for="(item, index) in assetInfo.selectedIds" :key="index"
          :style="{ backgroundImage: `url(${getPreviewUrl(item.background)})` }">
          <!-- 右上角删除按钮 -->
          <view class="delete-btn" @click.stop="deleteMaterial(index)">
            <image class="delete-icon" src="/static/删除.png"></image>
          </view>
          <!-- 加载中状态 -->
          <view class="loading-overlay" v-if="item.loading">
            <u-loading-icon mode="circle" size="30"></u-loading-icon>
          </view>
        </view>

        <!-- 新增素材按钮 -->
        <view class="add-material" @click="showAddSourcePopup">
          <view class="add-content">
            <image class="add-icon" src="/static/添加素材.png"></image>
            <view class="add-text">新增素材</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar" :style="{ paddingBottom: safeAreaBottom + 'px' }">
      <view class="selected-count">
        <view class="count-text">共{{ assetInfo.selectedIds.length }}个</view>
      </view>
      <view class="bottom-bar-btn">
        <view class="prev-btn" @click="prevStep">
          <view class="btn-text">上一步</view>
        </view>
        <view class="next-btn" @click="nextStep">
          <view class="btn-text">下一步</view>
        </view>
      </view>
    </view>

    <!-- 素材展示位置说明弹窗 -->
    <u-popup :show="positionInfoVisible" @close="() => positionInfoVisible = false" mode="center" round="20"
      :closeable="true">
      <view class="position-info-popup">
        <view class="popup-title">素材展示位置</view>

        <view class="position-options">
          <view class="position-option" @click="selectPosition('half')"
            :class="{ 'selected': tempDisplayMode === 'half' }">
            <view class="option-preview half-screen" :style="{ backgroundImage: `url(/static/半屏.webp)` }">

            </view>
            <text class="option-text">画面底部</text>
            <view class="select-indicator" v-if="tempDisplayMode === 'half'"></view>
          </view>

          <view class="position-option" @click="selectPosition('full')"
            :class="{ 'selected': tempDisplayMode === 'full' }">
            <view class="option-preview full-screen" :style="{ backgroundImage: `url(/static/全屏.webp)` }">

            </view>
            <text class="option-text">画面全屏</text>
            <view class="select-indicator" v-if="tempDisplayMode === 'full'"></view>
          </view>
        </view>

        <view class="confirm-button" @click="confirmPosition">
          <text class="confirm-text">确认</text>
        </view>
      </view>
    </u-popup>

    <!-- 添加途径弹窗 -->
    <u-popup :show="addSourceVisible" @close="addSourceVisible = false" mode="bottom" round="20">
      <view class="add-source-popup">
        <view class="popup-header">
          <text class="popup-title">请选择添加途径</text>
          <image class="close-icon" src="/static/删除.png" @click="addSourceVisible = false"></image>
        </view>

        <view class="source-option" @click="selectAddSource('library')">
          <text class="option-text">从「我的素材库」中选择</text>
        </view>

        <view class="source-option" @click="selectAddSource('channel')">
          <text class="option-text">从「我加入的频道」中选择</text>
        </view>

        <view class="source-option" @click="selectAddSource('upload')">
          <text class="option-text">上传视频/图片</text>
        </view>

        <!-- 底部安全区适配 -->
        <view class="safe-area-bottom"></view>
      </view>
    </u-popup>

    <!-- 频道选择弹窗 -->
    <channel-selector :show="channelSelectorVisible" :channelList="channelList" @close="channelSelectorVisible = false"
      @select="handleChannelSelect">
    </channel-selector>

    <!-- 素材为空确认弹窗 -->
    <u-popup :show="materialEmptyConfirmVisible" @close="materialEmptyConfirmVisible = false"
      :safeAreaInsetBottom="false" mode="center" round="20" :closeable="false">
      <view class="power-estimate-popup">
        <view class="popup-title">提示</view>
        <view class="popup-content">
          <text>当前没有选择任何素材，是否进入下一步？</text>
        </view>
        <view class="popup-buttons">
          <view class="cancel-btn" @click="materialEmptyConfirmVisible = false">
            <text class="btn-text">取消</text>
          </view>
          <view class="confirm-btn" @click="confirmNextStepWithoutMaterial">
            <text class="btn-text">好的</text>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { getGroup } from '@/api/numberUser/copy.js'
import ChannelSelector from '@/subpkg/index/components/channel.vue'
import { handleMediaUpload } from "@/utils/upload-media.js"
import { getMeterialResult } from "@/api/myMaterials"

export default {
  components: {
    ChannelSelector
  },
  data() {
    return {
      tempDisplayMode: 'half', // 弹窗中临时选择的展示模式
      positionInfoVisible: false, // 控制素材展示位置说明弹窗
      addSourceVisible: false, // 控制添加途径弹窗
      channelSelectorVisible: false, // 控制频道选择弹窗
      channelList: [], // 频道列表
      safeAreaBottom: 0,
      materialEmptyConfirmVisible: false,
      // 轮询计时器
      pollTimers: {},
      // 最大轮询次数
      maxPollCount: 30
    };
  },
  props: {
    assetInfo: {
      type: Object,
      default: () => ({
        selectedIds: [],
        position: 'half'
      })
    }
  },
  mounted() {
    this.safeAreaBottom = uni.getSystemInfoSync().safeAreaInsets.bottom + uni.upx2px(24);
    // 获取频道列表
    this.getChannelList();

    // 添加素材选择监听
    uni.$on('user-material-selected', this.handleUserMaterialSelected);
  },
  methods: {
    // 获取频道列表
    getChannelList() {
      getGroup().then(res => {
        this.channelList = res.data;
      }).catch(err => {
        console.error('获取频道列表失败:', err);
      });
    },

    // 获取素材预览URL
    getPreviewUrl(url) {
      if (!url) return '';
      // 如果是视频文件，添加OSS视频截图参数
      if (url.indexOf('.mp4') !== -1) {
        return `${url}?x-oss-process=video/snapshot,t_1000,f_jpg,w_800,h_450`;
      }
      return url;
    },

    // 显示添加途径弹窗
    showAddSourcePopup() {
      this.addSourceVisible = true;
    },

    // 选择添加途径
    selectAddSource(type) {
      this.addSourceVisible = false;

      switch (type) {
        case 'library':
          // 从我的素材库中选择
          uni.navigateTo({
            url: '/subpkg/myMaterials/index?select=true&maxSelect=9&returnSource=selectMaterial'
          });
          break;

        case 'channel':
          // 显示频道选择弹窗
          this.channelSelectorVisible = true;
          break;

        case 'upload':
          // 上传视频/图片
          this.uploadMedia();
          break;
      }
    },

    // 上传媒体文件
    async uploadMedia() {
      try {
        uni.showLoading({
          title: '文件上传中...'
        });

        // 调用上传方法
        const { fileUrl, materialId } = await handleMediaUpload();

        uni.hideLoading();

        if (fileUrl) {
          console.log('上传成功:', fileUrl, materialId);

          // 创建新素材列表
          const newSelectedIds = [...this.assetInfo.selectedIds];

          // 将选中的图片添加到素材列表中，设置loading状态
          const newMaterial = {
            id: materialId,
            outId: materialId,
            background: fileUrl,
            loading: true
          };

          newSelectedIds.push(newMaterial);

          // 更新素材信息
          this.updateAssetInfo({
            ...this.assetInfo,
            selectedIds: newSelectedIds
          });

          // 开始轮询检查上传结果
          this.startPolling(materialId);
        }
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '上传失败，请重试',
          icon: 'none'
        });
        console.error('上传失败:', error);
      }
    },

    // 开始轮询检查保存结果
    startPolling(materialId) {
      let pollCount = 0;

      // 创建轮询定时器
      this.pollTimers[materialId] = setInterval(async () => {
        try {
          // 轮询次数+1
          pollCount++;

          // 超过最大轮询次数，显示失败
          if (pollCount > this.maxPollCount) {
            clearInterval(this.pollTimers[materialId]);
            delete this.pollTimers[materialId];

            // 查找素材并标记失败
            const materialIndex = this.assetInfo.selectedIds.findIndex(item => item.id === materialId);
            if (materialIndex !== -1) {
              const newSelectedIds = [...this.assetInfo.selectedIds];
              newSelectedIds[materialIndex].loading = false;
              newSelectedIds[materialIndex].failed = true;

              this.updateAssetInfo({
                ...this.assetInfo,
                selectedIds: newSelectedIds
              });

              uni.showToast({
                title: '素材处理失败',
                icon: 'none'
              });
            }
            return;
          }

          // 获取素材保存结果
          const result = await getMeterialResult(materialId);
          console.log('轮询素材结果:', result);

          // 如果处理完成
          if (result.data) {
            clearInterval(this.pollTimers[materialId]);
            delete this.pollTimers[materialId];

            // 查找素材并更新状态
            const materialIndex = this.assetInfo.selectedIds.findIndex(item => item.id === materialId);
            if (materialIndex !== -1) {
              const newSelectedIds = [...this.assetInfo.selectedIds];
              newSelectedIds[materialIndex].loading = false;

              // 更新素材信息
              if (result.data.fileUrl) {
                newSelectedIds[materialIndex].background = newSelectedIds[materialIndex].background;
                newSelectedIds[materialIndex].outId = result.data.outId;
                newSelectedIds[materialIndex].id = result.data.outId;
              }

              this.updateAssetInfo({
                ...this.assetInfo,
                selectedIds: newSelectedIds
              });
            }

            // 检查是否所有素材都已处理完成
            this.checkAllMaterialsReady();
          }
        } catch (error) {
          console.error('轮询素材状态失败:', error);
        }
      }, 3000); // 每3秒轮询一次
    },

    // 检查所有素材是否都已准备好
    checkAllMaterialsReady() {
      const allReady = this.assetInfo.selectedIds.every(material => !material.loading);
      if (allReady) {
        console.log('所有素材都已处理完成');
      }
    },

    // 处理频道选择
    handleChannelSelect(channel) {
      // 隐藏频道选择弹窗
      this.channelSelectorVisible = false;

      // 跳转到message页面，传入频道信息，并指定只显示素材
      uni.navigateTo({
        url: '/subpkg/home_three/message?from=selectMaterial',
        success: (res) => {
          // 通过事件通道传送数据
          res.eventChannel.emit('get_message', {
            ...channel,
            selectMode: true // 添加标记，表示是选择素材模式
          });

          // 监听返回的素材
          res.eventChannel.on('selected_materials', (materials) => {
            if (materials && materials.length) {
              console.log(materials, 'materials');

              // 创建新素材列表
              const newSelectedIds = [...this.assetInfo.selectedIds];

              // 添加返回的素材到素材列表
              materials.forEach(item => {
                newSelectedIds.push({
                  id: item.outId || item.id, // 优先使用outId
                  outId: item.outId || item.id, // 保存素材outId
                  background: item.content
                });
              });

              // 更新素材信息
              this.updateAssetInfo({
                ...this.assetInfo,
                selectedIds: newSelectedIds
              });

              uni.showToast({
                title: `已添加${materials.length}个素材`,
                icon: 'none'
              });
            }
          });
        }
      });
    },

    // 删除素材
    deleteMaterial(index) {
      uni.showModal({
        title: '提示',
        content: '确定要删除该素材吗？',
        success: (res) => {
          if (res.confirm) {
            const materialId = this.assetInfo.selectedIds[index].id;

            // 如果有轮询计时器，清除它
            if (this.pollTimers[materialId]) {
              clearInterval(this.pollTimers[materialId]);
              delete this.pollTimers[materialId];
            }

            const newSelectedIds = [...this.assetInfo.selectedIds];
            newSelectedIds.splice(index, 1);

            // 更新素材信息
            this.updateAssetInfo({
              ...this.assetInfo,
              selectedIds: newSelectedIds
            });
          }
        }
      });
    },

    // 显示素材展示位置说明弹窗
    showPositionInfo() {
      this.tempDisplayMode = this.assetInfo.position; // 初始化临时选择为当前选择
      this.positionInfoVisible = true;
    },

    // 在弹窗中选择展示位置
    selectPosition(mode) {
      this.tempDisplayMode = mode;
    },

    // 确认展示位置选择
    confirmPosition() {
      // 更新素材信息
      this.updateAssetInfo({
        ...this.assetInfo,
        position: this.tempDisplayMode
      });
      this.positionInfoVisible = false;
    },

    // 设置展示模式（全屏/半屏）
    setDisplayMode(mode) {
      // 更新素材信息
      this.updateAssetInfo({
        ...this.assetInfo,
        position: mode
      });
    },

    // 更新素材信息
    updateAssetInfo(newAssetInfo) {
      this.$emit('saveAssetInfo', newAssetInfo);
    },

    // 上一步
    prevStep() {
      uni.$emit('batch-video-prev-step');
    },

    // 下一步
    nextStep() {
      if (this.assetInfo.selectedIds.length === 0) {
        this.materialEmptyConfirmVisible = true;
      } else {
        // 切换到下一步
        uni.$emit('batch-video-next-step');
      }
    },

    // 确认下一步（无素材）
    confirmNextStepWithoutMaterial() {
      this.materialEmptyConfirmVisible = false;
      // 切换到下一步
      uni.$emit('batch-video-next-step');
    },

    // 处理从素材库选择的素材
    handleUserMaterialSelected(materials) {
      if (materials && materials.length > 0) {
        console.log('收到选中的素材:', materials);

        // 创建新素材列表
        const newSelectedIds = [...this.assetInfo.selectedIds];

        // 添加选择的素材到素材列表
        materials.forEach(item => {
          newSelectedIds.push({
            id: item.id, // 素材库返回的id
            outId: item.id, // 使用id作为outId
            background: item.coverUrl // 使用coverUrl作为预览图
          });
        });

        // 更新素材信息
        this.updateAssetInfo({
          ...this.assetInfo,
          selectedIds: newSelectedIds
        });

        uni.showToast({
          title: `已添加${materials.length}个素材`,
          icon: 'none'
        });
      }
    }
  },
  beforeDestroy() {
    uni.$off('user-material-selected', this.handleUserMaterialSelected);

    // 清除所有轮询计时器
    Object.keys(this.pollTimers).forEach(id => {
      clearInterval(this.pollTimers[id]);
    });
    this.pollTimers = {};
  }
};
</script>

<style lang="scss">
.material-container {
  background-color: #f3f5f8;
  width: 100%;
  height: 100%;
  padding-bottom: 140rpx; // 为底部操作栏预留空间

  // 素材展示位置选择器
  .display-position {
    background-color: #ffffff;
    border-radius: 24rpx;
    width: 686rpx;
    height: 120rpx;
    margin: 32rpx auto;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 32rpx;

    .position-title {
      display: flex;
      flex-direction: row;
      align-items: center;

      .title-text {
        color: #333333;
        font-size: 28rpx;
        font-weight: bold;
        line-height: 40rpx;
        margin-right: 8rpx;
      }

      .help-icon {
        width: 32rpx;
        height: 32rpx;
      }
    }

    .position-selector {
      background-color: #f3f5f8;
      border-radius: 40rpx;
      width: 208rpx;
      height: 72rpx;
      display: flex;
      flex-direction: row;
      align-items: center;

      .selector-btn {
        width: 96rpx;
        height: 56rpx;
        border-radius: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 8rpx;

        &:not(.inactive) {
          background: linear-gradient(135deg, #22232c 0%, #0f0f0f 100%);

          .btn-text {
            color: #f3f5f8;
            font-weight: bold;
          }
        }

        &.inactive {
          background-color: transparent;

          .btn-text {
            color: #999999;
          }
        }

        .btn-text {
          font-size: 24rpx;
          line-height: 32rpx;
        }
      }
    }
  }

  // 素材区域
  .material-section {
    margin: 0 32rpx;

    .section-title {
      color: #333333;
      font-size: 32rpx;
      font-weight: bold;
      line-height: 48rpx;
      margin-bottom: 24rpx;
    }

    .material-grid {
      display: grid;
      grid-template-columns: repeat(4, 160rpx);
      grid-gap: 16rpx;
      justify-content: start;

      .material-item {
        width: 160rpx;
        height: 160rpx;
        border-radius: 16rpx;
        position: relative;
        background-size: cover;
        background-position: center;

        // 右上角删除按钮
        .delete-btn {
          position: absolute;
          top: 8rpx;
          right: 8rpx;
          width: 32rpx;
          height: 32rpx;
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 2;

          .delete-icon {
            width: 24rpx;
            height: 24rpx;
          }

          &:active {
            background-color: rgba(0, 0, 0, 0.7);
          }
        }

        // 加载中遮罩层
        .loading-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 16rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 3;
        }
      }

      .add-material {
        width: 160rpx;
        height: 160rpx;
        background-color: #efefef;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .add-content {
          display: flex;
          flex-direction: column;
          align-items: center;

          .add-icon {
            width: 48rpx;
            height: 48rpx;
            margin-bottom: 8rpx;
          }

          .add-text {
            color: #999999;
            font-size: 24rpx;
            font-weight: bold;
            line-height: 32rpx;
          }
        }
      }
    }
  }

  // 底部操作栏
  .bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    // height: 128rpx;
    background-color: #ffffff;
    border-radius: 16rpx 16rpx 0 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 48rpx;
    // box-sizing: border-box;

    .selected-count {
      background-color: #efefef;
      border-radius: 12rpx;
      height: 64rpx;
      padding: 0 32rpx;
      display: flex;
      align-items: center;

      .count-text {
        color: #666666;
        font-size: 24rpx;
      }
    }

    .bottom-bar-btn {
      display: flex;

      .prev-btn,
      .next-btn {
        height: 80rpx;
        width: 180rpx;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 24rpx;

        .btn-text {
          font-size: 28rpx;
          font-weight: bold;
        }
      }

      .prev-btn {
        background-color: #efefef;

        .btn-text {
          color: #666666;
        }
      }

      .next-btn {
        background: linear-gradient(135deg, #22232c 0%, #0f0f0f 100%);

        .btn-text {
          color: #ffffff;
        }
      }
    }
  }

  // 添加途径弹窗
  .add-source-popup {
    width: 750rpx;
    padding: 40rpx 0 0;

    .popup-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 40rpx;
      margin-bottom: 40rpx;

      .popup-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
      }

      .close-icon {
        width: 48rpx;
        height: 48rpx;
      }
    }

    .source-option {
      width: 670rpx;
      height: 104rpx;
      background-color: #f3f5f8;
      border-radius: 8rpx;
      margin: 0 40rpx 16rpx;
      display: flex;
      align-items: center;

      &:active {
        background-color: #e9e9e9;
      }

      .option-text {
        font-size: 28rpx;
        font-weight: bold;
        color: #333333;
        margin-left: 48rpx;
      }
    }

    .safe-area-bottom {
      height: 68rpx;
      width: 100%;
    }
  }

  // 素材展示位置说明弹窗
  .position-info-popup {
    width: 670rpx;
    padding: 40rpx 32rpx 60rpx;

    .popup-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
      text-align: center;
      margin-bottom: 80rpx;
    }

    .position-options {
      display: flex;
      justify-content: space-between;
      margin-bottom: 40rpx;

      .position-option {
        width: 272rpx;
        text-align: center;
        position: relative;

        &.selected {
          .option-preview {
            border: 3rpx solid #21BD74;
          }
        }

        .option-preview {
          width: 272rpx;
          height: 434rpx;
          background-color: #f5f7fa;
          border-radius: 12rpx;
          overflow: hidden;
          position: relative;
          margin-bottom: 16rpx;

          &.half-screen {
            background-image: url('/static/半屏.webp');
            background-size: cover;
            background-position: center;

            .preview-content {
              position: absolute;
              bottom: 0;
              left: 0;
              width: 100%;
              height: 136rpx;
              background-color: rgba(255, 255, 255, 0.5);
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }

          &.full-screen {
            background-image: url('/static/全屏.webp');
            background-size: cover;
            background-position: center;

            .preview-content {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }

          .material-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;

            .material-icon {
              width: 40rpx;
              height: 40rpx;
              margin-bottom: 8rpx;
            }

            .material-text {
              font-size: 24rpx;
              color: #333333;
              font-weight: bold;
            }
          }
        }

        .option-text {
          font-size: 28rpx;
          color: #666666;
          font-weight: bold;
        }

        .select-indicator {
          position: absolute;
          top: 0;
          right: 0;
          width: 40rpx;
          height: 40rpx;
          background-image: url('/static/goxuanzhong.png');
          background-size: cover;
        }
      }
    }

    .confirm-button {
      height: 96rpx;
      background: linear-gradient(135deg, #22232c 0%, #0f0f0f 100%);
      border-radius: 16rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      .confirm-text {
        font-size: 36rpx;
        color: #ffffff;
        font-weight: bold;
      }
    }
  }

  // 素材为空确认弹窗
  .power-estimate-popup {
    width: 600rpx;
    padding: 60rpx 48rpx 48rpx;
    background-color: #ffffff;
    border-radius: 20rpx;

    .popup-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333333;
      text-align: center;
      margin-bottom: 48rpx;
    }

    .popup-content {
      font-size: 28rpx;
      color: #333333;
      line-height: 44rpx;
      text-align: center;
      margin-bottom: 64rpx;
    }

    .popup-buttons {
      display: flex;
      justify-content: space-between;
      gap: 32rpx;

      .cancel-btn,
      .confirm-btn {
        flex: 1;
        height: 88rpx;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .btn-text {
          font-size: 32rpx;
          font-weight: 600;
        }
      }

      .cancel-btn {
        background-color: #f5f5f5;
        border: 1rpx solid #e6e6e6;

        .btn-text {
          color: #666666;
        }
      }

      .confirm-btn {
        background-color: #333333;

        .btn-text {
          color: #ffffff;
        }
      }
    }
  }
}
</style>
