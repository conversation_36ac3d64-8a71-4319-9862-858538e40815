<template>
  <view>
    <!-- 下载进度弹窗 -->
    <u-popup :show="visible" mode="center" :round="32">
      <view class="download-popup" :class="{ 'success-popup': isCompleted }">
        <!-- 下载中弹窗 -->
        <view v-if="!isCompleted" class="downloading-container">
          <text class="popup-title">批量下载中</text>
          <text class="file-name">当前下载：「{{ currentFileName || '文件' }}」</text>

          <!-- 进度条 -->
          <view class="progress-bar">
            <u-line-progress :percentage="(count / totalCount) * 100" height="12" :active="true" activeColor="#40A9FF">
            </u-line-progress>
          </view>

          <view class="progress-info">
            <view class="progress-count">
              <text class="count-text">总计：{{ totalCount }}</text>
              <text class="count-separator">/ {{ successCount }}</text>
            </view>
            <text class="progress-percent">{{ Math.floor((count / totalCount) * 100) }}%</text>
          </view>

          <view class="cancel-btn" @click="closePopup">
            <text>取消</text>
          </view>
        </view>

        <!-- 下载成功弹窗 -->
        <view v-else class="success-container" style="background-image: url('/static/mask.png');">
          <view class="image-text-container">
            <image class="success-image" src="/static/bq.png"></image>
            <text class="success-text">下载成功</text>
          </view>

          <view class="confirm-btn" @click="closePopup">
            <text>确定</text>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      downLoadUrl: [], // 下载的url
      count: 0, // 当前第几个
      successCount: 0, // 成功下载的数量
      totalCount: 0, // 总共要下载的数量
      visible: false, // 弹窗是否显示
      isCompleted: false, // 是否下载完成
      isDownloading: false, // 是否正在下载
      fileTypes: [], // 文件类型（video或audio）
      currentFileName: '' // 当前下载的文件名
    }
  },
  methods: {
    /**
     * 添加下载链接
     * @param {Array} urls 链接数组
     * @param {Array} types 类型数组 (可选，'video'或'audio')
     * @param {Array} names 文件名数组 (可选)
     */
    addDownLoadUrl(urls, types = [], names = []) {
      this.downLoadUrl = urls
      this.totalCount = urls.length
      this.fileTypes = Array.isArray(types) && types.length === urls.length ?
        types : urls.map(() => 'video') // 默认视频类型
      this.fileNames = Array.isArray(names) && names.length === urls.length ?
        names : urls.map((_, index) => `文件${index + 1}`)
      return this // 支持链式调用
    },

    // 开始下载
    startDownload() {
      if (this.isDownloading) return uni.showToast({ title: '正在下载中，请稍候', icon: 'none' })
      if (this.downLoadUrl.length <= 0) return uni.showToast({ title: '请传入正确的URL', icon: 'none' })

      // 检查权限
      this.checkPermission()
    },

    // 检查权限
    checkPermission() {
      uni.getSetting({
        success: (res) => {
          if (!res.authSetting['scope.writePhotosAlbum']) {
            // 请求权限
            uni.authorize({
              scope: 'scope.writePhotosAlbum',
              success: () => {
                // 授权成功，开始下载
                this.beginDownLoad()
              },
              fail: (authErr) => {
                // 用户拒绝了授权
                if (authErr.errMsg.includes('auth deny')) {
                  uni.showModal({
                    title: '提示',
                    content: '需要相册权限才能保存媒体文件',
                    confirmText: '去设置',
                    success: (modalRes) => {
                      if (modalRes.confirm) {
                        uni.openSetting()
                      }
                    }
                  })
                }
              }
            })
          } else {
            // 已有权限，直接下载
            this.beginDownLoad()
          }
        }
      })
    },

    // 开始下载
    beginDownLoad() {
      this.count = 0
      this.successCount = 0
      this.isCompleted = false
      this.isDownloading = true
      this.visible = true
      console.log(this.visible, 'this.visible');

      this.downloadNext(0)
    },

    // 下载下一个文件
    downloadNext(index) {
      if (index >= this.downLoadUrl.length) {
        this.handleCompleted()
        return
      }

      const url = this.downLoadUrl[index]
      const fileType = this.fileTypes[index] || 'video'
      this.currentFileName = this.fileNames[index] || `文件${index + 1}`

      uni.downloadFile({
        url,
        success: (res) => {
          if (res.statusCode === 200) {
            // 根据文件类型决定保存方式
            if (fileType === 'audio') {
              this.saveFile(res.tempFilePath, index)
            } else {
              this.saveToAlbum(res.tempFilePath, index)
            }
          } else {
            this.handleItemFailed()
            this.downloadNext(index + 1)
          }
        },
        fail: () => {
          this.handleItemFailed()
          this.downloadNext(index + 1)
        }
      })
    },

    // 保存到相册
    saveToAlbum(filePath, index) {
      const fileType = this.fileTypes[index] || 'video'

      if (fileType === 'audio') {
        // 音频保存
        this.saveFile(filePath, index)
        return
      }

      // 视频保存到相册
      uni.saveVideoToPhotosAlbum({
        filePath,
        success: () => {
          this.handleItemSuccess()
          this.downloadNext(index + 1)
        },
        fail: () => {
          this.handleItemFailed()
          this.downloadNext(index + 1)
        }
      })
    },

    // 保存音频文件
    saveFile(filePath, index) {
      uni.saveFile({
        tempFilePath: filePath,
        success: () => {
          this.handleItemSuccess()
          this.downloadNext(index + 1)
        },
        fail: () => {
          this.handleItemFailed()
          this.downloadNext(index + 1)
        }
      })
    },

    // 处理单个文件下载成功
    handleItemSuccess() {
      this.count++
      this.successCount++
    },

    // 处理单个文件下载失败
    handleItemFailed() {
      this.count++
      uni.showToast({
        title: `第${this.count}个文件下载失败`,
        icon: 'none',
        duration: 1000
      })
    },

    // 处理全部下载完成
    handleCompleted() {
      this.isDownloading = false
      this.isCompleted = true
      if (this.successCount > 0) {
        // uni.showToast({
        //   title: `下载完成，共${this.successCount}/${this.totalCount}个成功`,
        //   icon: 'success',
        //   duration: 2000
        // })
      } else {
        uni.showToast({
          title: '下载失败，请检查网络后重试',
          icon: 'none',
          duration: 2000
        })
      }
    },

    // 关闭弹窗
    closePopup() {
      this.visible = false
      // 重置状态
      this.downLoadUrl = []
      this.count = 0
      this.successCount = 0
      this.totalCount = 0
      this.isCompleted = false
      this.currentFileName = ''
    }
  }
}
</script>

<style scoped lang="scss">
.download-popup {
  background-color: #fff;
  border-radius: 32rpx;
  width: 654rpx;
  height: 524rpx;

  // 下载中弹窗
  .downloading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 48rpx;

    .popup-title {
      width: 200rpx;
      height: 64rpx;
      color: #333333;
      font-size: 40rpx;
      font-weight: 700;
      text-align: center;
      line-height: 64rpx;
      margin-bottom: 48rpx;
      font-family: ChillRoundGothic_Bold;
    }

    .file-name {
      width: 100%;
      height: 48rpx;
      color: #333;
      font-size: 32rpx;
      font-weight: bold;
      line-height: 48rpx;
      margin-bottom: 32rpx;
      text-align: left;
    }

    .progress-bar {
      width: 558rpx;
      height: 12rpx;
      margin-bottom: 16rpx;
    }

    .progress-info {
      display: flex;
      justify-content: space-between;
      width: 558rpx;
      height: 40rpx;
      margin-bottom: 80rpx;

      .progress-count {
        display: flex;
        align-items: center;

        .count-text {
          color: #333;
          font-size: 28rpx;
          font-weight: bold;
          line-height: 40rpx;
        }

        .count-separator {
          color: #999;
          font-size: 24rpx;
          font-weight: bold;
          line-height: 32rpx;
          margin-left: 4rpx;
        }
      }

      .progress-percent {
        color: #333;
        font-size: 28rpx;
        font-weight: bold;
        line-height: 40rpx;
      }
    }

    .cancel-btn {
      background-color: #EFEFEF;
      border-radius: 16rpx;
      width: 558rpx;
      height: 88rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      text {
        color: #666;
        font-size: 32rpx;
        font-weight: bold;
        line-height: 48rpx;
      }
    }
  }

  // 下载成功弹窗
  .success-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 48rpx;
    background-size: cover;
    background-repeat: no-repeat;

    .image-text-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 40rpx;

      .success-image {
        width: 216rpx;
        height: 228rpx;
        margin-bottom: 10rpx;
      }

      .success-text {
        background-image: linear-gradient(135deg, rgba(255, 123, 30, 1) 37.39%, rgba(250, 101, 123, 1) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 48rpx;
        font-weight: 700;
        line-height: 72rpx;
      }
    }

    .confirm-btn {
      background-image: linear-gradient(135deg, rgba(34, 35, 44, 1) 19.83%, rgba(15, 15, 15, 1) 100%);
      border-radius: 16rpx;
      width: 558rpx;
      height: 88rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      text {
        color: #fff;
        font-size: 32rpx;
        font-weight: bold;
        line-height: 48rpx;
      }
    }
  }
}
</style>