<template>
  <view class="message-page">
    <view class="message-header">
      <view class="back-button" @click="go_back"
        :style="{ top: menuButtonTop + 'rpx', height: menuButtonHeight + 'rpx', lineHeight: menuButtonHeight + 'rpx' }">
        <image src="/static/返回.png" class="back-icon"></image>
      </view>
    </view>

    <view class="banner-section">
      <view class="banner-content">
        <image :src="list.icon" class="banner-logo"></image>
        <view class="banner-text">
          <view class="banner-title">{{ list.title }}</view>
          <view class="banner-desc">{{ list.description }}</view>
        </view>
      </view>
    </view>

    <view class="content-container">
      <view class="tab-section" v-if="!selectMode">
        <view class="tab-wrapper">
          <view class="tab-item" :class="{ 'tab-item-active': current === 0 }" @click="change(0)">
            <view class="tab-text">文案库</view>
          </view>
          <view class="tab-item" :class="{ 'tab-item-active': current === 1 }" @click="change(1)">
            <view class="tab-text">素材库</view>
          </view>
        </view>
      </view>

      <view class="category-tabs">
        <u-tabs lineColor="black" :list="list1" @click="change_list"></u-tabs>
      </view>

      <scroll-view scroll-y="true" class="content-scroll" @scrolltolower="get_again">
        <view v-if="current == 0 && !selectMode">
          <view v-if="pageContent && !pageContent.length" class="empty-container">
            <u-empty mode="data"></u-empty>
          </view>

          <view v-for="item in pageContent" class="content-card">
            <view class="card-header">
              <image :src="list.icon" class="author-avatar"></image>
              <view class="author-info">
                <view class="author-name">运营小助手</view>
                <view class="publish-time">{{ item.createTime }}</view>
              </view>
            </view>

            <view class="card-title">{{ item.title }}</view>

            <view class="card-content">
              <view class="content-text"
                :class="{ 'content-text-collapsed': item.content.length > 60 && !item.expanded }">
                {{ item.content }}
              </view>
              <view class="expand-btn" v-if="item.content.length > 60" @click="toggleExpand(item)">
                {{ item.expanded ? '收起' : '展开全文' }}
                <image src="/static/下拉.png" style="width: 32rpx;height: 32rpx;" class="content-text-icon"
                  :class="{ 'content-text-icon-rotate': item.expanded }">
                </image>
              </view>
            </view>
            <view class="card-divider"></view>
            <view class="card-actions">
              <view class="action-item" @click="copy_message(item.content)">
                <image src="/static/home_three/message/fuzhi.png" class="action-icon"></image>
                <view class="action-name">复制文案</view>
              </view>
              <view class="action-item" @click="toGenerateVideo(item)">
                <image src="/static/home_three/message/video.png" class="action-icon"></image>
                <view class="action-name">生成视频</view>
              </view>
              <view class="action-item" @click="rewirte(item)">
                <image src="/static/home/<USER>/sijiaoxing.png" class="action-icon"></image>
                <view class="action-name">AI改写</view>
              </view>
            </view>


          </view>
        </view>

        <view v-else class="material-grid">
          <!-- 选择模式提示 -->
          <view class="select-mode-tip" v-if="selectMode && pageContent && pageContent.length > 0">
            <view class="selected-count">
              已选择 {{ selectedMaterials.length }} 项
            </view>
          </view>

          <!-- 空状态提示 -->
          <view v-if="pageContent && !pageContent.length" class="empty-container">
            <u-empty mode="data"></u-empty>
          </view>

          <!-- 素材列表 -->
          <view class="material-item" v-for="item in pageContent" @click="selectMaterial(item)" :key="item.id">
            <image :src="item.content"></image>
            <view class="select-overlay" :class="{ 'selected': selectedMaterials.includes(item.id) }">
              <image src="/static/home_three/message/selected.png" class="selected-icon"
                v-if="selectedMaterials.includes(item.id)"></image>
              <view class="select-mask" v-if="selectedMaterials.includes(item.id)"></view>
            </view>
          </view>

          <!-- 普通模式的操作按钮 -->
          <view class="material-actions-wrapper" v-if="!selectMode && pageContent && pageContent.length > 0">
            <view class="material-actions">
              <view class="action-download" @click="downloadMaterials"
                :class="{ 'action-disabled': selectedMaterials.length === 0 }">
                <view class="action-text">下载</view>
              </view>
              <view class="action-generate" @click="generateVideo"
                :class="{ 'action-disabled': selectedMaterials.length === 0 }">
                <view class="action-text">选择素材生成视频</view>
              </view>
            </view>
          </view>

          <!-- 选择模式的确定按钮 -->
          <view class="material-actions-wrapper" v-if="selectMode && pageContent && pageContent.length > 0">
            <view class="material-actions confirm-actions">
              <view class="action-confirm" @click="confirmSelection"
                :class="{ 'action-disabled': selectedMaterials.length === 0 }">
                <view class="action-text">确定</view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import {
  getGroupList,
  getGroupContent
} from '../../api/numberUser/copy.js'

import {
  formatRelativeTime
} from '../../utils/file.js'
export default {
  data() {
    return {
      current: 0,
      statusBarHeight: 0,
      navBarHeight: 0,
      customBarHeight: 0,
      menuButtonTop: 0, // 胶囊按钮距离顶部的距离
      menuButtonHeight: 0, // 胶囊按钮高度
      list: '',
      list1: [],
      title_list: ['文案库', '素材库'],
      listId: -1,
      pageContent: '',
      page: 1,
      data_length: 0,
      selectedMaterials: [], // 选中素材的ID数组
      selectedMaterialsData: [], // 选中素材的完整数据数组
      isAllSelected: false, // 是否全选
      contentHeight: 0, // 内容区域高度
      bottomHight: 0, // 底部高度
      selectMode: false // 是否为素材选择模式
    }
  },
  onLoad() {
    this.calculateHeight()
    this.getMenuButtonInfo();

    const eventChannel = this.getOpenerEventChannel();
    if (eventChannel) {
      eventChannel.on('get_message', (list) => {
        console.log('接收到频道数据:', list);
        this.list = list;

        // 判断是否为素材选择模式
        if (list.selectMode) {
          this.selectMode = true;
          this.current = 1; // 默认显示素材库
        }

        this.get_message();
      });
    }
  },
  methods: {
    // 确认素材选择
    confirmSelection() {
      if (this.selectedMaterials.length === 0) {
        uni.showToast({
          title: '请至少选择一个素材',
          icon: 'none'
        });
        return;
      }

      // 通过事件通道将选中的素材传回上一页
      const eventChannel = this.getOpenerEventChannel();
      eventChannel.emit('selected_materials', this.selectedMaterialsData);

      // 返回上一页
      uni.navigateBack();
    },

    async rewirte(item) {
      let templateInfo = `{"toolTitle": "文案改写","fields": [{"label": "改写内容","value": "${item.content}"}]}`
      uni.navigateTo({
        url: '/subpkg/index/results?source=group',
        success(res) {
          res.eventChannel.emit('get_message', JSON.parse(templateInfo))
        }
      })
    },
    toGenerateVideo(item) {
      this.$store.commit('setMessage_save', true)
      this.$store.commit('setAiMessage', {
        content: [item.content],
        title: item.title
      })
      uni.navigateTo({
        url: '/subpkg/index/get_radio/index'
      })
    },
    //复制
    async copy_message(message) {
      uni.setClipboardData({
        data: message,
        success: () => {
          uni.showToast({
            title: '复制成功',
            icon: 'none'
          });
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      });
    },
    change(e) {
      this.page = 1
      this.current = e
      this.get_message()
      this.pageContent = null
      this.listId = null

      // 切换时清空选择
      this.selectedMaterials = []
      this.selectedMaterialsData = []
      this.isAllSelected = false
    },
    //获取分类
    get_message() {
      getGroupList({
        groupId: this.list.id,
        type: this.current
      }).then(res => {
        this.list1 = [{
          name: '全部',
          id: null
        }, ...res.data]
        this.get_content()
      })
    },
    //获取分类内容
    get_content() {
      getGroupContent({
        type: this.current,
        groupId: this.list.id,
        categoryId: this.listId ?? -1,
        pageNum: this.page,
        pageSize: 10
      }).then(res => {
        if (this.current === 0) { // 文案库
          res.rows.map(item => {
            item.createTime = formatRelativeTime(item.createTime)
            item.expanded = false // 添加展开状态属性
          })
        }

        if (this.page == 1) {
          this.pageContent = res.rows
          // 重置选择状态
          this.selectedMaterials = []
          this.selectedMaterialsData = []
          this.isAllSelected = false
          this.$forceUpdate()
        } else {
          this.pageContent = [...this.pageContent, ...res.rows]
        }
        this.data_length = res.total
      })
    },
    //触发上拉刷新
    get_again() {
      this.page++
      if (this.pageContent.length < this.data_length) this.get_content()
      else {
        console.log('失败')
      }
    },
    //选择分类
    change_list(e) {
      this.page = 1
      this.listId = e.id
      this.get_content()
    },
    // 获取胶囊按钮信息
    getMenuButtonInfo() {
      // #ifdef MP-WEIXIN
      const systemInfo = uni.getSystemInfoSync();
      const menuButtonInfo = uni.getMenuButtonBoundingClientRect();

      // 状态栏高度
      this.statusBarHeight = systemInfo.statusBarHeight;

      // 转换单位 px -> rpx (750rpx = windowWidth)
      const pxToRpxScale = 750 / systemInfo.windowWidth;

      // 胶囊按钮到顶部的距离（rpx）
      this.menuButtonTop = menuButtonInfo.top * pxToRpxScale;

      // 胶囊按钮高度（rpx）
      this.menuButtonHeight = menuButtonInfo.height * pxToRpxScale;

      // 胶囊按钮距离右侧的距离（rpx）
      const menuButtonRight = (systemInfo.windowWidth - menuButtonInfo.right) * pxToRpxScale;

      // 胶囊按钮宽度（rpx）
      const menuButtonWidth = menuButtonInfo.width * pxToRpxScale;

      // 计算导航栏高度 = 胶囊距顶部 + 胶囊高度 + 胶囊距底部(取胶囊距顶部)
      this.navBarHeight = (menuButtonInfo.height + (menuButtonInfo.top - systemInfo.statusBarHeight) * 2) * pxToRpxScale;

      // 自定义导航栏高度 = 状态栏 + 导航栏
      this.customBarHeight = this.statusBarHeight * pxToRpxScale + this.navBarHeight;

      console.log('胶囊数据(rpx)', {
        menuButtonTop: this.menuButtonTop,
        menuButtonHeight: this.menuButtonHeight,
        menuButtonRight: menuButtonRight,
        menuButtonWidth: menuButtonWidth,
        navBarHeight: this.navBarHeight,
        customBarHeight: this.customBarHeight
      });

      // #endif

      // #ifndef MP-WEIXIN
      const systemInfo = uni.getSystemInfoSync();
      const pxToRpxScale = 750 / systemInfo.windowWidth;

      this.statusBarHeight = systemInfo.statusBarHeight * pxToRpxScale;
      this.navBarHeight = 90; // 非微信小程序环境给个固定高度
      this.customBarHeight = this.statusBarHeight + this.navBarHeight;
      this.menuButtonTop = this.statusBarHeight + 8; // 非微信小程序环境给个合适的位置
      this.menuButtonHeight = 72; // 非微信小程序环境给个合适的高度

      console.log('非微信平台胶囊模拟数据(rpx)', {
        menuButtonTop: this.menuButtonTop,
        menuButtonHeight: this.menuButtonHeight,
        navBarHeight: this.navBarHeight,
        customBarHeight: this.customBarHeight
      });
      // #endif
    },
    //切换展开/收起状态
    toggleExpand(item) {
      this.$set(item, 'expanded', !item.expanded)
    },
    //查看全文
    go_look_message(item) {
      let that = this
      uni.navigateTo({
        url: './messageContent',
        success(res) {
          res.eventChannel.emit('get_messageContent', {
            item,
            url: that.list.icon
          })
        }
      })
    },
    go_back() {
      uni.navigateBack()
    },
    // 下载素材
    downloadMaterials() {
      if (this.selectedMaterials.length === 0) {
        uni.showToast({
          title: '请先选择素材',
          icon: 'none'
        });
        return;
      }

      uni.showToast({
        title: `开始下载${this.selectedMaterials.length}个素材`,
        icon: 'none'
      });

      // 获取选中素材的URL列表
      const selectedUrls = this.selectedMaterialsData.map(item => item.url);
      console.log('要下载的素材URL:', selectedUrls);

      // 这里可以实现批量下载逻辑
    },

    // 生成视频
    generateVideo() {
      if (this.selectedMaterials.length === 0) {
        uni.showToast({
          title: '请先选择素材',
          icon: 'none'
        });
        return;
      }

      // 准备选中的素材数据
      const materialData = {
        ids: this.selectedMaterials,
        materials: this.selectedMaterialsData
      };

      // 存储到全局状态
      this.$store.commit('setSelectedMaterials', materialData);

      uni.navigateTo({
        url: '/subpkg/index/get_radio/index',
        success: (res) => {
          // 通过事件通道传递数据
          res.eventChannel.emit('selectedMaterials', materialData);
        }
      });
    },

    // 选择素材
    selectMaterial(item) {
      const index = this.selectedMaterials.indexOf(item.id);
      if (index > -1) {
        // 取消选择
        this.selectedMaterials.splice(index, 1);
        this.selectedMaterialsData = this.selectedMaterialsData.filter(material => material.id !== item.id);
      } else {
        // 选中
        this.selectedMaterials.push(item.id);
        this.selectedMaterialsData.push({
          id: item.id,
          url: item.content,
          content: item.content,
          outId: item.outId
        });
      }

      // 更新全选状态
      this.isAllSelected = this.pageContent.length > 0 && this.selectedMaterials.length === this.pageContent.length;
    },

    calculateHeight() {
      console.log('计算高度');

      // 计算内容区域高度
      const query = uni.createSelectorQuery()

      // 等待DOM挂载完成
      setTimeout(() => {
        console.log('计算高度');

        query.select('.message-page').boundingClientRect()
        query.select('.banner-section').boundingClientRect()
        let app = uni.getSystemInfoSync();
        this.bottomHight = app.safeAreaInsets.bottom
        query.exec((res) => {
          console.log(res);

          if (res[0] && res[1]) {
            const messageHeight = res[0].height
            const bannerHeight = res[1].height
            // 计算内容区域高度 = 容器高度 - 搜索栏高度 - 分类列表高度 - tabbar高度
            this.contentHeight = messageHeight - bannerHeight + uni.upx2px(350) - this.bottomHight

            console.log({
              messageHeight,
              bannerHeight,
              contentHeight: this.contentHeight,
              bottomHight: this.bottomHight
            });

          }
        })
      }, 100)
    }
  }
}
</script>

<style scoped lang="scss">
.message-page {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-color: #F3F5F8;

  .message-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;

    .back-button {
      position: absolute;
      left: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 70rpx;
      height: 70rpx;
      z-index: 999;

      .back-icon {
        width: 48rpx;
        height: 48rpx;
      }
    }
  }

  .banner-section {
    width: 100%;
    height: 760rpx;
    position: relative;
    background: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/app_tab/group.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    // padding: 40rpx;
    box-sizing: border-box;


    .banner-content {
      display: flex;
      padding: 200rpx 48rpx 0;
      align-items: center;

      .banner-logo {
        width: 160rpx;
        height: 160rpx;
        border-radius: 16rpx;
      }

      .banner-text {
        margin-left: 40rpx;
        flex: 1;

        .banner-title {
          font-size: 36rpx;
          font-weight: 600;
          color: #ffffff;
          margin-bottom: 20rpx;
          line-height: 56rpx;
        }

        .banner-desc {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.7);
          line-height: 32rpx;
        }
      }
    }
  }

  .content-container {
    background-color: #F3F5F8;
    border-radius: 40rpx 40rpx 0 0;
    margin-top: -350rpx;
    position: relative;
    z-index: 10;
    padding: 40rpx 0;

    .tab-section {
      display: flex;
      justify-content: center;
      margin-bottom: 10rpx;

      .tab-wrapper {
        display: flex;
        background-color: #ffffff;
        border-radius: 12rpx;
        width: 288rpx;
        height: 72rpx;
        padding: 8rpx;

        .tab-item {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 56rpx;
          border-radius: 12rpx;

          .tab-text {
            font-size: 24rpx;
            color: #999999;
          }

          &.tab-item-active {
            background-image: linear-gradient(135deg, #22232C 0%, #0F0F0F 100%);

            .tab-text {
              color: #ffffff;
              font-weight: 600;
            }
          }
        }
      }
    }

    .category-tabs {
      margin: 0 32rpx 10rpx;
      font-size: 28rpx;

      :deep(.u-tabs__wrapper__nav__item) {
        &.u-tabs__wrapper__nav__item--active {
          font-weight: bold;
          font-size: 32rpx;
          color: #333333;
        }

        &:not(.u-tabs__wrapper__nav__item--active) {
          font-size: 28rpx;
          color: #999999;
        }
      }
    }

    .content-scroll {
      height: 65vh;

      .empty-container {
        margin-top: 200rpx;
      }

      .content-card {
        margin: 20rpx 32rpx;
        background-color: #ffffff;
        border-radius: 24rpx;
        padding: 24rpx;

        .card-header {
          display: flex;
          align-items: center;

          .author-avatar {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
          }

          .author-info {
            margin-left: 20rpx;

            .author-name {
              font-size: 28rpx;
              font-weight: 600;
              color: #333333;
            }

            .publish-time {
              font-size: 24rpx;
              color: #999999;
              margin-top: 8rpx;
            }
          }
        }

        .card-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #333333;
          margin: 20rpx 0;
        }

        .card-content {
          position: relative;
          margin-bottom: 30rpx;

          .content-text {
            font-size: 28rpx;
            line-height: 40rpx;
            color: #666666;

            &.content-text-collapsed {
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 3;
              line-clamp: 3;
            }
          }

          .expand-btn {
            text-align: right;
            font-size: 28rpx;
            font-weight: 600;
            color: #666666;
            margin-top: 10rpx;
            padding: 5rpx 0;
            display: flex;
            align-items: center;
            justify-content: flex-end;

            .content-text-icon {
              margin-left: 8rpx;
              transition: transform 0.3s ease;

              &.content-text-icon-rotate {
                transform: rotate(180deg);
              }
            }
          }
        }

        .card-actions {
          display: flex;
          justify-content: space-around;
          margin: 20rpx 0;

          .action-item {
            display: flex;
            align-items: center;
            background-color: #F3F5F8;
            padding: 16rpx 30rpx;
            border-radius: 12rpx;

            .action-icon {
              width: 32rpx;
              height: 32rpx;
              margin-right: 10rpx;
            }

            .action-name {
              font-size: 26rpx;
              color: #333333;
            }
          }
        }

        .card-divider {
          height: 1rpx;
          background-color: #F3F5F8;
          margin-top: 10rpx;
        }
      }

      .material-grid {
        padding: 0 32rpx;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        grid-gap: 16rpx;
        position: relative;
        padding-bottom: 160rpx; // 为底部操作栏预留空间

        // 选择模式提示
        .select-mode-tip {
          width: 100%;
          padding: 10rpx 0;
          margin-bottom: 20rpx;
          text-align: right;
          grid-column: span 4;

          .selected-count {
            font-size: 28rpx;
            color: #333333;
          }
        }

        // 空状态容器占满整行
        .empty-container {
          grid-column: span 4;
        }

        // 素材项样式
        .material-item {
          width: 100%;
          aspect-ratio: 1/1;
          position: relative;
          border-radius: 8rpx;
          overflow: hidden;

          image {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .select-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;

            &.selected {
              border: 3rpx solid #21BD74;
            }

            .select-mask {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background-color: rgba(33, 189, 116, 0.1);
              border-radius: 8rpx;
            }

            .selected-icon {
              position: absolute;
              bottom: 10rpx;
              right: 10rpx;
              width: 40rpx;
              height: 40rpx;
              z-index: 2;
            }
          }
        }

        // 底部操作按钮容器
        .material-actions-wrapper {
          position: fixed;
          bottom: 0;
          left: 0;
          width: 100%;
          z-index: 999;
        }

        // 底部操作按钮
        .material-actions {
          background-color: #ffffff;
          border-radius: 32rpx 32rpx 0 0;
          padding: 40rpx 32rpx;
          display: flex;
          justify-content: space-between;
          width: 100%;
          box-sizing: border-box;

          .action-download {
            width: 218rpx;
            height: 96rpx;
            background-color: #EFEFEF;
            border-radius: 16rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            .action-text {
              font-size: 32rpx;
              font-weight: 600;
              color: #333333;
            }
          }

          .action-generate {
            width: 436rpx;
            height: 96rpx;
            background-image: linear-gradient(135deg, #22232C 19.84%, #0F0F0F 100%);
            border-radius: 16rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            .action-text {
              font-size: 32rpx;
              font-weight: 600;
              color: #ffffff;
            }
          }

          &.confirm-actions {
            justify-content: center;

            .action-confirm {
              width: 436rpx;
              height: 96rpx;
              background-image: linear-gradient(135deg, #22232C 19.84%, #0F0F0F 100%);
              border-radius: 16rpx;
              display: flex;
              align-items: center;
              justify-content: center;

              .action-text {
                font-size: 32rpx;
                font-weight: 600;
                color: #ffffff;
              }
            }
          }

          .action-disabled {
            opacity: 0.5;
            pointer-events: none;
          }
        }
      }
    }
  }
}
</style>