<template>
  <view class="page">
    <view style="font-size: 32rpx;font-weight: 600;">
      <text style="width:6rpx; height: 28rpx;background: #2fe62c;display: inline-block;margin-right: 18rpx;"></text>
      视频教程
    </view>

    <view v-if="loading" class="skeleton">
      <view class="skeleton-video"></view>
      <view class="skeleton-image"></view>
    </view>

    <view v-else>
      <view class="video-container">
        <video class="video" :src="page_data.demoVideoUrl" @error="videoErrorCallback" controls></video>
      </view>
      <image style="width: 100%;margin-top: 40rpx;" mode="widthFix" :src="page_data.demoImg">
      </image>
    </view>

    <view class="project_btn" @click="handleUpload">
      <view>
        <view>上传训练视频</view>
        <view style="font-size: 26rpx;color: aliceblue;">(时长 30-60s)</view>
      </view>
    </view>
    <projectModel v-if="showTips" title="温馨提示" :btn="false" :content="tipsContent">
      <view class="project_btn1" @click="showTips = false">
        知道了
      </view>
    </projectModel>
  </view>
</template>

<script>
import { getPageConfigInfo } from '../../../api/app/config.js'

export default {
  data() {
    return {
      type: '',
      page_data: '',
      loading: true,
      tipsContent: '',
      showTips: false
    }
  },
  onLoad(option) {
    this.type = option?.type
    let type = ''
    if (this.type == 'pro') type = 'tranDrPro'
    if (this.type == 'lite') type = 'tranDrLite'
    getPageConfigInfo({ type }).then(res => {
      this.page_data = JSON.parse(res.data)
      this.loading = false
    })
  },
  methods: {
    //选择视频
    chooseVideo() {
      return new Promise((resolve, reject) => {
        uni.chooseVideo({
          sourceType: ['album'],
          maxDuration: 60,
          compressed: false,
          success: resolve,
          fail: (error) => {
            console.error('选择视频失败:', error);
            reject(error);
          }
        });
      });
    },
    getCompressionConfig(videoInfo) {
      const { bitrate, fps, width, height, duration } = videoInfo;
      // 1. 动态场景检测优化（结合时长、分辨率、帧率）
      const isDynamicScene = duration < 31 || fps > 25 || width >= 1080;
      // 2. 码率分级策略（单位：kbps）
      const TARGET_BITRATE = {
        MEDIUM: 3200, // 中等画质（默认）
        HIGH: 5000    // 高画质（仅限动态场景且分辨率≥1080p）
      };
      // 3. 分辨率缩放策略（优先保持宽高比）
      const getResolutionScale = () => {
        if (width > 1920 || height > 1920) return 0.7;  // 4K+ 视频大幅压缩
        if (width > 1080 || height > 1080) return 0.8; // 1080p 适度压缩
        return 1.0;
      };
      return {
        bitrate: isDynamicScene
          ? Math.min(bitrate, TARGET_BITRATE.HIGH)
          : Math.min(bitrate, TARGET_BITRATE.MEDIUM),
        fps: Math.min(fps, 30), // 限制最高帧率
        resolution: getResolutionScale()
      };
    },
    // 视频上传
    async compressVideo(filePath) {
      try {
        // 判断文件大小
        const fileInfo = await uni.getFileInfo({ filePath });
        const fileSizeMB = fileInfo.size / (1024 * 1024);
        if (fileSizeMB <= 35) {
          console.log('文件较小，无需压缩');
          return { tempFilePath: filePath };
        }
        // 1. 获取视频原始信息
        const videoInfo = await this.getVideoInfo(filePath);
        const config = this.getCompressionConfig(videoInfo);
        // 4. 执行压缩
        return await new Promise((resolve, reject) => {
          uni.compressVideo({
            src: filePath,
            ...config,
            success: resolve,
            fail: (err) => {
              console.warn('压缩失败，降级为 quality 模式:', err);
              uni.compressVideo({
                src: filePath,
                quality: 'high',
                success: resolve,
                fail: reject
              });
            }
          });
        });
      } catch (err) {
        console.error('压缩视频失败:', err);
        throw err;
      }
    },
    // 获取视频信息
    getVideoInfo(filePath) {
      return new Promise((resolve, reject) => {
        uni.getVideoInfo({
          src: filePath,
          success: resolve,
          fail: reject
        });
      });
    },
    /**
     * 视频上传
     *  pro-专业版需要进行裁剪，传递裁剪参数给后端用于训练数字人
     *  lite -极速版，不做处理
     * */
    async handleUpload() {
      let tempFilePath = '';
      let compressedPath = '';
      try {
        const res = await this.chooseVideo();
        console.log("选择的视频信息:", JSON.stringify(res));
        tempFilePath = res.tempFilePath;
        const { duration } = res;
        if (duration < 30 || duration > 60) {
          this.showTips = true;
          this.tipsContent = "请上传30-60秒之间的视频，以获得更好的视频效果";
          return;
        }

        uni.showLoading({ title: '压缩中...', mask: true });

        const compressedRes = await this.compressVideo(tempFilePath);
        console.log("压缩后的视频信息:", JSON.stringify(compressedRes));
        compressedPath = compressedRes.tempFilePath;

      } catch (err) {
        console.error("处理视频失败:", err);

        if (err && err.errMsg && err.errMsg.indexOf('cancel') !== -1) {
          console.log('用户取消了选择');
          return;
        }

        // 压缩失败兜底
        compressedPath = tempFilePath;
      } finally {
        uni.hideLoading();
        if(this.showTips) return;
        compressedPath = compressedPath || tempFilePath;
        if (!compressedPath) {
          uni.showToast({ title: '视频处理失败', icon: 'none' });
          return;
        }
        if (this.type === 'pro') {
          uni.navigateTo({ url: `/subpkg/tailor/index?videoSrc=${compressedPath}` });
        } else {
          this.$store.commit('SetNumberUserVideo', compressedPath);
          uni.navigateTo({ url: '/subpkg/index/save_video/index?type=lite' });
        }
      }
    }


  }
}

</script>

<style scoped lang="scss">
.page {
  padding: 40rpx;

  .project_btn {
    background: black;
    margin-top: 40rpx;
  }

  .video-container {
    margin-top: 40rpx;
    width: 672rpx;
    height: 378rpx;
    position: relative;
    overflow: hidden;
    background-color: #000;
    border-radius: 20rpx;

    .video {
      width: 100%;
      height: 100%;
      object-fit: fill;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1;
    }
  }
}

.skeleton {
  display: flex;
  flex-direction: column;
  align-items: center;

  .skeleton-video {
    width: 100%;
    height: 360rpx;
    background: #f0f0f0;
    border-radius: 20rpx;
    margin-bottom: 40rpx;
  }

  .skeleton-image {
    width: 100%;
    height: 200rpx;
    background: #f0f0f0;
    border-radius: 20rpx;
  }
}

/* 弹窗按钮 */
.project_btn1 {
  background: #000;
  color: #fff;
  border-radius: 12rpx;
  padding: 16rpx 0;
  text-align: center;
  margin: 35rpx auto 0;
  width: 320rpx;
}
</style>