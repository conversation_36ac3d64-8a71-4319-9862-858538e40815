<template>
	<!-- 模板编辑器主容器 -->
	<view class="template-editor">
		<view v-if="introduceCardData && introduceCardData.define" ref="introduceCardContainer"
			class="identity-card-container" :style="[calculateBackgroundDisplay(introduceCardData.define), {
				fontSize: introduceCardData.define.fontSize + 'px',
			}]">
			<text>
				{{ tempText1 }}
			</text>
			<view>
				<!-- 修改：使用格式化后的临时介绍文本，支持换行 -->
				<text v-for="(line, lineIndex) in formatText(tempText2)" :key="lineIndex" class="description-line">
					{{ line }}
				</text>
			</view>
		</view>

		<!-- 编辑区域容器 -->
		<view class="editor-container">
			<!-- 离屏Canvas (用于生成预览图) -->
			<canvas canvas-id="myCanvasBlack" id="myCanvasBlack"
				:style="{ width: 1080 * canvasScale + 'px', height: 1920 * canvasScale + 'px', position: 'fixed', left: '-999px' }"></canvas>

			<!-- 缩放控制器组件 -->
			<scale-slider
				v-if="selectedElementId && currentElementType !== 'mask' && currentElementType !== 'caption' && currentElementType !== 'introduceCard'"
				v-model="currentScaleValue" :throttle-time="200" @change="handleScaleChange"></scale-slider>

			<!-- 滑块控制器(目前未启用) -->
			<pp-slider v-if="currentElementType == 'title' && false" :min="5" :max="15" :vertical="true" :show-value="true"
				:value="10" :block-size="20" :lineSize="4" :disabled="false" @changing="handleSliderChange"
				style="width: 20vw;" />
			<view v-else style="width: 20vw;"></view>

			<!-- 可移动编辑区域 - 主画布 -->
			<movable-area class="editor-canvas" :scale-area="true" id="myCanvas"
				:style="{ width: 1080 * canvasScale + 'px', height: 1920 * canvasScale + 'px' }" style="color: white;">

				<!-- 遍历所有模板元素并渲染 -->
				<template v-for="(item, index) in canvasData" v-if="item.id">
					<!-- 身份栏元素 -->
					<movable-view v-if="item.type == 'introduceCard' && item.ready" style="z-index: 2;" :style="[{
						transform: `scale(1, 1)`,
						width: introduceCardSize.width,
						height: introduceCardSize.height
					}]" :x="item.define.transform.position[0] - item.define.transform.anchor[0]"
						:y="item.define.transform.position[1] - item.define.transform.anchor[1]" direction="all" :scale="false"
						:animation="false" :out-of-bounds="false" @click.stop="handleSelectElement(item)"
						@change="((e) => onIntroduceCardChange(e, item, index))">
						<view class="element-content">
							<!-- 渲染身份栏内容，包括背景和文字 -->
							<view v-if="item.type == 'introduceCard'" :style="[calculateBackgroundDisplay(item.define), {
								fontSize: item.define.fontSize + 'px',
								display: 'flex',
								flexDirection: 'column',
								color: item.define.description.color,
								position: 'absolute',
								left: '0px',
								top: '0px'
							}]">
								<view>
									{{ identityName }}
								</view>
								<view>
									<!-- 修改：使用格式化后的介绍文本，支持换行 -->
									<text v-for="(line, lineIndex) in formatText(identityDesc)" :key="lineIndex" class="description-line">
										{{ line }}
									</text>
								</view>
							</view>
						</view>
						<!-- 删除按钮，仅在选中状态显示 -->
						<image v-if="selectedElementId == item.id" @click="handleDeleteElement(item)" class="element-delete-btn"
							src="/static/home/<USER>/chacha.png"></image>
					</movable-view>

					<!-- 标题元素 -->
					<movable-view v-if="item.type == 'title'"
						:style="{ width: item.define.width + 'px', height: item.define.height + 'px' }" style="z-index: 2;"
						:x="item.define.transform.position[0] - item.define.transform.anchor[0]"
						:y="item.define.transform.position[1] - item.define.transform.anchor[1]"
						:direction="item.id == selectedElementId ? 'vertical' : 'vertical'" :out-of-bounds="false"
						:animation="false" @click.stop="handleSelectElement(item)" @change="((e) => onTitleChange(e, item, index))">
						<!-- 渲染标题图片 -->
						<view class="element-content">
							<view class="element-wrapper" :class="selectedElementId == item.id ? 'element-selected' : ''" :style="{
								width: Number(item.imageWidth) * canvasScale + 'px',
								height: Number(item.imageHeight) * canvasScale + 'px',
								transform: `scale(${item.define.transform.scalar ? item.define.transform.scalar[0] / 100 : 1}, ${item.define.transform.scalar ? item.define.transform.scalar[1] / 100 : 1})`,
								transformOrigin: 'center center',
								position: 'relative'
							}">
								<image v-if="item.url && item.type != 'mask'" style="width: 100%; height: 100%;" :src="item.url">
								</image>
								<!-- 删除按钮，仅在选中状态显示 -->
								<image v-if="selectedElementId == item.id" @click.stop="handleDeleteElement(item)"
									class="element-delete-btn" src="/static/home/<USER>/chacha.png"></image>
							</view>
						</view>
					</movable-view>

					<!-- 字幕元素 -->
					<movable-view v-if="item.type == 'caption'" style="z-index: 2;"
						:style="{ width: item.define.width + 'px', height: item.define.height + 'px' }"
						:x="item.define.transform.position[0] - item.define.transform.anchor[0]"
						:y="item.define.transform.position[1] - item.define.transform.anchor[1]" :animation="false"
						:direction="item.id == selectedElementId ? 'vertical' : 'vertical'" :scale="false" :out-of-bounds="false"
						@click.stop="handleSelectElement(item)" @change="((e) => onCaptionChange(e, item, index))">
						<!-- 渲染字幕图片 -->
						<view class="element-content">
							<view class="element-wrapper" :class="selectedElementId == item.id ? 'element-selected' : ''" :style="{
								width: Number(item.imageWidth) * canvasScale + 'px',
								height: Number(item.imageHeight) * canvasScale + 'px',
								transform: `scale(1, 1)`,
								transformOrigin: 'center center',
								position: 'relative'
							}">
								<image v-if="item.url && item.type != 'mask'" style="width: 100%; height: 100%;" :src="item.url">
								</image>
								<!-- 删除按钮，仅在选中状态显示 -->
								<image v-if="selectedElementId == item.id" @click.stop="handleDeleteElement(item)"
									class="element-delete-btn" src="/static/home/<USER>/chacha.png"></image>
							</view>
						</view>
					</movable-view>

					<!-- 背景元素 -->
					<movable-view v-if="item.type == 'mask'" :animation="false"
						:style="{ zIndex: 0, width: 1080 * canvasScale + 'px', height: 1920 * canvasScale + 'px' }"
						@change="((e) => onMaskChange(e, item, index))">
						<!-- 渲染背景图片 -->
						<image v-if="getBackgroundUrl(item.id)" style="position: absolute;top: 0;left: 0;width: 100%;height: 100%;"
							:src="getBackgroundUrl(item.id)"></image>
					</movable-view>

					<!-- 数字人元素 -->
					<movable-view v-if="item.type == 'virtualman' && digitalHumanVisible" style="z-index: 1;" :animation="false"
						:style="{
							width: item.extra.width * item.scale * canvasScale * (item.define.transform.scalar ? item.define.transform.scalar[0] / 100 : 1) + 'px',
							height: item.extra.height * item.scale * canvasScale * (item.define.transform.scalar ? item.define.transform.scalar[1] / 100 : 1) + 'px'
						}" :x="item.define.transform.position[0] - item.define.transform.anchor[0] * (item.define.transform.scalar ? item.define.transform.scalar[0] / 100 : 1)"
						:y="item.define.transform.position[1] - item.define.transform.anchor[1] * (item.define.transform.scalar ? item.define.transform.scalar[1] / 100 : 1)"
						direction="all" :scale="false" :out-of-bounds="false" @click.stop="handleSelectElement(item)"
						@change="((e) => onVirtualmanChange(e, item, index))">
						<!-- 渲染数字人图片 -->
						<image v-if="item.cover" style="width: 100%; height: 100%; position: relative;" :src="item.cover"></image>

						<!-- 删除按钮，仅在选中状态显示 -->
						<!-- <image v-if="selectedElementId == item.id" @click="handleDeleteElement(item)" class="element-delete-btn"
							src="/static/home/<USER>/chacha.png"></image> -->
					</movable-view>


				</template>
			</movable-area>

			<!-- 右侧保存按钮区域 -->
			<view class="editor-sidebar">
				<view class="save-button" @click="drawToCanvas">保 存</view>
			</view>
		</view>


		<!-- 底部编辑面板 -->
		<view class="editor-panel">
			<!-- 分类标签页 -->
			<u-tabs :list="tabList" @click="handleTabClick" :scrollable="false"
				:activeStyle="{ color: 'white', fontSize: '30rpx' }" :inactiveStyle="{ color: '#919191', fontSize: '30rpx' }"
				lineWidth="10" lineHeight="2" lineColor="#24b135" :current="activeTabIndex"></u-tabs>


			<!-- 元素选择区域 -->
			<view class="element-selection">
				<scroll-view scroll-y="true" class="element-scroll-area">

					<!-- 推荐模板选择 -->
					<template v-if="activeTabIndex == 0">
						<view v-for="item in recommendTemplates" class="recommend-item" @click="handleSelectTemplate(item)">
							<image :class="item.id == selectedListItemId ? 'recommend-item-active' : 'recommend-item-inactive'"
								:src="item.coverUrl" mode="widthFix">
							</image>
						</view>
					</template>

					<!-- 数字人选择 -->
					<template v-if="activeTabIndex == 1">
						<view v-if="digitalHumanList.length <= 0" class="virtual-human-add">
							<u-icon name="plus" size="60rpx" color="#8f8f8f"></u-icon>
						</view>
						<template v-else>

							<view v-for="item in digitalHumanList" class="virtual-human-item"
								@click.stop="handleSelectDigitalHuman(item)">
								<image :class="item.id == selectedListItemId ? 'element-item-active' : 'element-item-inactive'"
									:src="item.cover" mode="aspectFill">
								</image>
								<image v-if="item.is_pro === 'pro'" class="premium-tag" src="/static/index/pro_vip.png"></image>
							</view>
						</template>
					</template>

					<!-- 标题选择 -->
					<template v-if="activeTabIndex == 2">
						<view v-for="item in titleElements" class="title-item"
							:class="item.id == selectedListItemId ? 'title-item-active' : ''" @click="handleSelectTitle(item)">
							<image v-if="item.coverUrl" :src="item.coverUrl" mode="widthFix"></image>
							<view v-else class="title-empty">
								<image class="empty-icon" src="/static/home/<USER>/close_circle.png"></image>
							</view>
						</view>
					</template>

					<!-- 字幕选择 -->
					<template v-if="activeTabIndex == 3">
						<view v-for="item in captionElements" class="caption-item"
							:class="item.id == selectedListItemId ? 'caption-item-active' : ''" @click="handleSelectCaption(item)">
							<image v-if="item.coverUrl" :src="item.coverUrl"></image>
							<view v-else class="caption-empty">
								<image class="empty-icon" src="/static/home/<USER>/close_circle.png"></image>
							</view>
						</view>
					</template>

					<!-- 身份栏选择和编辑 -->
					<template v-if="activeTabIndex == 4">
						<!-- 身份栏文本编辑区域 -->
						<view class="identity-edit-form">
							<view>人物名称</view>
							<u-input placeholder="请输入人物名称，最多10个字" border="surround" maxlength="10" :value="identityName" color="white"
								@input="handleTextChange('name', $event)">
								<text slot="suffix" class="word-count" style="color: white;">{{ identityName ? identityName.length : 0
								}}/10</text>
							</u-input>
						</view>
						<view class="identity-edit-form">
							<view>人物介绍</view>
							<u-input placeholder="请输入人物介绍，最多60个字" border="surround" maxlength="60" :value="identityDesc" color="white"
								@input="handleTextChange('description', $event)">
								<text slot="suffix" class="word-count" style="color: white;">{{ identityDesc ? identityDesc.length : 0
								}}/60</text>
							</u-input>
						</view>

						<!-- 身份栏样式选择 -->
						<view v-for="item in identityCardElements" class="identity-item"
							:class="item.id == selectedListItemId ? 'identity-item-active' : ''"
							@click="handleSelectIntroduceCard(item)">
							<image :src="item.coverUrl" mode="widthFix"></image>
						</view>
					</template>

					<!-- 背景选择 -->
					<template v-if="activeTabIndex == 5">
						<view v-for="item in backgroundElements" class="background-item"
							:class="item.id == selectedListItemId ? 'background-item-active' : ''"
							@click="handleSelectBackground(item)">
							<image v-if="item.coverUrl" :src="item.coverUrl"></image>
							<view v-else class="background-empty">
								<image class="empty-icon" src="/static/home/<USER>/close_circle.png"></image>
							</view>
						</view>
					</template>
				</scroll-view>
			</view>
		</view>

		<!-- 未定制数字人提示弹窗 -->
		<projectModel v-if="showNoHumanDialog" title="提示" content="您尚未定制数字人,请完成定制数字人后再来设置模版吧~" save="确定"
			@btn_save="handleConfirmDialog" @btn_close="showNoHumanDialog = false">
		</projectModel>
		
		<!-- 添加加载蒙版 -->
		<view v-if="model_loading" class="project_model_1" style="background: rgba(0, 0, 0, 0.9);">
			<view class="project_model_2" style="background: none;color: white;">
				<u-loading-icon mode="circle" size="80rpx"></u-loading-icon>
				<view style="text-align: center;margin-top: 30rpx;">模板数据加载中...</view>
				<view style="text-align: center;margin: 20rpx 0;font-size: 24rpx;opacity: 0.8;">请稍候片刻</view>
			</view>
		</view>
	</view>
</template>
<script>
// 导入模板相关API
import {
	postTemplateoList,
	postTemplateoTitle
} from '../../../api/numberUser/dr.js'

// 导入保存模板的API
import {
	postTemplateSave
} from '../../../api/numberUser/userType.js'

// 导入上传工具
import { uploadUrl } from '../../../utils/ali-oss.js'

// 导入视频模板处理
import { videoTemplate, videoTemplateReverse, handleIntroduceCardData } from '@/utils/videoTemplate.js'

// 导入模板预览图生成工具
import { drawTemplatePreview } from '@/utils/templatePreview.js'

// 导入缩放滑块组件
import ScaleSlider from '../../../components/ScaleSlider.vue'

export default {
	components: {
		ScaleSlider // 注册组件
	},
	data() {
		return {
			model_loading: false, // 添加model_loading变量，用于控制加载蒙版的显示与隐藏
			digitalHumanVisible: true, // 控制数字人显示/隐藏的布尔值
			showNoHumanDialog: false, // 控制未定制数字人提示弹窗
			debounceTimer: null, // 防抖定时器(保留用于兼容)

			// 为每种元素类型添加独立的防抖定时器
			titleDebounceTimer: null, // 标题元素防抖定时器
			captionDebounceTimer: null, // 字幕元素防抖定时器
			virtualmanDebounceTimer: null, // 数字人元素防抖定时器
			introduceCardDebounceTimer: null, // 身份栏元素防抖定时器
			maskDebounceTimer: null, // 背景元素防抖定时器

			throttleLastTime: 0, // 节流上次执行时间
			_lastGetContainerSizeTime: 0, // 获取容器尺寸的上次执行时间
			_sizeInitialized: false, // 是否已初始化过尺寸
			currentElementType: '', // 当前编辑的元素类型
			currentTemplateStructure: [], // 存储当前选中的推荐模板完整结构

			selectedElementId: '', // 当前选中的元素ID
			selectedListItemId: '', // 当前选中的列表项ID

			digitalHumanId: '', // 数字人ID
			digitalHumanName: '', // 数字人名称
			templateId: '', // 模板ID

			movingElement: '', // 当前移动的元素
			recommendedId: 0, // 当前选择的推荐ID
			canvasScale: 0.4, // 画布缩放比例
			canvasData: [], // 当前画布内容数据
			elementScale: 10, // 元素缩放比例
			// 底部标签页配置
			tabList: [
				{
					name: '推荐'
				},
				{
					name: '数字人'
				},
				{
					name: '标题'
				},
				{
					name: '字幕'
				},
				{
					name: '身份栏'
				},
				{
					name: '背景'
				}
			],
			activeTabIndex: 0, // 当前选中的标签页索引

			// 各类元素数据
			recommendTemplates: [], // 推荐模板数据
			titleElements: [], // 标题元素数据
			captionElements: [], // 字幕元素数据
			identityCardElements: [], // 身份栏元素数据
			fontElements: [], // 字体元素数据
			backgroundElements: [], // 背景元素数据
			digitalHumanList: '', // 数字人数据

			// 分页参数
			currentPage: 1, // 当前页码
			pageSize: 100, // 每页数量

			tempValue1: '', // 备用值1
			tempValue2: '', // 备用值2

			// 身份栏文本内容
			identityName: '', // 人物名称
			identityDesc: '', // 人物介绍

			// 用于尺寸计算的临时文本内容
			tempText1: '', // 临时人物名称
			tempText2: '', // 临时人物介绍

			currentScaleValue: 100, // 当前元素缩放比例
			previewStyle: {},
			previewSrc: "",

			// 存储容器尺寸
			containerSize: {
				width: 0,
				height: 0
			},
			identityCardReady: false, // 新增：身份栏是否准备好显示
		}
	},

	// 计算属性
	computed: {
		// 提取身份栏数据
		introduceCardData() {
			return this.canvasData.find(item => {
				return item.type === 'introduceCard' && item.materialId
			})
		},

		// 返回身份栏元素尺寸
		introduceCardSize() {
			// 如果已经有获取到的容器尺寸，优先使用
			if (this.containerSize.width > 0 && this.containerSize.height > 0) {
				return {
					width: this.containerSize.width + 'px',
					height: this.containerSize.height + 'px'
				};
			}

			// 否则使用计算的宽高
			return {
				width: 136 + 'px',
				height: 37 + 'px'
			};
		}
	},

	// 页面加载时执行
	onLoad() {
		// 显示加载蒙版
		this.model_loading = true;

		// 获取屏幕宽度并计算缩放比例
		const info = uni.getSystemInfoSync();
		this.screenWidth = info.windowWidth;
		// 计算缩放比例 = (屏幕宽度 * 60%) / 1080
		this.canvasScale = (this.screenWidth * 0.6) / 1080;

		// 使用Promise.all并行请求多个接口数据
		Promise.all([
			//获取数字人模版
			this.$api.get_drList({
				pageNum: this.currentPage,
				pageSize: this.pageSize,
				status: 'success',
			}),
			// 获取标题列表
			postTemplateoTitle({
				type: 'title',
				page: this.currentPage,
				pageSize: this.pageSize
			}),
			// 获取字幕列表
			postTemplateoTitle({
				type: 'caption',
				page: this.currentPage,
				pageSize: this.pageSize
			}),
			// 获取身份栏
			postTemplateoTitle({
				type: 'introduceCard',
				page: this.currentPage,
				pageSize: this.pageSize
			}),
			// 获取背景列表
			postTemplateoTitle({
				type: 'mask',
				page: this.currentPage,
				pageSize: this.pageSize
			}),
			// 获取字体列表
			postTemplateoTitle({
				type: 'font',
				name: '黑字黄底1',
				page: this.currentPage,
				pageSize: this.pageSize
			})
		]).then(([numberRes, titleRes, captionRes, introduceCardRes, maskRes, fontRes]) => {

			//获取数字人列表
			if (numberRes.rows.length <= 0) {
				this.showNoHumanDialog = true // 如果没有数字人，显示提示弹窗
			} else {
				// 过滤成功状态的数字人并添加类型标识
				this.digitalHumanList = numberRes.rows.filter((item) => {
					item.is_pro = item.type
					item.type = 'virtualman'
					item.extra = JSON.parse(item.extra)
					item.scale = this.numberScale(item)

					return item.status == 'success'
				})
			}

			// 处理标题列表

			this.titleElements = titleRes.data.results;
			// 添加一个空白标题选项
			this.titleElements.unshift({
				url: '',
				id: '',
				type: 'title'
			});

			// 处理字幕列表

			this.captionElements = captionRes.data.results;
			// 添加一个空白字幕选项
			this.captionElements.unshift({
				url: '',
				id: '',
				type: 'caption'
			});

			// 处理身份栏

			// 去除后面三个元素
			this.identityCardElements = handleIntroduceCardData(this.canvasScale, introduceCardRes.data.results);

			// 处理背景列表

			this.backgroundElements = maskRes.data.results;
			// 添加一个空白背景选项
			this.backgroundElements.unshift({
				url: '',
				id: '',
				type: 'mask'
			});

			// 处理字体列表

			this.fontElements = fontRes.data.results;

			// 最后调用推荐列表接口
			return postTemplateoList({
				page: this.currentPage,
				pageSize: this.pageSize
			});
		}).then(res => {
			// 获取推荐模板列表
			this.recommendTemplates = videoTemplate(this.canvasScale, res.data.results);
			// 默认选中第一个推荐模板
			this.handleSelectTemplate(this.recommendTemplates[0], 'first');

			// 隐藏加载效果
			uni.hideLoading();
			
			// 隐藏加载蒙版
			this.model_loading = false;
		}).catch(error => {

			// 发生错误时也需要隐藏加载效果
			uni.hideLoading();
			// 隐藏加载蒙版
			this.model_loading = false;
			
			// 显示错误提示
			uni.showToast({
				title: '加载数据失败',
				icon: 'none',
				duration: 2000
			});
		});
	},

	// 监听数据变化
	watch: {
		// 监听身份栏数据变化
		introduceCardData: {
			handler(newVal, oldVal) {
				if (newVal) {
					// 使用自定义属性来防止循环更新
					// 如果尺寸数据已经获取过，且内容未改变，则不重新获取尺寸
					const oldText1 = oldVal?.define?.name?.content || '';
					const oldText2 = oldVal?.define?.description?.content || '';
					const newText1 = newVal?.define?.name?.content || '';
					const newText2 = newVal?.define?.description?.content || '';

					// 只在文本内容变化或第一次加载时获取尺寸
					if (!this._sizeInitialized || oldText1 !== newText1 || oldText2 !== newText2) {
						// 标记已经初始化过尺寸
						this._sizeInitialized = true;

						// 确保临时文本与实际文本同步
						this.tempText1 = this.identityName || '';
						this.tempText2 = this.identityDesc || '';

						// 使用节流控制获取尺寸的频率
						this._throttleGetContainerSize();
					}
				}
			},
			deep: true // 深度监听对象内部变化
		},

		// 监听实际文本变化，同步更新临时文本
		identityName(newVal) {
			this.tempText1 = newVal || '';
		},
		identityDesc(newVal) {
			this.tempText2 = newVal || '';
		}
	},

	// 页面显示后执行
	mounted() {
		// 页面加载完成后，获取容器尺寸
		this.$nextTick(() => {
			// 确保临时文本初始化与实际文本一致
			this.tempText1 = this.identityName || '';
			this.tempText2 = this.identityDesc || '';

			// 使用节流方法获取容器尺寸
			this._throttleGetContainerSize();
		});
	},

	methods: {
		// 添加：格式化文本方法，每15个字符换一行
		formatText(text) {
			if (!text) return [''];

			// 将文本按每15个字符分段
			const lines = [];
			let remainingText = text;

			while (remainingText.length > 0) {
				lines.push(remainingText.substring(0, 15));
				remainingText = remainingText.substring(15);
			}

			return lines;
		},

		// 节流获取容器尺寸
		_throttleGetContainerSize() {
			const now = Date.now();
			// 如果距离上次执行不足500ms，则不执行
			if (this._lastGetContainerSizeTime && now - this._lastGetContainerSizeTime < 50) {
				return;
			}
			this._lastGetContainerSizeTime = now;

			// 延迟获取容器尺寸，让UI有时间更新
			setTimeout(() => {
				this.getContainerSize((size) => {
					if (size) {

					} else {
						// 没有获取到有效尺寸，但仍然设置身份栏为ready状态
						const introduceCard = this.canvasData.find(item => item.type === 'introduceCard');
						if (introduceCard && !introduceCard.ready) {
							// 设置默认尺寸
							if (!introduceCard.define.width || !introduceCard.define.height) {
								introduceCard.define.width = 136;
								introduceCard.define.height = 37;

								// 设置默认锚点
								if (!introduceCard.define.transform) {
									this.$set(introduceCard.define, 'transform', {});
								}
								this.$set(introduceCard.define.transform, 'anchor', [68, 18.5, 0]);
							}

							// 设置为准备好状态
							this.$set(introduceCard, 'ready', true);

						}
					}
				});
			}, 100);
		},

		// 获取身份栏容器尺寸
		getContainerSize(callback) {
			// 检查是否有有效的身份栏数据
			if (!this.introduceCardData) {

				if (typeof callback === 'function') {
					callback(null);
				}
				return;
			}

			// 使用nextTick确保DOM已更新
			this.$nextTick(() => {
				const query = uni.createSelectorQuery().in(this);
				query.select('.identity-card-container').boundingClientRect(data => {
					if (!data) {

						if (typeof callback === 'function') {
							callback(null);
						}
						return;
					}

					// 检查尺寸是否有实际变化
					const hasChanged = this.containerSize.width !== data.width ||
						this.containerSize.height !== data.height;

					// 只有尺寸变化时才更新
					if (hasChanged) {

						this.containerSize.width = data.width;
						this.containerSize.height = data.height;

						// 找到身份栏元素并更新其尺寸和锚点
						const introduceCard = this.canvasData.find(item => item.type === 'introduceCard');
						if (introduceCard && introduceCard.define) {
							// 更新define中的width和height属性
							introduceCard.define.width = data.width;
							introduceCard.define.height = data.height;


							// 确保transform结构存在
							if (!introduceCard.define.transform) {
								this.$set(introduceCard.define, 'transform', {});
							}

							// 更新锚点为宽度/2，高度/2，0
							const newAnchor = [
								data.width / 2,
								data.height / 2,
								0
							];

							this.$set(introduceCard.define.transform, 'anchor', newAnchor);

							// 如果有锚点，需要同步更新元素的位置，保持视觉上的不变
							this.updateElementPositionAfterResize(introduceCard, data.width, data.height);

							// 设置ready状态为true，允许显示身份栏
							this.$set(introduceCard, 'ready', true);

						}
					} else {

						// 即使尺寸未变化，也需要设置ready状态
						const introduceCard = this.canvasData.find(item => item.type === 'introduceCard');
						if (introduceCard) {
							this.$set(introduceCard, 'ready', true);

						}
					}

					// 执行回调
					if (typeof callback === 'function') {
						callback(this.containerSize);
					}
				}).exec();
			});
		},

		// 在更改身份栏尺寸后更新其位置，保持视觉上的相对位置不变
		updateElementPositionAfterResize(element, newWidth, newHeight) {
			// 只有在有transform和position的情况下才调整位置
			if (element.define && element.define.transform && element.define.transform.position) {
				// 当前位置已经考虑了锚点，不需要额外调整

			}
		},

		// 计算背景显示
		calculateBackgroundDisplay(define) {
			let background = define.background;
			let padding = {
				paddingTop: define.padding?.top + 'px' || '0px',
				paddingBottom: define.padding?.bottom + 'px' || '0px',
				paddingLeft: define.padding?.left + 'px' || '0px',
				paddingRight: define.padding?.right + 'px' || '0px',
			}
			let backgroundObject = {
				backgroundColor: '',
			};
			if (background && background.url) {
				backgroundObject = {
					backgroundImage: `url(${background.url})`,
					backgroundSize: 'cover',
					backgroundPosition: 'center',
					backgroundRepeat: 'no-repeat',
				}
			}
			if (background && background.color) {
				backgroundObject.backgroundColor = background.color;
			}
			return {
				...backgroundObject,
				...padding,
			}
		},

		// 获取背景URL
		getBackgroundUrl(id) {
			if (!id || id === '') return '';
			const background = this.backgroundElements.find((item) => item.id == id);
			return background ? background.coverUrl : '';
		},

		// 计算数字人缩放比例
		numberScale(item) {
			// 宽度四舍五入后除以1080
			return 1080 / (Math.round(item.extra.width / 10) * 10)
		},

		// 验证文本输入，确保符合字数限制
		validateTextInput() {
			// 限制人物名称最多10个字
			if (this.tempText1 && this.tempText1.length > 10) {
				this.tempText1 = this.tempText1.substring(0, 10);
				uni.showToast({
					title: '人物名称最多10个字',
					icon: 'none',
					duration: 1500
				});
			}

			// 限制人物介绍最多60个字
			if (this.tempText2 && this.tempText2.length > 10) {
				this.tempText2 = this.tempText2.substring(0, 10);
				uni.showToast({
					title: '人物介绍最多10个字',
					icon: 'none',
					duration: 1500
				});
			}

			// 更新实际文本
			this.identityName = this.tempText1;
			this.identityDesc = this.tempText2;

			// 更新身份栏元素中的文本
			this.updateIntroduceCardText();

			// 使用节流方法获取容器尺寸
			this._throttleGetContainerSize();
		},

		// 更新身份栏元素中的文本内容
		updateIntroduceCardText() {
			// 查找当前数据中的身份栏元素
			const introduceCard = this.canvasData.find(item => item.type === 'introduceCard');
			if (introduceCard && introduceCard.define) {
				// 先设置ready为false，等待尺寸获取完成
				this.$set(introduceCard, 'ready', false);


				// 如果存在name和description属性，更新其内容
				if (introduceCard.define.name) {
					introduceCard.define.name.content = this.identityName;
				}
				if (introduceCard.define.description) {
					introduceCard.define.description.content = this.identityDesc;
				}

				// 更新宽高信息 - 直接从containerSize同步更新
				if (this.containerSize.width > 0 && this.containerSize.height > 0) {
					// 直接修改define的width和height
					introduceCard.define.width = this.containerSize.width;
					introduceCard.define.height = this.containerSize.height;

					// 确保transform结构存在
					if (!introduceCard.define.transform) {
						this.$set(introduceCard.define, 'transform', {});
					}

					// 更新锚点为宽度/2，高度/2，0
					const newAnchor = [
						this.containerSize.width / 2,
						this.containerSize.height / 2,
						0
					];

					this.$set(introduceCard.define.transform, 'anchor', newAnchor);


					// 同步更新元素的位置
					this.updateElementPositionAfterResize(introduceCard, this.containerSize.width, this.containerSize.height);
				} else {
					// 如果containerSize还没有值，需要获取一次容器尺寸

					this._throttleGetContainerSize();
				}

				// 确保临时文本与实际文本保持同步
				this.tempText1 = this.identityName;
				this.tempText2 = this.identityDesc;


			}
		},

		// 绘制Canvas生成预览图
		drawToCanvas() {
			// 调用抽离出来的模板预览图生成工具 增加一秒的延时防止用户点击保存过块
			uni.showLoading()
			setTimeout(() => {
				drawTemplatePreview({
					canvasData: this.canvasData,
					uploadCallback: (tempFilePath) => this.handleSavePreview(tempFilePath),
					canvasScale: this.canvasScale,
					backgroundElements: this.backgroundElements,
					identityName: this.identityName,
					identityDesc: this.identityDesc,
					formatTextFn: this.formatText
				});
			}, 1000)
		},

		// 处理未定制数字人弹窗确认按钮
		handleConfirmDialog() {
			this.showNoHumanDialog = false
			uni.navigateBack()
		},

		// 保存模板数据
		handleSavePreview(path) {


			// 检查是否已经是有效的URL
			if (path.startsWith('http')) {
				// 如果已经是URL，直接使用
				this.saveTemplateWithUrl(path);
			} else {
				// 如果是临时文件路径，先上传
				uni.showLoading({
					title: '上传预览图...'
				});

				uploadUrl(path).then(url => {

					this.saveTemplateWithUrl(url);
				}).catch(error => {

					uni.hideLoading();
					uni.showToast({
						title: '预览图上传失败',
						icon: 'none'
					});
				});
			}
		},

		// 使用URL保存模板
		saveTemplateWithUrl(url) {


			// 准备模板保存参数
			const option = {
				name: this.digitalHumanName, // 模板名称
				figureId: this.digitalHumanId, // 数字人ID
				titleId: null, // 标题ID
				titleUrl: null, // 标题URL
				captionId: null, // 字幕ID 
				captionUrl: null, // 字幕URL
				introduceCardId: null, // 身份栏ID
				introduceCardUrl: null, // 身份栏URL
				bgUrl: null, // 背景URL
				bgId: null, // 背景ID
				introduceCardName: null, // 身份栏名称
				introduceCardDesc: null, // 身份栏描述
				metadata: '', // 元数据
				templateId: this.templateId, // 模板ID
				preUrl: url, // 预览图URL
			}

			// 遍历所有元素，更新对应信息
			this.canvasData.map(item => {
				item.materialId = item.materialId || item.id || ''

				// 处理标题元素
				if (item.type == 'title') {
					option.titleId = item.materialId
					option.titleUrl = item.url
				}
				// 处理字幕元素
				if (item.type == 'caption') {
					option.captionId = item.materialId
					option.captionUrl = item.url
				}
				// 处理背景元素
				if (item.type == 'mask') {
					option.bgUrl = item.url
					option.bgId = item.materialId
				}
				// 处理身份栏元素
				if (item.type == 'introduceCard') {
					if (item.define.name) {
						// 更新身份栏文本
						item.define.name.content = this.identityName
						item.define.description.content = this.identityDesc
					}
					option.introduceCardName = this.identityName
					option.introduceCardDesc = this.identityDesc
					option.introduceCardId = item.materialId
					option.introduceCardUrl = ''
				}
			})


			// 将元素数据转为JSON字符串
			option.metadata = JSON.stringify((videoTemplateReverse(this.canvasScale, this.canvasData)))

			// 调用保存接口
			postTemplateSave(option).then(res => {

				uni.hideLoading()
				uni.navigateBack()
			})
		},

		// 对象深合并方法
		deepMerge(target, source) {
			for (const key in source) {
				if (source[key] instanceof Object && target[key]) {
					this.deepMerge(target[key], source[key]);
				} else {
					target[key] = source[key];
				}
			}
			return target;
		},

		// 移除元素
		handleDeleteElement(item) {
			// 从canvasData数组中真正删除元素
			const index = this.canvasData.findIndex(res => res.id === item.id);
			if (index !== -1) {
				this.canvasData.splice(index, 1);

				// 清除选中状态
				if (this.selectedElementId === item.id) {
					this.selectedElementId = '';
				}
			}
		},

		// 点击元素，切换到对应的编辑标签页
		handleSelectElement(item) {
			this.currentElementType = item.type;
			this.selectedElementId = item.id;
			this.selectedListItemId = item.id;

			// 设置当前元素的缩放值，但身份栏、字幕和背景不设置缩放
			if (item.type !== 'caption' && item.type !== 'introduceCard' && item.type !== 'mask') {
				if (item.define && item.define.transform && item.define.transform.scalar) {
					this.currentScaleValue = item.define.transform.scalar[0];
				} else {
					this.currentScaleValue = 100;

					// 初始化scalar属性
					if (!item.define) {
						this.$set(item, 'define', {});
					}

					if (!item.define.transform) {
						this.$set(item.define, 'transform', {});
					}

					if (!item.define.transform.scalar) {
						this.$set(item.define.transform, 'scalar', [100, 100, 100]);
					}
				}
			}

			// 根据元素类型切换到相应标签页
			if (item.type == 'virtualman') this.activeTabIndex = 1;
			if (item.type == 'title') this.activeTabIndex = 2;
			if (item.type == 'caption') this.activeTabIndex = 3;
			if (item.type == 'introduceCard') this.activeTabIndex = 4;
			if (item.type == 'mask') this.activeTabIndex = 5;
		},

		// 获取当前移动的元素
		setCurrentMovingElement(item) {
			this.movingElement = item
		},

		// 选择推荐模板
		handleSelectTemplate(data, value) {
			this.templateId = data.id
			this.selectedListItemId = data.id
			this.currentElementType = 'recommend' // 设置类型为推荐，不显示缩放滑块
			this.selectedElementId = '' // 清除当前选中的元素ID

			// 存储当前推荐模板的完整结构，供后续使用
			this.currentTemplateStructure = JSON.parse(JSON.stringify(data.metadata))

			// 清空当前数据，只保留数字人
			this.canvasData = this.canvasData.filter(res => {
				return res.type == "virtualman"
			})
			// 处理模板元数据中的各种元素
			data.metadata.map((item) => {
				// 处理标题元素
				if (item.type == 'title') {
					this.titleElements.map((res) => {
						if (res.id == item.materialId) {
							// 确保元素有transform和scalar属性
							if (!item.define.transform) {
								item.define.transform = {};
							}

							if (!item.define.transform.scalar) {
								item.define.transform.scalar = [100, 100, 0];
							}

							this.canvasData.push({
								'url': res.editorPreviewImageUrl,
								'define': item.define,
								'id': res.id,
								'type': res.type,
								imageWidth: 0,
								imageHeight: 0
							})
						}
					})
					if (!item.materialId) {
						// 确保元素有transform属性
						if (!item.define.transform) {
							item.define.transform = {};
						}

						// 标题元素固定缩放比例
						item.define.transform.scalar = [100, 100, 0];

						this.canvasData.push(item);
					}
				}

				// 处理身份栏元素
				if (item.type == 'introduceCard') {
					this.identityCardElements.map((res) => {

						if (res.id == item.materialId) {
							// 确保元素有transform属性
							if (!item.define.transform) {
								item.define.transform = {};
							}

							// 身份栏元素固定缩放比例
							item.define.transform.scalar = [100, 100, 0];

							// 身份栏文本准备
							const name = item.define?.contents?.[0] || '';
							const desc = item.define?.contents?.[1] || '';

							// 创建身份栏元素，初始ready为false
							const introduceCardElement = {
								define: item.define,
								id: res.id,
								materialId: item.materialId,
								type: res.type,
								ready: false
							};

							// 确保身份栏元素包含name和description属性用于文本显示
							if (!introduceCardElement.define.name) {
								this.$set(introduceCardElement.define, 'name', {
									color: '#ffffff',
									content: name
								});
							} else {
								introduceCardElement.define.name.content = name;
							}

							if (!introduceCardElement.define.description) {
								this.$set(introduceCardElement.define, 'description', {
									color: '#ffffff',
									content: desc
								});
							} else {
								introduceCardElement.define.description.content = desc;
							}

							// 添加到画布数据
							this.canvasData.push(introduceCardElement);

							// 设置身份栏文本
							this.identityName = name;
							this.identityDesc = desc;

							// 同步更新临时文本
							this.tempText1 = this.identityName;
							this.tempText2 = this.identityDesc;

							// 触发尺寸获取
							this.$nextTick(() => {
								this._throttleGetContainerSize();
							});
						}
					})
					if (!item.materialId) {
						// 确保元素有transform属性
						if (!item.define.transform) {
							item.define.transform = {};
						}

						// 身份栏元素固定缩放比例
						item.define.transform.scalar = [100, 100, 0];

						// 添加ready属性为false
						item.ready = false;

						this.canvasData.push(item);
					}
				}

				// 处理字幕元素
				if (item.type == 'caption') {
					this.captionElements.map((res) => {
						if (res.id == item.materialId) {
							// 确保元素有transform属性
							if (!item.define.transform) {
								item.define.transform = {};
							}

							// 字幕元素固定缩放比例
							item.define.transform.scalar = [100, 100, 100];

							this.canvasData.push({
								'url': res.editorPreviewImageUrl,
								'define': item.define,
								'id': res.id,
								'type': res.type,
								imageWidth: 0,
								imageHeight: 0
							})
						}
					})
					if (!item.materialId) {
						// 确保元素有transform属性
						if (!item.define.transform) {
							item.define.transform = {};
						}

						// 字幕元素固定缩放比例
						item.define.transform.scalar = [100, 100, 100];

						this.canvasData.push(item);
					}
				}

				// 处理背景元素
				if (item.type == 'mask') {
					this.backgroundElements.map((res) => {
						if (res.id == item.materialId) {
							// 确保元素有transform和scalar属性，虽然背景通常不需要缩放
							if (!item.define.transform) {
								item.define.transform = {};
							}

							this.canvasData.push({
								'define': item.define,
								'id': res.id,
								'type': res.type
							})
						}
					})
				}

				// 处理字体元素
				if (item.type == 'font') {
					this.fontElements.map((res) => {
						if (res.id == item.materialId) {
							// 确保元素有transform和scalar属性
							if (!item.define.transform) {
								item.define.transform = {};
							}

							if (!item.define.transform.scalar) {
								item.define.transform.scalar = [100, 100, 100];
							}

							this.canvasData.push({
								'url': res.editorPreviewImageUrl,
								'define': item.define,
								'id': res.id,
								'type': res.type
							})
						}
					})
					if (!item.materialId) {
						// 确保元素有transform和scalar属性
						if (!item.define.transform) {
							item.define.transform = {};
						}

						if (!item.define.transform.scalar) {
							item.define.transform.scalar = [100, 100, 100];
						}

						this.canvasData.push(item);
					}
				}
			});

			this.canvasData.forEach((item) => {
				if (item.type == 'title' || item.type == 'caption') {
					uni.getImageInfo({
						src: item.url,
						success: (res) => {
							item.imageWidth = res.width
							item.imageHeight = res.height
						}
					})
				}
			})

			// 如果是首次加载且有数字人，则选中第一个数字人
			if (this.digitalHumanList.length > 0 && value) {
				this.handleSelectDigitalHuman(this.digitalHumanList[0], 'first')
			}
		},

		// 选择数字人
		handleSelectDigitalHuman(item, value) {
			// 先隐藏数字人
			this.digitalHumanVisible = false;

			this.digitalHumanId = item.outId
			this.digitalHumanName = item.name
			this.currentElementType = item.type

			// 不再清除选中的元素ID，允许显示缩放控制器
			// this.selectedElementId = ''

			if (!value) {
				this.selectedListItemId = item.id
			}

			// 创建完整的数字人元素结构
			const digitalHumanElement = {
				...item,
				define: {
					width: item.extra.width,
					height: item.extra.height,
					transform: {
						// 设置锚点为中心点
						anchor: [item.extra.width / 2 * item.scale * this.canvasScale, item.extra.height / 2 * item.scale * this.canvasScale, 0],
						scalar: [100, 100, 0],
						position: [540 * this.canvasScale, 960 * this.canvasScale, 0], // 默认居中位置
						rotationX: 0,
						rotationY: 0,
						rotationZ: 0
					},
					layer: {
						width: 1080,
						height: 1920,
						transform: {
							position: [0, 0, 0]
						}
					}
				}
			}

			// 检查是新增还是更新
			if (value == 'first') {
				this.canvasData.push(digitalHumanElement)
			} else {
				// 更新现有数字人
				const index = this.canvasData.findIndex(item_1 => item_1.type === 'virtualman')
				if (index !== -1) {
					this.canvasData.splice(index, 1)
					this.canvasData.push(digitalHumanElement)
					this.handleSelectElement(digitalHumanElement)
				} else {
					this.canvasData.push(digitalHumanElement)
					this.handleSelectElement(digitalHumanElement)
				}
			}

			// 设置当前缩放值
			this.currentScaleValue = 100;




			// 50毫秒后显示数字人
			setTimeout(() => {
				this.digitalHumanVisible = true;
			}, 50);
		},

		// 点击标签页
		handleTabClick(e) {
			this.activeTabIndex = e.index

			// 根据点击的标签页类型设置 currentElementType
			if (e.index === 0) this.currentElementType = 'recommend'; // 推荐
			else if (e.index === 1) this.currentElementType = 'virtualman'; // 数字人
			else if (e.index === 2) this.currentElementType = 'title'; // 标题
			else if (e.index === 3) this.currentElementType = 'caption'; // 字幕
			else if (e.index === 4) this.currentElementType = 'introduceCard'; // 身份栏
			else if (e.index === 5) this.currentElementType = 'mask'; // 背景

			// 清除当前选中的元素ID，避免缩放滑块显示
			// 但数字人标签页不清除，允许缩放控制器显示
			if (e.index === 0 || e.index === 3 || e.index === 4 || e.index === 5) {
				this.selectedElementId = '';
			}


		},

		// 缩放值变化
		handleSliderChange(value) {
			this.page_scal = value
		},

		// 元素位置变化处理
		onChange(e, item, index) {


			// 忽略非触摸事件
			if (e.detail.source != 'touch') return

			// 使用防抖控制更新频率，取消上一个计时器
			if (this.debounceTimer) {
				clearTimeout(this.debounceTimer);
			}

			// 创建新的计时器
			this.debounceTimer = setTimeout(() => {
				// 执行位置更新
				this.currentElementType = item.type;

				// 对于数字人元素，需要考虑缩放因素
				if (item.type === 'virtualman') {
					const scalarRatio = item.define.transform.scalar ? item.define.transform.scalar[0] / 100 : 1;
					// 计算移动位置，需要考虑缩放比例
					let positionX = e.detail.x + this.canvasData[index].define.transform.anchor[0] * scalarRatio;
					let positionY = e.detail.y + this.canvasData[index].define.transform.anchor[1] * scalarRatio;

					this.$set(this.canvasData[index].define.transform, 'position', [
						positionX,
						positionY,
						0
					]);
				}
				// 标题元素位置计算
				else if (item.type == 'title') {
					// 计算移动位置
					let positionX = e.detail.x + this.canvasData[index].define.transform.anchor[0]
					let positionY = e.detail.y + this.canvasData[index].define.transform.anchor[1]

					this.$set(this.canvasData[index].define.transform, 'position', [
						positionX,
						positionY,
						0
					]);
				} else if (item.type == 'introduceCard') {
					// 计算移动位置
					let positionX = e.detail.x + this.canvasData[index].define.transform.anchor[0]
					let positionY = e.detail.y + this.canvasData[index].define.transform.anchor[1]


					this.$set(this.canvasData[index].define.transform, 'position', [
						positionX,
						positionY,
						0
					]);

					// 身份栏位置变化，重新获取尺寸
					this.getContainerSize((size) => {
						if (size) {

						}
					});
				} else {
					// 计算移动位置
					let positionX = e.detail.x + this.canvasData[index].define.transform.anchor[0]
					let positionY = e.detail.y + this.canvasData[index].define.transform.anchor[1]

					this.$set(this.canvasData[index].define.transform, 'position', [
						positionX,
						positionY,
						0
					]);
				}


			}, 500); // 等待500毫秒后执行，如果500毫秒内再次触发则重新计时
		},

		// 处理缩放值变化
		handleScaleChange(value) {
			// 使用节流控制更新频率
			const now = Date.now();
			// 节流间隔，毫秒
			const throttleInterval = 200;

			// 如果距离上次执行不足节流时间间隔，则不执行
			if (now - this.throttleLastTime < throttleInterval) {
				return;
			}

			// 更新上次执行时间
			this.throttleLastTime = now;

			// 查找当前选中的元素
			const selectedItem = this.canvasData.find(item => item.id === this.selectedElementId);
			if (!selectedItem) return;

			// 身份栏和字幕不应用缩放
			if (selectedItem.type === 'caption' || selectedItem.type === 'introduceCard') {

				return;
			}

			// 确保元素有完整的transform和scalar属性
			if (!selectedItem.define) {
				this.$set(selectedItem, 'define', {});
			}

			if (!selectedItem.define.transform) {
				this.$set(selectedItem.define, 'transform', {});
			}

			// 获取当前z轴的值，如果不存在则使用默认值100
			const currentZValue = selectedItem.define.transform.scalar ?
				selectedItem.define.transform.scalar[2] : 100;

			// 更新scalar属性，x和y设置为相同的值，保持z不变
			this.$set(selectedItem.define.transform, 'scalar', [
				value,
				value,
				currentZValue
			]);

			// 如果是数字人元素，还需要调整位置，保持中心点不变
			if (selectedItem.type === 'virtualman') {
				// 获取当前元素的位置和锚点
				const position = selectedItem.define.transform.position;
				const anchor = selectedItem.define.transform.anchor;
				const oldScalar = selectedItem.define.transform._oldScalar || 100;
				const newScalar = value;

				// 记录旧的缩放值，用于下次计算相对变化
				this.$set(selectedItem.define.transform, '_oldScalar', newScalar);

				// 注意，这里不需要调整position，因为我们已经在movable-view的x和y属性中
				// 计算了考虑缩放因素的偏移量
			}


		},

		// 处理文本变化
		handleTextChange(field, event) {
			// 保存输入值 - 在uView UI中，event可能直接是值本身
			const value = typeof event === 'string' ? event : event?.detail?.value || event;

			// 如果没有获取到有效值，直接返回
			if (value === undefined || value === null) {

				return;
			}

			// 查找身份栏元素并设置ready为false
			const introduceCard = this.canvasData.find(item => item.type === 'introduceCard');
			if (introduceCard) {
				this.$set(introduceCard, 'ready', false);

			}

			// 字段映射
			const fieldMap = {
				'name': {
					temp: 'tempText1',
					actual: 'identityName'
				},
				'description': {
					temp: 'tempText2',
					actual: 'identityDesc'
				}
			};

			// 文本验证
			if (field === 'name' && value.length > 10) {
				uni.showToast({
					title: '人物名称最多10个字',
					icon: 'none',
					duration: 1500
				});
				return;
			}

			if (field === 'description' && value.length > 60) {
				uni.showToast({
					title: '人物介绍最多60个字',
					icon: 'none',
					duration: 1500
				});
				return;
			}

			// 记录接收到的事件和提取的值


			// 更新临时文本以便计算尺寸
			this[fieldMap[field].temp] = value;

			// 更新实际文本
			this[fieldMap[field].actual] = value;

			// 更新身份栏元素中的文本内容
			this.updateIntroduceCardText();

			// 使用节流方法获取容器尺寸
			this._throttleGetContainerSize();
		},


		// 处理标题元素选择
		handleSelectTitle(sourceItem) {
			// 更新选中状态
			this.currentElementType = sourceItem.type;
			this.selectedListItemId = sourceItem.id;

			// 查找存储的模板中对应类型的元素结构
			let templateElement = this.currentTemplateStructure.find(item => item.type === 'title');

			// 如果找不到，使用默认结构
			if (!templateElement) {
				templateElement = {
					type: 'title',
					define: {
						width: 200,
						height: 100,
						transform: {
							position: [540 * this.canvasScale, 300 * this.canvasScale, 0],
							anchor: [100, 50, 0],
							scalar: [100, 100, 100]
						}
					}
				};
			}

			// 查找是否已存在标题元素
			const existingIndex = this.canvasData.findIndex(item => item.type === 'title');

			// 创建新的标题元素
			const titleElement = {
				...JSON.parse(JSON.stringify(templateElement)),
				id: sourceItem.id,
				materialId: sourceItem.id,
				url: sourceItem.editorPreviewImageUrl,
				type: 'title',
				imageWidth: 0,
				imageHeight: 0
			};

			// 更新或添加元素
			if (existingIndex !== -1) {
				this.canvasData.splice(existingIndex, 1, titleElement);
			} else {
				this.canvasData.push(titleElement);
			}

			// 获取标题图片尺寸
			if (titleElement.url) {
				uni.getImageInfo({
					src: titleElement.url,
					success: (res) => {
						titleElement.imageWidth = res.width;
						titleElement.imageHeight = res.height;
					}
				});
			}


		},

		// 处理字幕元素选择
		handleSelectCaption(sourceItem) {
			// 更新选中状态
			this.currentElementType = sourceItem.type;
			this.selectedListItemId = sourceItem.id;

			// 查找存储的模板中对应类型的元素结构
			let templateElement = this.currentTemplateStructure.find(item => item.type === 'caption');

			// 如果找不到，使用默认结构
			if (!templateElement) {
				templateElement = {
					type: 'caption',
					define: {
						width: 300,
						height: 100,
						transform: {
							position: [540 * this.canvasScale, 1600 * this.canvasScale, 0],
							anchor: [150, 50, 0],
							scalar: [100, 100, 100]
						}
					}
				};
			}

			// 查找是否已存在字幕元素
			const existingIndex = this.canvasData.findIndex(item => item.type === 'caption');

			// 创建新的字幕元素
			const captionElement = {
				...JSON.parse(JSON.stringify(templateElement)),
				id: sourceItem.id,
				materialId: sourceItem.id,
				url: sourceItem.editorPreviewImageUrl,
				type: 'caption',
				imageWidth: 0,
				imageHeight: 0
			};

			// 更新或添加元素
			if (existingIndex !== -1) {
				this.canvasData.splice(existingIndex, 1, captionElement);
			} else {
				this.canvasData.push(captionElement);
			}

			// 获取字幕图片尺寸
			if (captionElement.url) {
				uni.getImageInfo({
					src: captionElement.url,
					success: (res) => {
						captionElement.imageWidth = res.width;
						captionElement.imageHeight = res.height;
					}
				});
			}


		},

		// 处理身份栏元素选择
		handleSelectIntroduceCard(sourceItem) {
			// 更新选中状态
			this.currentElementType = sourceItem.type;
			this.selectedListItemId = sourceItem.id;

			// 查找是否已存在身份栏元素
			const existingIndex = this.canvasData.findIndex(item => item.type === 'introduceCard');

			// 确保renderDefine存在
			if (!sourceItem.renderDefine) {

				// 创建一个基本结构
				sourceItem.renderDefine = {
					background: {
						url: sourceItem.renderDefine?.background?.url || ''
					},
					name: { color: '#ffffff' },
					description: { color: '#ffffff' }
				};
			}

			// 创建新的身份栏元素
			const introduceCardElement = {
				id: sourceItem.id,
				materialId: sourceItem.id,
				type: 'introduceCard',
				ready: false,
				define: {
					...sourceItem.renderDefine,
					width: this.containerSize.width > 0 ? this.containerSize.width : 136,
					height: this.containerSize.height > 0 ? this.containerSize.height : 37,
					transform: {
						position: [440 * this.canvasScale, 1300 * this.canvasScale, 0],
						anchor: [
							(this.containerSize.width > 0 ? this.containerSize.width : 136) / 2,
							(this.containerSize.height > 0 ? this.containerSize.height : 37) / 2,
							0
						],
						scalar: [100, 100, 100]
					}
				}
			};

			// 确保身份栏元素包含name和description属性
			if (!introduceCardElement.define.name) {
				this.$set(introduceCardElement.define, 'name', {
					color: '#ffffff',
					content: this.identityName || '姓名'
				});
			} else {
				introduceCardElement.define.name.content = this.identityName || '姓名';
			}

			if (!introduceCardElement.define.description) {
				this.$set(introduceCardElement.define, 'description', {
					color: '#ffffff',
					content: this.identityDesc || '职位描述'
				});
			} else {
				introduceCardElement.define.description.content = this.identityDesc || '职位描述';
			}

			// 更新或添加元素
			if (existingIndex !== -1) {
				this.canvasData.splice(existingIndex, 1, introduceCardElement);
			} else {
				this.canvasData.push(introduceCardElement);
			}

			// 初始化或保持身份栏文本内容
			if (!this.identityName || this.identityName.trim() === '') {
				this.identityName = '姓名';
			}

			if (!this.identityDesc || this.identityDesc.trim() === '') {
				this.identityDesc = '职位描述';
			}

			// 同步更新临时文本
			this.tempText1 = this.identityName;
			this.tempText2 = this.identityDesc;

			// 触发尺寸获取
			this.$nextTick(() => {
				this._throttleGetContainerSize();
			});


		},


		// 处理背景元素选择
		handleSelectBackground(sourceItem) {
			// 更新选中状态
			this.currentElementType = sourceItem.type;
			this.selectedListItemId = sourceItem.id;

			// 查找存储的模板中对应类型的元素结构
			let templateElement = this.currentTemplateStructure.find(item => item.type === 'mask');

			// 如果找不到，使用默认结构
			if (!templateElement) {
				templateElement = {
					type: 'mask',
					define: {
						transform: {
							position: [0, 0, 0],
							anchor: [0, 0, 0]
						},
						width: 1080 * this.canvasScale,
						height: 1920 * this.canvasScale
					}
				};
			}

			// 查找是否已存在背景元素
			const existingIndex = this.canvasData.findIndex(item => item.type === 'mask');

			// 创建新的背景元素
			const backgroundElement = {
				...JSON.parse(JSON.stringify(templateElement)),
				id: sourceItem.id,
				materialId: sourceItem.id,
				type: 'mask'
			};

			// 更新或添加元素
			if (existingIndex !== -1) {
				this.canvasData.splice(existingIndex, 1, backgroundElement);
			} else {
				this.canvasData.push(backgroundElement);
			}


		},

		// 针对标题元素的位置变化处理
		onTitleChange(e, item, index) {
			// 忽略非触摸事件
			if (e.detail.source != 'touch') return

			// 使用防抖控制更新频率，取消上一个计时器
			if (this.titleDebounceTimer) {
				clearTimeout(this.titleDebounceTimer);
			}

			// 创建新的计时器
			this.titleDebounceTimer = setTimeout(() => {
				this.currentElementType = 'title';

				// 计算移动位置
				let positionX = e.detail.x + this.canvasData[index].define.transform.anchor[0]
				let positionY = e.detail.y + this.canvasData[index].define.transform.anchor[1]

				this.$set(this.canvasData[index].define.transform, 'position', [
					positionX,
					positionY,
					0
				]);


			}, 500); // 等待500毫秒后执行，如果500毫秒内再次触发则重新计时
		},

		// 针对字幕元素的位置变化处理
		onCaptionChange(e, item, index) {
			// 忽略非触摸事件
			if (e.detail.source != 'touch') return

			// 使用防抖控制更新频率，取消上一个计时器
			if (this.captionDebounceTimer) {
				clearTimeout(this.captionDebounceTimer);
			}

			// 创建新的计时器
			this.captionDebounceTimer = setTimeout(() => {
				this.currentElementType = 'caption';

				// 计算移动位置
				let positionX = e.detail.x + this.canvasData[index].define.transform.anchor[0]
				let positionY = e.detail.y + this.canvasData[index].define.transform.anchor[1]

				this.$set(this.canvasData[index].define.transform, 'position', [
					positionX,
					positionY,
					0
				]);


			}, 500); // 等待500毫秒后执行，如果500毫秒内再次触发则重新计时
		},

		// 针对背景元素的位置变化处理
		onMaskChange(e, item, index) {
			// 忽略非触摸事件
			if (e.detail.source != 'touch') return

			// 使用防抖控制更新频率，取消上一个计时器
			if (this.maskDebounceTimer) {
				clearTimeout(this.maskDebounceTimer);
			}

			// 创建新的计时器
			this.maskDebounceTimer = setTimeout(() => {
				this.currentElementType = 'mask';

				// 计算移动位置
				let positionX = e.detail.x + this.canvasData[index].define.transform.anchor[0]
				let positionY = e.detail.y + this.canvasData[index].define.transform.anchor[1]

				this.$set(this.canvasData[index].define.transform, 'position', [
					positionX,
					positionY,
					0
				]);


			}, 500); // 等待500毫秒后执行，如果500毫秒内再次触发则重新计时
		},

		// 针对数字人元素的位置变化处理
		onVirtualmanChange(e, item, index) {
			// 忽略非触摸事件
			if (e.detail.source != 'touch') return

			// 使用防抖控制更新频率，取消上一个计时器
			if (this.virtualmanDebounceTimer) {
				clearTimeout(this.virtualmanDebounceTimer);
			}

			// 创建新的计时器
			this.virtualmanDebounceTimer = setTimeout(() => {
				this.currentElementType = 'virtualman';

				// 对于数字人元素，需要考虑缩放因素
				const scalarRatio = item.define.transform.scalar ? item.define.transform.scalar[0] / 100 : 1;
				// 计算移动位置，需要考虑缩放比例
				let positionX = e.detail.x + this.canvasData[index].define.transform.anchor[0] * scalarRatio;
				let positionY = e.detail.y + this.canvasData[index].define.transform.anchor[1] * scalarRatio;

				this.$set(this.canvasData[index].define.transform, 'position', [
					positionX,
					positionY,
					0
				]);


			}, 500); // 等待500毫秒后执行，如果500毫秒内再次触发则重新计时
		},

		// 针对身份栏元素的位置变化处理
		onIntroduceCardChange(e, item, index) {
			// 忽略非触摸事件
			if (e.detail.source != 'touch') return

			// 使用防抖控制更新频率，取消上一个计时器
			if (this.introduceCardDebounceTimer) {
				clearTimeout(this.introduceCardDebounceTimer);
			}

			// 创建新的计时器
			this.introduceCardDebounceTimer = setTimeout(() => {
				this.currentElementType = 'introduceCard';

				// 计算移动位置
				let positionX = e.detail.x + this.canvasData[index].define.transform.anchor[0]
				let positionY = e.detail.y + this.canvasData[index].define.transform.anchor[1]

				this.$set(this.canvasData[index].define.transform, 'position', [
					positionX,
					positionY,
					0
				]);

				// 身份栏位置变化，重新获取尺寸
				this.getContainerSize((size) => {
					if (size) {
						console.log('身份栏位置变化后，容器尺寸:', size);
					}
				});
			}, 500); // 等待500毫秒后执行，如果500毫秒内再次触发则重新计时
		},
	}
}
</script>

<style scoped lang="scss">
/* 页面主容器样式 */
.template-editor {
	background: rgb(38, 38, 38);
	/* 深色背景 */
	min-height: 100vh;
	box-sizing: border-box;
	z-index: -2;
	display: flex;
	flex-direction: column;

	/* 背景选中样式 */
	.mask-selected {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border: 4rpx dashed #41DD94;
		box-sizing: border-box;
		pointer-events: none;
	}

	/* 身份栏尺寸信息 */
	.size-info {
		margin-top: 10rpx;
		font-size: 20rpx;
		color: #ffffff;
		background-color: rgba(0, 0, 0, 0.5);
		padding: 4rpx 8rpx;
		border-radius: 6rpx;
		text-align: center;
	}

	/* 描述文本行样式 */
	.description-line {
		display: block;
		word-break: break-all;
		line-height: 1.5;
	}

	/* 身份栏编辑区域样式 */
	.identity-edit-form {
		display: flex;
		align-items: center;
		justify-content: space-around;
		color: white;
		width: 100%;
		font-size: 28rpx;
		margin-bottom: 20rpx;

		view {
			margin-right: 30rpx;
		}

		.word-count {
			font-size: 24rpx;
			color: #aaaaaa;
			margin-right: 10rpx;
		}
	}

	/* 编辑区域容器 */
	.editor-container {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;

		/* 左侧区域 */
		.editor-left-area {
			width: 20vw;
		}

		/* 中央编辑画布 */
		.editor-canvas {
			display: flex;
			justify-content: space-around;
			background: black;
			overflow: hidden;

			/* 选中元素样式 */
			.element-selected {
				position: relative;
				border: 2rpx solid white;
				box-shadow: 0 0 10rpx rgba(255, 255, 255, 0.5);
			}

			/* 删除按钮样式 */
			.element-delete-btn {
				background: white;
				border-radius: 50%;
				width: 40rpx;
				height: 40rpx;
				padding: 6rpx;
				position: absolute;
				top: -20rpx;
				right: -20rpx;
				z-index: 10;
			}
		}

		/* 右侧保存按钮区域 */
		.editor-sidebar {
			width: 20vw;
			position: relative;
			height: 100%;

			/* 保存按钮样式 */
			.save-button {
				display: flex;
				align-items: center;
				justify-content: center;
				background: rgb(65, 221, 148);
				font-size: 30rpx;
				color: #2e2e2e;
				font-weight: bold;
				text-align: center;
				padding: 10rpx 20rpx;
				position: absolute;
				top: 0;
				right: 0;
				border-top-left-radius: 40rpx;
				border-bottom-left-radius: 40rpx;
			}
		}
	}

	/* 底部面板样式 */
	.editor-panel {
		flex: 1;
		padding: 20rpx;
		box-sizing: border-box;
	}

	/* 自定义uTabs组件样式 */
	::v-deep .u-tabs__wrapper__nav__item__text {
		font-size: 22px;
		padding-bottom: 20rpx;
		// width: 90rpx;
	}

	::v-deep .u-tabs__wrapper__nav__item {
		width: 100rpx;
		padding: 0 5rpx
	}

	/* 元素选择区域样式 */
	.element-selection {
		margin-top: 20rpx;

		/* 使元素卡片横向排列 */
		::v-deep .uni-scroll-view-content {
			display: flex;
			flex-wrap: wrap;
		}
	}

	/* 底部滚动区域 */
	.element-scroll-area {
		height: 35vh;

		/* 通用元素样式 - 共性设置 */
		.common-item-style {
			display: inline-block;
			margin: 10rpx 1%;
			border-radius: 20rpx;
			position: relative;
			border: 6rpx solid rgba(0, 0, 0, 0);

			image {
				width: 100%;
				border-radius: 20rpx;
			}
		}

		/* 数字人卡片样式 */
		.virtual-human-item {
			@extend .common-item-style;
			width: 160rpx;
			height: 240rpx;

			image {
				width: 100%;
				height: 100%;
				border-radius: 20rpx;
			}

			.premium-tag {
				position: absolute;
				top: 6rpx;
				left: 6rpx;
				width: 60rpx;
				height: 30rpx;
				z-index: 1;
			}
		}

		/* 推荐模板样式 */
		.recommend-item {
			@extend .common-item-style;
			width: 23%;

			&-active {
				background: $project_1_bg;
				border: 6rpx solid transparent;
			}

			&-inactive {
				border: 6rpx solid rgba(0, 0, 0, 0);
			}
		}

		/* 标题元素样式 */
		.title-item {
			width: 150rpx;
			height: 150rpx;
			display: inline-block;
			margin: 20rpx 0 0 15rpx;
			border-radius: 20rpx;
			overflow: hidden;

			image {
				width: 100%;
				height: 100%;
			}

			&-active {
				background: $project_1_bg;
				border: 6rpx solid transparent;
			}

			.title-empty {
				height: 100%;
				display: flex;
				justify-content: space-around;
				align-items: center;

				.empty-icon {
					width: 60rpx;
					height: 60rpx;
				}
			}
		}

		/* 字幕元素样式 */
		.caption-item {
			width: 150rpx;
			height: 150rpx;
			display: inline-block;
			margin: 20rpx 0 0 15rpx;
			border-radius: 20rpx;
			overflow: hidden;

			image {
				width: 100%;
				height: 100%;
			}

			&-active {
				background: $project_1_bg;
				border: 6rpx solid transparent;
			}

			.caption-empty {
				height: 100%;
				display: flex;
				justify-content: space-around;
				align-items: center;

				.empty-icon {
					width: 60rpx;
					height: 60rpx;
				}
			}
		}

		/* 身份栏元素样式 */
		.identity-item {
			@extend .common-item-style;
			width: 23%;

			&-active {
				background: $project_1_bg;
				border: 6rpx solid transparent;
			}
		}

		/* 背景元素样式 */
		.background-item {
			width: 150rpx;
			height: 266rpx;
			border-radius: 20rpx;
			display: inline-block;
			overflow: hidden;
			margin-left: 25rpx;
			margin-top: 20rpx;

			image {
				width: 100%;
				height: 100%;
			}

			&-active {
				background: $project_1_bg;
				border: 6rpx solid transparent;
			}

			.background-empty {
				height: 100%;
				display: flex;
				justify-content: space-around;
				align-items: center;

				.empty-icon {
					width: 60rpx;
					height: 60rpx;
				}
			}
		}

		/* 未选中卡片样式 */
		.element-item-inactive {
			border: 6rpx solid rgba(0, 0, 0, 0);
		}

		/* 选中卡片样式 */
		.element-item-active {
			background: $project_1_bg;
			/* 全局主题色背景 */
			border: 6rpx solid transparent;
		}
	}

	/* 数字人分类选择区域 */
	.virtual-human-category {
		display: flex;
		width: 100%;
		height: 60rpx;
		line-height: 60rpx;

		/* 分类选项样式 */
		.virtual-human-option {
			color: white;
			font-size: 28rpx;
			background: #393939;
			border-radius: 10rpx;
			padding: 0 20rpx;
			margin-right: 20rpx;
		}
	}

	/* 添加数字人按钮 */
	.virtual-human-add {
		width: 200rpx;
		height: 200rpx;
		border-radius: 20rpx;
		margin: auto;
		display: flex;
		justify-content: space-around;
		background: #393939;
	}

	/* 元素内容容器 */
	.element-content {
		position: relative;
		transform-origin: center center;
		display: flex;
		justify-content: center;
		align-items: center;
		overflow: visible;
	}

	.identity-card-container {
		display: flex;
		flex-direction: column;
		position: absolute;
		left: 0px;
		top: 0px;
		width: fit-content;
		z-index: -999;
	}

	/* 元素包装器样式 */
	.element-wrapper {
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		overflow: visible;
	}

	/* 选中元素样式 */
	.element-selected {
		border: 2rpx solid white;
		box-shadow: 0 0 10rpx rgba(255, 255, 255, 0.5);
	}
}

/* 加载蒙版样式 */
.project_model_1 {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 99999;
	display: flex;
	justify-content: space-around;
	align-items: center;
}

.project_model_2 {
	width: 500rpx;
	height: 300rpx;
	background: white;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	border-radius: 20rpx;
}
</style>