<template>
  <view class="page">
    <view class="card">
      <image :src="url" mode="widthFix" show-menu-by-longpress></image>
    </view>
    <view class="content">{{content}}</view>
  </view>
</template>

<script>
import {getUserType} from '../../api/numberUser/userType.js'
export default {
  data() {
    return {
      url: '',  // url 声明在 data 中
      content: ''
    }
  },
  onLoad() {
    getUserType().then(res =>{
      this.url = res.data.rechargeContactImg  // 设置 url
      this.content = res.data.rechargeContactText || '长按添加售后客服~如兑换算力请联系对接人'
    })
  }
}
</script>

<style scoped lang="scss">
	.page{
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 100rpx 0;
		
		.card{
			width: 400rpx;
			display: flex;
			justify-content: center;
			
			image{
				width: 100%;
				border-radius: 12rpx;
			}
		}
		.content{
			text-align: center;
			font-size: 24rpx;
			margin-top: 40rpx;
			color: #919191;
		}
	}

</style>
