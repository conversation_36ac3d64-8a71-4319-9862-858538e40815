<template>
  <view class="empty-state" :style="{paddingTop: pad_top}">
    <view class="empty-state__box">
      <image
        v-if="type === 'music'"
        class="empty-state__image"
        mode="widthFix"
        :src="page_img_statr + 'project1_music_none.png'"
      />
      <image
        v-if="type === 'message'"
        class="empty-state__image"
        mode="widthFix"
        :src="page_img_statr + 'project1_message_none1.png'"
      />
      <image
        v-if="type === 'data'"
        class="empty-state__image"
        mode="widthFix"
        :src="page_img_statr + 'project1_message_none2.png'"
      />
      <view class="empty-state__content">{{ content }}</view>
    </view>
  </view>
</template>

<script>
import { page_img_statr } from "../../api/getData.js";

export default {
  data() {
    return {
      page_img_statr: "",
    };
  },
  props: {
	  pad_top:String,
    content: String,
    type: {
      type: String,
      validator(value) {
        return ["music", "message", "data"].includes(value);
      },
    },
  },
  created() {
    this.page_img_statr = page_img_statr;
  },
};
</script>

<style scoped lang="scss">
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%; /* 确保父容器有高度 */
}

.empty-state__box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // padding: 40rpx 0;
  text-align: center;
  width: 100%;
}

.empty-state__image {
  width: 40%;
  margin-bottom: 20rpx; /* 图片和文字间距 */
}

.empty-state__content {
  font-size: 32rpx;
  color: #999999;
}
</style>