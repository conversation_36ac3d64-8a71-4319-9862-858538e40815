import {requests} from '../../utils/request.js'
export const getPowerLog = (data) =>{
    return requests({
        url:'/user/power/log',
        method:'get',
        data:data
    })
}


export const deleteDrPerson = (id) =>{
    return requests({
        url:`/user/dr/remove/${id}`,
        method:'delete'
    })
}

export const logout = ()=>{
    return requests({
        url:'/logout',
        method:'post'
    })
}