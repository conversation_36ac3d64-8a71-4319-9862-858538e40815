<template>
	<view class="page">
		<vipVersion @show_model="show_model" :pageShow="page_show" @down_close="page_show = false"
			:fastPowerRange="fastPowerRange"></vipVersion>
		<view style="z-index: 999;" v-if="page_model">
			<InsufficientPermissions @closeModel="close"></InsufficientPermissions>
		</view>

		<view class="content_box">
			<!-- 空状态 -->
			<view class="content_none" v-if="dr_list.length<=0">
				<image src="/static/empty-state.png" class="empty-image" mode="widthFix"></image>
				<view class="text_one">您还没有数字人，快去定制一个吧</view>
				<view class="box_button" @click="addNumberUser">
					定制数字人
				</view>
			</view>
			
			<!-- 有数据状态 -->
			<view v-else>

				<view class="page_scroll">
					<!-- 添加卡片按钮 -->
					<view class="add_card" @click="addNumberUser">
						<u-icon name="plus" color="#999" size="36"></u-icon>
						<text class="add-text">新建数字人</text>
					</view>
					
					<!-- 数字人卡片列表 -->
					<view v-for="item in dr_list" :key="item.id" class="card" :class="'black_card'">
						<!-- 卡片图片 -->
						<image v-if="item.cover" :src="item.cover" class="image_" mode="aspectFill"></image>
						<image v-else src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/video_loading_bg.png" class="image_"></image>
						
						<!-- 专业版标识 -->
						<image v-if="item.type == 'pro'" mode="heightFix" src="/static/index/pro_vip.png" class="left_top_video"></image>
						
						<!-- 状态覆盖层 -->
						<view class="status-overlay" v-if="item.status !== 'success'">
							<view class="status-content">
								<u-icon v-if="item.status != 'success'" name="info-circle" color="#fff" size="24"></u-icon>
								
								<text class="status-text">
									{{ 
										item.status == 'training' ? '训练中' : 
										item.status == 'censoring' ? '审核中' :
										'训练失败' 
									}}
								</text>
								
								<view v-if="item.status == 'fail'" class="look_message" @click.stop="look_reason(item)">
									查看原因
								</view>
							</view>
						</view>
						
						<!-- 成功状态下的控制按钮 -->
						<view v-if="item.status === 'success'" class="card-controls">
							<!-- 播放按钮 - 已更新样式 -->
							<view class="play-icon" @click="down_card(item)">
								<image :src="ossPath + '/project1_video_play.png'" mode="aspectFit"></image>
							</view>
							
							<!-- 更多按钮 -->
							<view class="more-button" @click.stop="showMoreOptions(item)">
								<u-icon name="more-dot-fill" color="#fff" size="24"></u-icon>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 弹窗组件 -->
		<projectModel 
			v-if="fail_message"
			title="温馨提示"
			:content="page_reason"
			save="删除"
			@btn_close="fail_message = false"
			@btn_save="deletPerson"
		></projectModel>
		
		<projectModel
			v-if="censoring_show"
			title="温馨提示"
			content="您的数字人定制正在训练中，如有疑问请联系我们。"
			save="删除"
			:btn="false"
			@btn_close="fail_message = false"
			@btn_save="down_edit"
		>
			<view class="project_btn" @click="censoring_show = false">
				知道了
			</view>
		</projectModel>
		
		<projectModel
			v-if="training_show"
			title="温馨提示"
			content="您的数字人定制正在训练中，如有疑问请联系我们。"
			save="删除"
			:btn="false"
			@btn_close="fail_message = false"
			@btn_save="down_edit"
		>
			<view class="project_btn" @click="training_show = false">
				知道了
			</view>
		</projectModel>
		
		<!-- 更多选项弹出层 -->
		<u-popup :show="showMorePopup" mode="bottom" :round="24" @close="showMorePopup = false">
			<view class="more-options-box">
				操作
			</view>
			<view class="more-options">
				<view class="option-item delete-option" @click="confirmDelete">
					<u-icon name="trash" color="#ff4d4f" size="22"></u-icon>
					<text>删除数字人</text>
				</view>
				<view class="option-item cancel" @click="showMorePopup = false">取消</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import {page_img_statr} from '../../api/getData.js'
	import {
		getUserType,
		getVoiceList,
		getTemplateList
	} from '../../api/numberUser/userType.js'
	import {deleteDrPerson} from "@/api/user/user";
	export default {
		data() {
			return {
				page_img_statr:'',
				dr_list: '',
				currentId:'',
				page_model: false,
				training_show: false,
				censoring_show: false,
				page_setInterval: '',
				page_show: false,
				page_reason: '',
				fail_message: false,
				showMorePopup: false,
				selectedItem: null,
				ossPath: this.$appConfig.appInfo.ossPath || 'https://xiao-dr.oss-cn-beijing.aliyuncs.com/img'
			}
		},
		onLoad() {
			
		},
		onShow() {
			this.page_show = false
			this.get_drList()
		},
		onHide() {
			this.innerAudioContext.stop()
			clearInterval(this.page_setInterval)
		},
		onUnload() {
			// 页面卸载时销毁音频
			if (this.innerAudioContext) {
				this.innerAudioContext.destroy()
			}
		},
		beforeDestroy() {
			clearInterval(this.page_setInterval);
		},
		methods: {
			close(value) {
				this.page_model = value
			},
			show_model(value) {
				this.page_show = false
				this.page_model = value
			},
			addNumberUser() {
				getUserType().then(res => {
					if (res.data.enableFastClone == '0') {
						this.page_show = true
						this.fastPowerRange = res.data.fastPowerRange
					} else {
						uni.navigateTo({
							url: '/subpkg/index/numbe_user/index?type=pro'
						})
					}
				})
			},

			down_card(item) {
				console.log(item.status,'item.status');
				
				if (item.status == 'training') this.training_show = true
				if (item.status == 'censoring') this.censoring_show = true
				if (item.status == 'success') {
					console.log(item.demoUrl)
					uni.navigateTo({
						url: `/subpkg/index/get_radio/look_my_video?url=${encodeURIComponent(item.demoUrl)}&type=Human`
					})
				}
			},

			
			//获取我的数字人列表
			get_drList() {
				this.$api.get_drList({
					pageNum: 1,
					pageSize: 100
				}).then((res) => {
					this.dr_list = res.rows
					let arr = this.dr_list.filter((item) => {
						return (item.status == 'training' || item.status == 'censoring')
					})
					if (arr.length) {
						clearInterval(this.page_setInterval)
						this.page_setInterval = setInterval(() => {
							this.get_drList()
							console.log('计数:')
						}, 20000)
					} else {
						clearInterval(this.page_setInterval)
					}
				})
			},
			async deletPerson(){
				const res = await deleteDrPerson(this.currentId)
				if(res.code==200){
					uni.showToast({title:'删除成功',icon:'none'})
					this.get_drList()
					this.fail_message = false
					this.showMorePopup = false
				}
			},
			look_reason(item) {
				this.fail_message = true
				this.currentId=item.id
				this.page_reason = item.reason
			},
			down_edit() {
				uni.navigateTo({
					url: '/subpkg/index/numbe_user/index'
				})
			},
			// 显示更多选项
			showMoreOptions(item) {
				console.log(item);
				
				this.selectedItem = item
				this.currentId = item.id
				this.showMorePopup = true
				
			},
			// 确认删除
			confirmDelete() {
				this.fail_message = true
				this.showMorePopup = false
				this.page_reason = '确认要删除该数字人吗？'
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		padding: 24rpx;
		background-color: #f8f8f8;
		min-height: 100vh;
		box-sizing: border-box;
	}
	
	.content_box {
		margin-top: 20rpx;
	}
	
	/* 空状态样式 */
	.content_none {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
		
		.empty-image {
			width: 300rpx;
			height: 300rpx;
			margin-bottom: 40rpx;
			opacity: 0.8;
		}
		
		.text_one {
			margin-bottom: 40rpx;
			font-size: 28rpx;
			color: #999;
		}
		
		.box_button {
			background: #000;
			color: #fff;
			border-radius: 50rpx;
			padding: 16rpx 48rpx;
			font-size: 28rpx;
			font-weight: 500;
			transition: all 0.3s;
			
			&:active {
				opacity: 0.8;
				transform: scale(0.98);
			}
		}
	}
	
	/* 标题样式 */
	.my_text {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 30rpx 0 20rpx;
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		
		.right {
			display: flex;
			align-items: center;
			font-size: 24rpx;
			color: #666;
			font-weight: normal;
			
			text {
				margin-right: 8rpx;
			}
		}
	}
	
	/* 卡片列表 */
	.page_scroll {
		display: flex;
		flex-wrap: wrap;
		gap: 24rpx;
		padding: 10rpx 0;
	}
	
	/* 卡片基础样式 */
	.card, .add_card {
		width: 31%;
		height: 302rpx;
		border-radius: 16rpx;
		overflow: hidden;
		position: relative;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
		transition: transform 0.2s;
		
		&:active {
			transform: scale(0.98);
		}
	}
	
	/* 添加卡片样式 */
	.add_card {
		background: #f5f5f5;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		border: 1px dashed #ddd;
		
		.add-text {
			font-size: 24rpx;
			color: #999;
			margin-top: 16rpx;
		}
	}
	
	/* 图片样式 */
	.image_ {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	
	/* VIP标识 */
	.left_top_video {
		height: 32rpx;
		position: absolute;
		top: 12rpx;
		left: 12rpx;
		z-index: 2;
	}
	
	/* 状态覆盖层 */
	.status-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.6);
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.status-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		color: #fff;
		padding: 20rpx;
		text-align: center;
	}
	
	.status-text {
		font-size: 24rpx;
		margin-top: 16rpx;
	}
	
	/* 查看原因按钮 */
	.look_message {
		background: rgba(255, 255, 255, 0.2);
		border-radius: 20rpx;
		border: 1rpx solid #fff;
		padding: 8rpx 20rpx;
		font-size: 22rpx;
		margin-top: 16rpx;
	}
	
	/* 弹窗按钮 */
	.project_btn {
		background: #000;
		color: #fff;
		border-radius: 50rpx;
		padding: 16rpx 0;
		text-align: center;
		margin-top: 30rpx;
		width: 200rpx;
	}
	
	/* 卡片控制按钮 */
	.card-controls {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		z-index: 2;
		
		/* 播放按钮样式 - 从tutorial.vue移植过来的样式 */
		.play-icon {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			border-radius: 50%;
			padding: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100rpx;
			height: 100rpx;

			image {
				width: 60rpx;
				height: 60rpx;
			}
			
			&:active {
				transform: translate(-50%, -50%) scale(0.95);
				opacity: 1;
			}
		}
		
		/* 更多按钮 */
		.more-button {
			position: absolute;
			bottom: 10rpx;
			right: 10rpx;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			opacity: 0.8;
			transition: all 0.2s;
			
			&:active {
				opacity: 1;
			}
		}
	}

	.more-options-box{
		font-size: 36rpx;
		font-weight: 500;
		color: #333;
		padding: 40rpx 0 20rpx 0;
		width: 100%;
		text-align: center;
		border-radius: 16rpx 16rpx 0 0;
	}
	
	/* 更多选项弹出层样式 */
	.more-options {
		padding: 30rpx;
		
		.option-item {
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 28rpx 0;
			font-size: 30rpx;
			border-radius: 16rpx;
			margin-bottom: 20rpx;
			
			text {
				margin-left: 12rpx;
			}
		}
		
		.delete-option {
			color: #ff4d4f;
			background: #fff5f5;
		}
		
		.cancel {
			background: #f5f5f5;
			color: #333;
		}
	}
</style>