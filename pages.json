{
  "easycom": {
    "^u-(.*)": "@/uni_modules/uview-ui/components/u-$1/u-$1.vue"
  },
  "pages": [
    //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/home_two/index",
      "style": {
        "navigationBarTitleText": "动态",
        "navigationStyle": "default",
        "onReachBottomDistance": 50,
        "navigationBarBackgroundColor": "#F3F5F8"
      }
    },
    {
      "path": "pages/home_three/index",
      "style": {
        "navigationBarTitleText": "频道",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/home_four/index",
      "style": {
        "navigationBarTitleText": "我的",
        "navigationStyle": "custom"
      }
    }
  ],
  "subPackages": [

  ],
  "tabBar": {
    "backgroundColor": "black",
    "color": "black",
    "selectedColor": "red",
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": ""
      },
      {
        "pagePath": "pages/home_two/index",
        "text": ""
      },
      {
        "pagePath": "pages/home_three/index",
        "text": ""
      },
      {
        "pagePath": "pages/home_four/index",
        "text": ""
      }
    ]
  },
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "uni-app",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  },
  "uniIdRouter": {}
}