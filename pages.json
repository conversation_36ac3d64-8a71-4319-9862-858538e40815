{
  "easycom": {
    "^u-(.*)": "@/uni_modules/uview-ui/components/u-$1/u-$1.vue"
  },
  "pages": [
    //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/home_two/index",
      "style": {
        "navigationBarTitleText": "动态",
        "navigationStyle": "default",
        "onReachBottomDistance": 50,
        "navigationBarBackgroundColor": "#F3F5F8"
      }
    },
    {
      "path": "pages/home_three/index",
      "style": {
        "navigationBarTitleText": "频道",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/home_four/index",
      "style": {
        "navigationBarTitleText": "我的",
        "navigationStyle": "custom"
      }
    }
  ],
  "subPackages": [
    {
      "root": "subpkg",
      "pages": [
        {
          // subpkg/index/index
          "path": "index/page_copy",
          "style": {
            "navigationBarTitleText": "DeepSeek口播文案",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/index/extractionCopy
          "path": "index/extractionCopy",
          "style": {
            "navigationBarTitleText": "视频文案提取",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/index/extractionResults
          "path": "index/extractionResults",
          "style": {
            "navigationBarTitleText": "文案生成",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/index/results
          "path": "index/results",
          "style": {
            "navigationBarTitleText": "生成结果",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/index/get_radio/index
          "path": "index/get_radio/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          // subpkg/index/get_radio/choose_music
          "path": "index/get_radio/choose_music",
          "style": {
            "navigationBarTitleText": "选择音乐",
            "navigationStyle": "default",
            "navigationBarBackgroundColor": "#F3F5F8"
          }
        },
        {
          // subpkg/index/get_radio/add_stencil
          "path": "index/get_radio/add_stencil",
          "style": {
            "navigationBarTitleText": "新增视频模版",
            "navigationStyle": "default",
            "navigationBarBackgroundColor": "#000000",
            "navigationBarTextStyle": "white"
          }
        },
        {
          // subpkg/index/get_radio/listMessage
          "path": "index/get_radio/listMessage",
          "style": {
            "navigationBarTitleText": "选择频道文案",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/index/get_radio/my_video
          "path": "index/get_radio/my_video",
          "style": {
            "navigationBarTitleText": "我的作品",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/index/get_radio/look_my_video
          "path": "index/get_radio/look_my_video",
          "style": {
            "navigationBarTitleText": "查看作品",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/index/get_radio/example
          "path": "index/get_radio/example",
          "style": {
            "navigationBarTitleText": "案例样片",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/index/get_radio/selectMaterial
          "path": "index/get_radio/selectMaterial",
          "style": {
            "navigationBarTitleText": "选择素材",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/index/edit_text/index
          "path": "index/edit_text/index",
          "style": {
            "navigationBarTitleText": "输入文案",
            "navigationStyle": "custom"
          }
        },
        {
          // subpkg/index/numbe_user/index
          "path": "index/numbe_user/index",
          "style": {
            "navigationBarTitleText": "定制数字人",
            "navigationStyle": "custom"
          }
        },
        {
          // subpkg/index/numbe_user/waitReciew
          "path": "index/numbe_user/waitReciew",
          "style": {
            "navigationBarTitleText": "等待审核",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/index/numbe_user/genVideoResult
          "path": "index/numbe_user/genVideoResult",
          "style": {
            "navigationBarTitleText": "生成结果",
            "navigationStyle": "custom"
          }
        },
        {
          // subpkg/index/tutorial/index
          "path": "index/tutorial/index",
          "style": {
            "navigationBarTitleText": "数字人教程",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/index/record_video/index
          "path": "index/record_video/index",
          "style": {
            "navigationBarTitleText": "裁剪",
            "navigationStyle": "default",
            "navigationBarBackgroundColor": "#000000",
            "navigationBarTextStyle": "white"
          }
        },
        {
          // subpkg/index/save_video/index
          "path": "index/save_video/index",
          "style": {
            "navigationBarTitleText": "视频训练确认",
            "navigationStyle": "default",
            "navigationBarBackgroundColor": "#000000",
            "navigationBarTextStyle": "white"
          }
        },
        {
          // subpkg/index/authorization_video/index
          "path": "index/authorization_video/index",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "default",
            "navigationBarBackgroundColor": "#000000",
            "navigationBarTextStyle": "white"
          }
        },
        {
          // subpkg/index/authorization_video/save
          "path": "index/authorization_video/save",
          "style": {
            "navigationBarTitleText": "授权视频确认",
            "navigationStyle": "default",
            "navigationBarBackgroundColor": "#000000",
            "navigationBarTextStyle": "white"
          }
        },
        {
          // subpkg/index/get_sound/index
          "path": "index/get_sound/index",
          "style": {
            "navigationBarTitleText": "克隆声音",
            "navigationStyle": "custom",
            "navigationBarBackgroundColor": "#000000",
            "navigationBarTextStyle": "white"
          }
        },
        {
          // subpkg/index/get_sound/save_sound
          "path": "index/get_sound/save_sound",
          "style": {
            "navigationBarTitleText": "克隆中",
            "navigationStyle": "custom",
            "navigationBarBackgroundColor": "#000000",
            "navigationBarTextStyle": "white"
          }
        },
        {
          // subpkg/myMaterials/index
          "path": "myMaterials/index",
          "style": {
            "navigationBarTitleText": "我的素材",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "subpkg",
      "pages": [
        {
          // subpkg/home_two/message
          "path": "home_two/message",
          "style": {
            "navigationBarTitleText": "内容详情",
            "navigationStyle": "default",
            "onReachBottomDistance": 50
          }
        },
        {
          // subpkg/batchVideoHome
          "path": "batchVideoHome/index",
          "style": {
            "navigationBarTitleText": "批量视频",
            "navigationStyle": "custom"
          }
        },
        {
          // subpkg/batchVideoBegin
          "path": "batchVideoDetail/index",
          "style": {
            "navigationBarTitleText": "批量视频",
            "navigationStyle": "custom"
          }
        },
        {
          // subpkg/batchVideoConfig
          "path": "batchVideoConfig/index",
          "style": {
            "navigationBarTitleText": "批量视频",
            "navigationStyle": "custom"
          }
        },
        {
          // subpkg/myBatchVideo
          "path": "myBatchVideo/index",
          "style": {
            "navigationBarTitleText": "我的作品",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "tailor/index",
          "style": {
            "navigationBarTitleText": "裁剪",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "subpkg",
      "pages": [
        {
          // subpkg/home_three/message
          "path": "home_three/message",
          "style": {
            "navigationBarTitleText": "频道详情",
            "navigationStyle": "custom"
          }
        },
        {
          // subpkg/home_three/messageContent
          "path": "home_three/messageContent",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationStyle": "default"
          }
        }
      ]
    },
    {
      "root": "subpkg",
      "pages": [
        {
          // subpkg/home_four/exchange
          "path": "home_four/exchange",
          "style": {
            "navigationBarTitleText": "兑换中心",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/home_four/phoneMe
          "path": "home_four/phoneMe",
          "style": {
            "navigationBarTitleText": "联系我们",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/home_four/dr_list
          "path": "home_four/dr_list",
          "style": {
            "navigationBarTitleText": "我的数字人",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/home_four/music_list
          "path": "home_four/music_list",
          "style": {
            "navigationBarTitleText": "音频列表",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/home_four/tutorial
          "path": "home_four/tutorial",
          "style": {
            "navigationBarTitleText": "新手教程",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/home_four/template_list
          "path": "home_four/template_list",
          "style": {
            "navigationBarTitleText": "模版列表",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/home_four/myPower
          "path": "home_four/myPower",
          "style": {
            "navigationBarTitleText": "我的算力"
          }
        },
        {
          // subpkg/home_four/about
          "path": "home_four/about",
          "style": {
            "navigationBarTitleText": "关于",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/home_four/agreement
          "path": "home_four/agreement",
          "style": {
            "navigationBarTitleText": "协议",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/home_four/power_rule
          "path": "home_four/power_rule",
          "style": {
            "navigationBarTitleText": "算力规则",
            "navigationStyle": "default"
          }
        },
        {
          // subpkg/home_four/power_rule
          "path": "test/index",
          "style": {
            "navigationBarTitleText": "算力规则",
            "navigationStyle": "default"
          }
        }
      ]
    },
    {
      "root": "devTools",
      "pages": [
        {
          // subpkg/home_four/power_rule
          "path": "page/index",
          "style": {
            "navigationBarTitleText": "调试",
            "navigationStyle": "custom"
          }
        }
      ]
    }
  ],
  "tabBar": {
    "backgroundColor": "black",
    "color": "black",
    "selectedColor": "red",
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": ""
      },
      {
        "pagePath": "pages/home_two/index",
        "text": ""
      },
      {
        "pagePath": "pages/home_three/index",
        "text": ""
      },
      {
        "pagePath": "pages/home_four/index",
        "text": ""
      }
    ]
  },
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "uni-app",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  },
  "uniIdRouter": {}
}