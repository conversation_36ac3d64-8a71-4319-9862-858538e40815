<script>
import { mapActions, mapGetters } from 'vuex'
// 创建 WebSocket 连接
//todo,关闭默认token
//uni.setStorageSync('userToken',"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxNTI0MzkwOTcwNCIsImxvZ2luX3VzZXJfa2V5IjoiOWNhYTY4YzItZGE5OC00OTMyLWI0YmQtOTdlMzMwNTJjOTcwIn0.xS4EoXqfVBDcgvvZcnNv5sSBVRnsnUkeo0e7QhR8YmAc-qav4Hj_xbw6qkkk1OP1M57XvNLYItlmHl53DxbgTA")

export default {
  data() {
    return {
    }
  },
  computed: {
    ...mapGetters('user', ['isLogin', 'token'])
  },
  methods: {
    ...mapActions('user', ['getUserInfo', 'closeWebSocket'])
  },
  onLaunch: async function () {
    // 判断用户是否登录
    if (this.isLogin && this.token) {
      try {
        // 获取用户信息（成功后会自动初始化WebSocket）
        await this.getUserInfo();
        console.log('用户信息获取成功');
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    } else {
      console.log('用户未登录');
    }
  },
  onShow: function () {
  },
  onHide: function () {
    console.log('App Hide');
  },
  onUnload() {
    this.closeWebSocket();
  }
}
</script>


<style lang="scss">
.pro1_text_color {
  color: $project_1_text_color;
}

.ellipsis {
  white-space: nowrap;
  /* 强制文字不换行 */
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  display: block;
  /* 如果是行内元素需要转为块级元素 */
}

.ellipsis2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}


//.project_btn {
//  width: 90%;
//  // background: #000000;
//  color: #FFFFFF;
//  text-align: center;
//  padding: 20rpx 0;
//  margin: auto;
//  border-radius: 20rpx;
//  background: $project_1_bg;
//}



.project_btn {
  width: 90%;
  // background: #000000;
  color: #FFFFFF;
  text-align: center;
  padding: 20rpx 0;
  margin: auto;
  border-radius: 20rpx;
  background: black;
}

.project_model_1 {
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
}

.project_model_2 {
  width: 85vw;
  padding: 40rpx 20rpx;
  background: white;
  border-radius: 20rpx;
  z-index: 999;
  position: fixed;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
}


// ::v-deep * {
// 	box-sizing: border-box;
// }


*::before,
*::after {
  box-sizing: border-box;
  font-size: 28rpx;
}

image {
  width: 60rpx;
  height: 60rpx;
}

@import "@/uni_modules/uview-ui/index.scss";
/*每个页面公共css */
</style>
<style lang="scss" platform="mp-weixin">
page,
view,
scroll-view,
swiper,
swiper-item,
image,
navigator,
text,
button,
input,
textarea {
  box-sizing: border-box;
}
</style>
