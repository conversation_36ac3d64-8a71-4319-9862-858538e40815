<template>
  <view v-if="show" class="loading-wrapper" :style="{ background: background }">
    <view class="loading-container">
      <view class="loader" :style="{ width: size, height: size }">
        <view class="inner one" :style="{ borderBottomColor: color }"></view>
        <view class="inner two" :style="{ borderRightColor: color }"></view>
        <view class="inner three" :style="{ borderTopColor: color }"></view>
      </view>
      <view
          v-if="text"
          class="loading-text"
          :style="{
          color: textColor,
          fontSize: textSize,
          marginTop: textMarginTop
        }"
      >
        {{ text }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CustomLoading',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: '64px'
    },
    // 加载动画颜色
    color: {
      type: String,
      default: '#EFEFFA'
    },
    //遮罩背景色（如需遮罩）:background="'rgba(0,0,0,0.3)'"
    background: {
      type: String,
      default: 'transparent'
    },
    // 加载动画文字,不传默认不显示
    text: {
      type: String,
      default: ''
    },
    textColor: {
      type: String,
      default: 'black'
    },
    textSize: {
      type: String,
      default: '14px'
    },
    textMarginTop: {
      type: String,
      default: '12px'
    }
  }
}
</script>

<style scoped>
.loading-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loader {
  border-radius: 50%;
  perspective: 800px;
  position: relative;
}

.inner {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.inner.one {
  animation: rotate-one 1s linear infinite;
  border-bottom: 4px solid;
}

.inner.two {
  animation: rotate-two 1s linear infinite;
  border-right: 4px solid;
}

.inner.three {
  animation: rotate-three 1s linear infinite;
  border-top: 4px solid;
}

.loading-text {
  text-align: center;
}

@keyframes rotate-one {
  0% {
    transform: rotateX(35deg) rotateY(-45deg) rotateZ(0deg);
  }
  100% {
    transform: rotateX(35deg) rotateY(-45deg) rotateZ(360deg);
  }
}

@keyframes rotate-two {
  0% {
    transform: rotateX(50deg) rotateY(10deg) rotateZ(0deg);
  }
  100% {
    transform: rotateX(50deg) rotateY(10deg) rotateZ(360deg);
  }
}

@keyframes rotate-three {
  0% {
    transform: rotateX(35deg) rotateY(55deg) rotateZ(0deg);
  }
  100% {
    transform: rotateX(35deg) rotateY(55deg) rotateZ(360deg);
  }
}
</style>
