<template>
  <view class="works-page">
    <custom-navbar title="我的作品" :isBack="true" :isBlack="true" backgroundColor="#f3f5f8"></custom-navbar>
    <view class="works-page-content" :style="{ paddingBottom: workPageContentBottom + 'px' }">
      <!-- 作品信息区域 -->
      <view class="works-info">
        <text class="works-count">共计{{ workInfo.length }}个视频</text>
        <text class="works-date" v-if="workInfo.length > 0">{{ workInfo[0].createTime }}</text>
      </view>

      <!-- 加载状态 -->
      <view class="loading-container" v-if="loading">
        <view class="video-grid-skeleton">
          <view class="video-card-skeleton" v-for="i in 4" :key="'skeleton-' + i">
            <!-- 视频卡片骨架屏 -->
            <view class="video-overlay-skeleton">
              <!-- 头部骨架屏 -->
              <view class="video-header-skeleton">
                <view class="duration-tag-skeleton">
                  <view class="skeleton-animation"></view>
                </view>
                <view class="play-button-skeleton">
                  <view class="skeleton-animation"></view>
                </view>
              </view>

              <!-- 大播放按钮骨架屏 -->
              <view class="play-large-icon-skeleton">
                <view class="skeleton-animation"></view>
              </view>

              <!-- 视频信息骨架屏 -->
              <view class="video-info-skeleton">
                <view class="video-title-skeleton">
                  <view class="skeleton-animation"></view>
                </view>
                <view class="video-desc-skeleton">
                  <view class="skeleton-animation"></view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-container" v-else-if="workInfo.length === 0">
        <u-empty mode="data" text="暂无视频数据"></u-empty>
      </view>

      <!-- 视频列表 -->
      <view class="video-list" v-else>
        <view class="video-grid">
          <view class="video-card" v-for="(video, index) in workInfo" :key="video.id"
            @click.stop="handleVideoClick(video, index)">
            <view class="video-overlay" :style="{ backgroundImage: `url(${video.thumbnailUrl})` }"
              v-if="video.status === 'success'">
              <view class="video-header">
                <view class="duration-tag">
                  <text class="duration-text">{{ video.formattedDuration }}</text>
                </view>
                <!-- <view class="play-button" v-if="video.status === 'success' && !isSelectionMode">
                  <image class="play-icon" src="/static/goxuanzhong.png" />
                </view> -->
                <view class="select-button" v-if="isSelectionMode" :class="{ 'selected': video.selected }">
                  <image v-if="video.selected" class="selected-icon" src="/static/goxuanzhong.png" />
                </view>
                <view class="select-button" v-else-if="video.status !== 'success'"></view>
              </view>

              <image class="play-large-icon" src="/static/播放按钮.png"
                v-if="video.status === 'success' && !isSelectionMode" />

              <!-- 生成中状态显示 -->
              <view class="generating-overlay" v-if="video.status === 'generating'">
                <image class="loading-gif" src="/static/loading.gif"></image>
                <text class="generating-text">生成中...</text>
              </view>

              <view class="video-info">
                <text class="video-title">{{ video.title }}</text>
                <text class="video-desc">{{ truncateText(video.videoText, 20) }}</text>
              </view>
            </view>
            <view v-else class="video-fail"
              :style="{ backgroundImage: 'url(https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/video_loading_bg.png)' }">
              <view class="video-fail-mask">
                <image class="video-fail-icon" src="/static/注意.png"></image>
                <text class="video-fail-text">生成失败</text>
                <!-- 添加查看按钮 -->
                <view class="view-error-btn" @click.stop="showErrorDetails(video)">
                  <text class="view-error-text">查看原因</text>
                </view>
              </view>
              <view class="video-info">
                <text class="video-title">{{ video.title }}</text>
                <text class="video-desc">{{ truncateText(video.videoText, 20) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 底部操作按钮 -->
    <view class="action-buttons" :style="{ paddingBottom: safeAreaBottom + 'px' }">
      <view class="regenerate-btn" @click="goBatchVideoConfig">
        <text class="btn-text">再次生成</text>
      </view>
      <view class="download-btn" @click="toggleSelectionMode">
        <text class="btn-text" v-if="!isSelectionMode">批量下载</text>
        <text class="btn-text" v-else>{{ selectedCount > 0 ? `下载(${selectedCount})` : '取消' }}</text>
      </view>
    </view>

    <!-- 批量下载组件 -->
    <batch-download ref="batchDownload"></batch-download>

    <!-- 弹出模态框 -->
    <projectModel v-if="errorDetailsVisible" title="失败原因" :content="currentErrorVideo.message" :btn="true"
      @btn_close="closeErrorDetails" @btn_save="closeErrorDetails">
      <!-- <view class="action-button dialog-button" @click="errorDetailsVisible = false" >
        知道了
      </view> -->
    </projectModel>
  </view>
</template>

<script>
import { postBatchVideoDetail } from '@/api/batchVideoApi'
import batchDownload from '@/components/batchDownload/index.vue'

export default {
  components: {
    batchDownload
  },
  data() {
    return {
      safeAreaBottom: 0,
      workId: '',
      workInfo: [],
      loading: false,
      workPageContentBottom: 0,
      isSelectionMode: false, // 是否处于选择模式
      selectedCount: 0, // 已选中的视频数量
      errorDetailsVisible: false, // 失败详情弹窗是否显示
      currentErrorVideo: null, // 当前查看的失败视频
    };
  },
  onLoad(options) {
    this.safeAreaBottom = uni.getSystemInfoSync().safeAreaInsets.bottom + uni.upx2px(12)
    this.workPageContentBottom = this.safeAreaBottom + uni.upx2px(120)
    this.workId = options.workId
    this.getBatchVideoDetail()
  },
  methods: {
    goBatchVideoConfig() {
      uni.navigateTo({
        url: '/subpkg/batchVideoConfig/index'
      })
    },

    // 关闭失败详情
    closeErrorDetails() {
      console.log('assdsad');
      
      this.errorDetailsVisible = false;
      this.currentErrorVideo = null;
    },

    // 获取批量视频详情
    async getBatchVideoDetail() {
      try {
        this.loading = true;
        const res = await postBatchVideoDetail(this.workId);

        if (res.code === 200 && res.data) {
          this.workInfo = res.data.map(item => ({
            ...item,
            duration: item.duration || 0, // 时长(秒)
            formattedDuration: this.formatDuration(item.duration || 0),
            selected: false // 添加选择状态
          }));
        }
      } catch (error) {
        console.error('获取视频详情失败:', error);
        uni.showToast({
          title: '获取视频详情失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 时长转换 (秒 -> 时:分:秒)
    formatDuration(seconds) {
      if (!seconds || seconds === 0) return '00:00:00';

      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = Math.floor(seconds % 60);

      if (hours > 0) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
      } else {
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
      }
    },

    // 处理视频点击
    handleVideoClick(video, index) {
      if (this.isSelectionMode) {
        // 选择模式下，切换选择状态
        this.toggleVideoSelection(index);
      } else {
        // 非选择模式下，播放视频
        this.playVideo(video);
      }
    },

    // 切换视频选择状态
    toggleVideoSelection(index) {
      if (this.workInfo[index].status === 'fail') {
        uni.showToast({
          title: '视频生成失败，无法下载',
          icon: 'none'
        });
        return;
      }

      if (this.workInfo[index].status === 'generating') {
        uni.showToast({
          title: '视频生成中，无法下载',
          icon: 'none'
        });
        return;
      }

      // 切换选择状态
      this.$set(this.workInfo[index], 'selected', !this.workInfo[index].selected);

      // 更新选中数量
      this.selectedCount = this.workInfo.filter(item => item.selected).length;

      // 如果取消全部选择，自动退出选择模式
      if (this.selectedCount === 0 && this.isSelectionMode) {
        //this.isSelectionMode = false;
      }
    },

    // 切换选择模式
    toggleSelectionMode() {
      if (this.isSelectionMode) {
        // 如果已经在选择模式，且有选中项，则开始下载
        if (this.selectedCount > 0) {
          this.startBatchDownload();
        } else {
          // 无选中项，退出选择模式
          this.isSelectionMode = false;
        }
      } else {
        // 进入选择模式
        this.isSelectionMode = true;
        this.selectedCount = 0;

        // 重置所有选择状态
        this.workInfo.forEach((item, idx) => {
          this.$set(this.workInfo[idx], 'selected', false);
        });
      }
    },

    // 开始批量下载
    startBatchDownload() {
      // 获取选中的视频URL和文件名
      const selectedVideos = this.workInfo.filter(item => item.selected && item.status === 'success');

      if (selectedVideos.length === 0) {
        uni.showToast({
          title: '请选择要下载的视频',
          icon: 'none'
        });
        return;
      }

      const videoUrls = selectedVideos.map(item => item.fileUrl);
      const videoTypes = selectedVideos.map(() => 'video');
      const videoNames = selectedVideos.map(item => item.title || '视频');

      // 调用批量下载组件的方法
      this.$refs.batchDownload.addDownLoadUrl(videoUrls, videoTypes, videoNames).startDownload();

      // 退出选择模式
      this.isSelectionMode = false;
      this.selectedCount = 0;

      // 重置所有选择状态
      this.workInfo.forEach((item, idx) => {
        this.$set(this.workInfo[idx], 'selected', false);
      });
    },

    // 播放视频
    playVideo(item) {
      console.log(item.status, 'item');

      if (item.status !== 'success') {
        uni.showToast({
          title: '视频还未生成完成',
          icon: 'none'
        });
        return;
      }

      if (!item.fileUrl) {
        uni.showToast({
          title: '视频地址不存在',
          icon: 'none'
        });
        return;
      }

      // 跳转到视频播放页面
      uni.navigateTo({
        url: `/subpkg/index/get_radio/look_my_video?url=${encodeURIComponent(item.fileUrl)}`
      });
    },

    // 文本截断方法
    truncateText(text, length = 20) {
      if (!text) return '';
      if (text.length <= length) return text;
      return text.substring(0, length) + '...';
    },

    // 显示错误详情
    showErrorDetails(video) {
      console.log('asdasdas');

      this.currentErrorVideo = video;
      this.errorDetailsVisible = true;
    },

    // 关闭错误详情
    closeErrorDetails() {
      this.errorDetailsVisible = false;
      this.currentErrorVideo = null;
    },

    // 重新生成
    retryGenerate() {
      this.errorDetailsVisible = false;
      // 跳转到批量生成页面
      uni.navigateTo({
        url: '/subpkg/batchVideoConfig/index?from=error'
      });
    }
  }
};
</script>

<style lang="scss">
.works-page {
  background-color: #f3f5f8;
  width: 100%;
  min-height: 100vh;

  .works-page-content {
    padding: 30rpx 32rpx 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    // 作品信息
    .works-info {
      margin-top: 32rpx;

      .works-count {
        display: block;
        font-size: 32rpx;
        color: #333333;
        font-weight: 600;
        line-height: 48rpx;
      }

      .works-date {
        display: block;
        font-size: 24rpx;
        color: #999999;
        font-weight: 500;
        line-height: 32rpx;
      }
    }

    // 加载状态
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 80rpx 0;

      // 骨架屏动画
      @keyframes skeleton-loading {
        0% {
          background-position: -200% 0;
        }

        100% {
          background-position: 200% 0;
        }
      }

      .skeleton-animation {
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
        background-size: 400% 100%;
        animation: skeleton-loading 1.4s ease infinite;
        border-radius: 4rpx;
      }

      .video-grid-skeleton {
        display: grid;
        grid-template-columns: repeat(2, 332rpx);
        gap: 24rpx;
        justify-content: space-between;
        width: 100%;
      }

      .video-card-skeleton {
        width: 332rpx;
        height: 432rpx;
        border-radius: 16rpx;
        background-color: #f5f5f5;
        overflow: hidden;

        .video-overlay-skeleton {
          position: relative;
          width: 100%;
          height: 100%;
          padding: 16rpx;
          box-sizing: border-box;
          display: flex;
          flex-direction: column;
        }

        .video-header-skeleton {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          height: 56rpx;
          margin-bottom: 16rpx;
        }

        .duration-tag-skeleton {
          width: 154rpx;
          height: 56rpx;
          border-radius: 8rpx;
        }

        .play-button-skeleton {
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
        }

        .play-large-icon-skeleton {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 64rpx;
          height: 64rpx;
          border-radius: 50%;
        }

        .video-info-skeleton {
          margin-top: auto;

          .video-title-skeleton {
            width: 250rpx;
            height: 32rpx;
            margin-bottom: 8rpx;
            border-radius: 4rpx;
          }

          .video-desc-skeleton {
            width: 200rpx;
            height: 32rpx;
            border-radius: 4rpx;
          }
        }
      }
    }

    // 空状态
    .empty-container {
      padding: 80rpx 0;
    }

    // 视频列表
    .video-list {
      margin-top: 24rpx;

      .video-grid {
        display: grid;
        grid-template-columns: repeat(2, 332rpx);
        gap: 24rpx;
        justify-content: space-between;
        width: 100%;
      }

      .video-card {
        width: 332rpx;
        height: 432rpx;
        border-radius: 16rpx;
        background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG5f9342fc1a18893ab6bd5471a220dd2a.png) no-repeat;
        background-size: cover;
        overflow: hidden;

        &.video-card-right {
          background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG1a99e040172baa3d2a297dc694eceba9.png) no-repeat;
          background-size: cover;
        }

        .video-overlay {
          position: relative;
          width: 100%;
          height: 100%;
          background-position: center;
          background-size: cover;
          background-repeat: no-repeat;
          // background-color: rgba(0, 0, 0, 0.3);
          border-radius: 16rpx;
          padding: 16rpx;
          box-sizing: border-box;
          display: flex;
          flex-direction: column;
        }

        .video-fail {
          width: 100%;
          height: 100%;
          background-size: cover;
          background-repeat: no-repeat;
          position: relative;

          .video-fail-mask {
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            position: absolute;
            top: 0;
            left: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;


            .video-fail-icon {
              width: 64rpx;
              height: 64rpx;
            }

            .video-fail-text {
              font-weight: 600;
              font-size: 32rpx;
              color: #FFFFFF;
              margin-top: 10rpx;
              margin-bottom: 10rpx;
            }

            .view-error-btn {
              width: 160rpx;
              height: 60rpx;
              background-color: rgba(255, 255, 255, 0.2);
              border: 1rpx solid rgba(255, 255, 255, 0.5);
              border-radius: 30rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-bottom: 20rpx;

              .view-error-text {
                color: #FFFFFF;
                font-size: 28rpx;
                font-weight: 500;
              }
            }
          }

          .video-info {
            position: absolute;
            bottom: 0;
            left: 0;
            padding: 16rpx;
            // width: 100%;
            // height: 100%;
            // background-color: rgba(0, 0, 0, 0.5);
            // border-radius: 16rpx;
          }
        }

        .video-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          height: 56rpx;
        }

        .duration-tag {
          background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG37130bbbc37ff0541c6dec6057ffe5e2.png) no-repeat;
          background-size: 100% 100%;
          width: 154rpx;
          height: 56rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          .duration-text {
            color: #ffffff;
            font-size: 24rpx;
            font-weight: 500;
            text-align: center;
          }
        }

        .play-button {
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          background-image: linear-gradient(135deg, #07e3d2 0%, #01f770 100%);
          display: flex;
          align-items: center;
          justify-content: center;

          .play-icon {
            width: 24rpx;
            height: 24rpx;
          }
        }

        .select-button {
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          background-color: rgba(255, 255, 255, 0.3);
          border: 1px solid #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;

          &.selected {
            background-image: linear-gradient(135deg, #07e3d2 0%, #01f770 100%);
            border: none;
          }

          .selected-icon {
            width: 24rpx;
            height: 24rpx;
          }
        }

        .play-large-icon {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 64rpx;
          height: 64rpx;
          display: block;
        }

        .video-info {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          margin-top: auto;

          .video-title {
            color: #ffffff;
            font-size: 24rpx;
            font-weight: 500;
            // line-height: 32rpx;
            margin-bottom: 8rpx;
          }

          .video-desc {
            color: rgba(255, 255, 255, 0.7);
            font-size: 24rpx;
            font-weight: 500;
            line-height: 32rpx;
          }
        }

        .generating-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 16rpx;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          .loading-gif {
            width: 48rpx;
            height: 48rpx;
          }

          .generating-text {
            color: #ffffff;
            font-size: 24rpx;
            font-weight: 500;
            margin-top: 16rpx;
          }
        }
      }
    }
  }

  .action-button {
    color: white;
    background: linear-gradient(135deg, #22232C 0%, #0F0F0F 100%);
    border-radius: 16rpx;
    padding: 14rpx 30rpx;
    font-size: 28rpx;
    display: inline-block;
    height: 64rpx;
    line-height: 36rpx;
    text-align: center;

    &:active {
      opacity: 0.9;
    }
  }

  .dialog-button {
    margin: auto;
    display: block;
    width: 200rpx;
  }

  // 底部按钮区域
  .action-buttons {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    background-color: #ffffff;
    border-radius: 32rpx 32rpx 0 0;
    padding: 24rpx 48rpx;

    // 再次生成按钮
    .regenerate-btn {
      width: 290rpx;
      height: 96rpx;
      background-image: linear-gradient(135deg, #222328 19.837089%, #0f0f0f 100%);
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .btn-text {
        color: #ffffff;
        font-size: 36rpx;
        font-weight: 500;
      }
    }

    // 批量下载按钮
    .download-btn {
      width: 312rpx;
      height: 96rpx;
      background-image: linear-gradient(135deg, #07e3d2 0%, #01f770 100%);
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .btn-text {
        color: #333333;
        font-size: 36rpx;
        font-weight: 500;
      }
    }
  }

  // 失败详情弹窗
  .error-details-popup {
    background-color: #ffffff;
    border-radius: 32rpx;
    padding: 40rpx;

    .popup-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 40rpx;

      .error-icon {
        width: 80rpx;
        height: 80rpx;
        margin-bottom: 16rpx;
      }

      .popup-title {
        font-size: 40rpx;
        font-weight: bold;
        color: #ff4d4f;
      }
    }

    .error-content {
      background-color: #f8f9fa;
      padding: 30rpx;
      border-radius: 16rpx;
      margin-bottom: 40rpx;
      width: 602rpx;
      box-sizing: border-box;

      .error-title {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 12rpx;
        display: block;
      }

      .error-message,
      .error-time {
        padding: 20rpx;
        background-color: #fff;
        border-radius: 8rpx;
        margin-bottom: 24rpx;

        text {
          font-size: 28rpx;
          color: #333;
          line-height: 40rpx;
        }
      }

      .error-time {
        margin-bottom: 0;
      }
    }

    .button-container {
      display: flex;
      gap: 20rpx;

      .retry-btn,
      .close-btn {
        flex: 1;
        height: 88rpx;
        border-radius: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        text {
          font-size: 32rpx;
          font-weight: 500;
        }
      }

      .retry-btn {
        background-image: linear-gradient(135deg, #222328 19.837089%, #0f0f0f 100%);

        text {
          color: #ffffff;
        }
      }

      .close-btn {
        background-color: #f5f5f5;
        border: 1rpx solid #e6e6e6;

        text {
          color: #666666;
        }
      }
    }
  }
}
</style>