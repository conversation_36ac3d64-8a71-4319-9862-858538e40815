import {requests} from '../utils/request.js'



export const page_img_statr = 'https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/app_tab/'

// xiaoa.co?token=??

export const post_login = (data) =>{
	return requests({
		url:'/login',
		method:'post',
		data:data
	})
}
export const get_authorization = (data) =>{
	return requests({
		url:'/tool/list',
		method:'get',
		data:data
	})
}

export const get_userInfo = (data) =>{
	return requests({
		url: '/user/info',
		method:'get',
		data:data
	})
}

export const get_msgList = (data) =>{
	return requests({
		url:'/user/msg/list',
		method:'get',
		data:data
	})
}

export const get_drList = (data) =>{
	return requests({
		url:'/user/dr/list',
		method:'get',
		data:data
	})
}

export const get_tutorial= (data) =>{
	return requests({
		url:'/tutorial/guide',
		method:'get',
		data:data
	})
}

export const get_redeemCode= (data) =>{
	return requests({
		url:'/user/use/redeemCode',
		method:'get',
		data:data
	})
}



export default {
	post_login,
	get_authorization,
	get_userInfo,
	get_msgList,
	get_drList,
	get_tutorial,
	get_redeemCode
}
