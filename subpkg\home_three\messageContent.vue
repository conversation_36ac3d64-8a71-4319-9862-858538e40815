<template>
	<view>
		<view class="box">
			<view class="card">
				<view>
					<image :src="url"></image>
				</view>
				<view class="card_text">
					<view class="title">运营小助手</view>
					<view class="time">{{item.createTime}}</view>
				</view>
			</view>
			<view class="message">
				{{item.content}}
			</view>

			<view class="footer">
				<view class="footer_box" @click="copy_message(item.content)">
					<u-icon name="home"></u-icon>
					<text>复制文案</text>
				</view>
				<view class="footer_box">
					<u-icon name="home"></u-icon>
					<text>生成视频</text>
				</view>
				<view class="footer_box">
					<u-icon name="home"></u-icon>
					<text>AL改写</text>
				</view>
			</view>
		</view>



	</view>
</template>

<script>
	export default {
		data() {
			return {
				item: '',
				url:''
			}
		},
		onLoad() {
			this.getOpenerEventChannel().on('get_messageContent', (item,url) => {
				this.item = item.item
				this.url = item.url
			})
		},
		methods:{
			//复制
			copy_message(message) {
				uni.setClipboardData({
					data: message,
					success: () => {
						uni.showToast({
							title: '复制成功',
							icon: 'none'
						});
					},
					fail: () => {
						uni.showToast({
							title: '复制失败',
							icon: 'none'
						});
					}
				});
			},
		}
	}
</script>

<style scoped lang="scss">
	.box_list {
		font-size: 28rpx;
		margin-top: 60rpx;
		padding: 20rpx 40rpx;
		color: white;

		.list_content {
			display: flex;
			align-items: center;
			margin-bottom: 30rpx;
		}
	}
	.box {
		padding: 60rpx 40rpx 0 40rpx;
	
		.card {
			display: flex;
			align-items: center;
			image {
				width: 80rpx;
				height: 80rpx;
			}
	
			.card_text {
				margin-left: 20rpx;
	
				.title {
					font-size: 28rpx;
					font-weight: 600;
					margin-bottom: 10rpx;
				}
	
				.time {
					font-size: 26rpx;
					opacity: 0.8;
				}
			}
		}
	
		.message {
			margin-top: 20rpx;

      font-family: MiSans, MiSans;
      font-weight: 500;
      font-size: 24rpx;
      color: #999999;
      line-height: 36rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;
		}
		
		.footer {
			background: white;
			display: flex;
			justify-content: space-around;
			position: fixed;
			width: 100vw;
			bottom: 0rpx;
			left: 0;
			padding: 20rpx;
			border-top: 2rpx solid #dedede;
			.footer_box {
				padding: 10rpx 20rpx;
      font-size: 24rpx;
				display: flex;
				align-items: center;
        background: #F3F5F8;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
				text {
					font-size: 26rpx;
				}
			}
		}
	}
</style>