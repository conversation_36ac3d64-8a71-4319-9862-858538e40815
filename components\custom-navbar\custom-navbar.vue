<template>
  <view class="navbar">
    <!-- navbar实体占位 -->
    <view class="navbar-placeholder" :style="{ height: statusBarHeight + navBarHeight + paddingBottom + 'px' }"></view>
    <view class="navbar-container" :style="[styleObject()]">
      <!-- 状态栏占位 -->
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <!-- 导航栏占位（与胶囊按钮平行） -->
      <view class="nav-bar" :style="{
        height: navBarHeight + 'px'
      }">
        <!-- 返回按钮 -->
        <view v-if="showBackBtn && !isBlack" class="nav-back-btn" @click="handleBack">
          <image src="/static/返回.png" mode="aspectFit" class="back-icon"></image>
        </view>
        <view v-if="showBackBtn && isBlack" class="nav-back-btn" @click="handleBack">
          <image src="/static/返回黑.png" mode="aspectFit" class="back-icon"></image>
        </view>
        <!-- 标题区域 -->
        <text v-if="title" class="nav-title" :style="{
          color: titleColor,
          fontSize: titleSize + 'rpx',
          fontFamily: titleFontFamily,

        }">{{ title }}</text>
      </view>
    </view>
  </view>

</template>

<script>
export default {
  name: 'custom-navbar',
  props: {
    // 导航栏标题
    title: {
      type: String,
      default: ''
    },
    // 标题颜色
    titleColor: {
      type: String,
      default: '#000000'
    },
    // 标题字体大小
    titleSize: {
      type: Number,
      default: 34
    },
    // 标题字体
    titleFontFamily: {
      type: String,
      default: 'MiSans-Demibold'
    },
    // 背景图片
    backgroundImage: {
      type: String,
      default: ''
    },
    // 背景颜色
    backgroundColor: {
      type: String,
      default: ''
    },
    // 是否显示返回按钮
    showBackBtn: {
      type: Boolean,
      default: true
    },
    // 返回后进行页面刷新
    refreshOnBack: {
      type: Boolean,
      default: false
    },
    // 背景
    background: {
      type: String,
      default: ''
    },
    // 底部padding
    paddingBottom: {
      type: Number,
      default: 0
    },
    // 是否为黑色
    isBlack: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      statusBarHeight: 0, // 状态栏高度
      navBarHeight: 44,   // 导航栏高度默认值

    }
  },
  created() {
    // 获取状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;

    // #ifdef MP-WEIXIN
    // 获取微信胶囊按钮位置信息
    const menuButton = wx.getMenuButtonBoundingClientRect();
    // 导航栏高度 = 胶囊高度 + (顶部距离 - 状态栏高度) * 2
    this.navBarHeight = menuButton.height + (menuButton.top - this.statusBarHeight) * 2;
    // #endif
  },
  methods: {
    // 处理返回按钮点击事件
    handleBack() {
      if (this.refreshOnBack) {
        // 返回上一页并刷新
        uni.navigateBack({
          delta: 1,
          success: function () {
            var pages = getCurrentPages();
            var prevPage = pages[pages.length - 1];
            prevPage.onLoad(); // 刷新页面
          }
        });
      } else {
        console.log('普通返回');
        this.$emit('beforeJump');
        // 普通返回
        uni.navigateBack({ delta: 1 });
      }

      // 触发自定义事件，便于父组件响应
      this.$emit('back');
    },
    styleObject() {
      const styleObject = {

      }
      if (this.backgroundImage) {
        styleObject.backgroundImage = this.backgroundImage;
      }
      if (this.backgroundColor) {
        styleObject.backgroundColor = this.backgroundColor;
      }
      if (this.background) {
        styleObject.background = this.background;
      }

      if (this.paddingBottom) {
        styleObject.paddingBottom = this.paddingBottom + 'px';
      }
      return styleObject;
    }
  }
}
</script>

<style lang="scss">
.navbar-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;

  .status-bar {
    width: 100%;
  }

  .nav-bar {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    .nav-back-btn {
      position: absolute;
      left: 30rpx;
      height: 100%;
      display: flex;
      align-items: center;
      z-index: 10;

      .back-icon {
        width: 44rpx;
        height: 44rpx;
      }
    }

    .nav-title {
      font-weight: 500;
      text-align: center;
      // 为右侧胶囊按钮预留空间
      max-width: 60%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>