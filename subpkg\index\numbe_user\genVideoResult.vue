<template>
  <view class="g-container">
    <view class="main">
      <image :src="resultUrl" class="img"/>
      <view class="sub-title">
        <text>-  视频作品生成需要一些时间，请耐心等待;</text>
        <text>-  作品生成完成后，我们将通过通知告知您结果;</text>
        <text>-  您可以随时在 <text style="font-weight: bold">[动态]</text> 页面查看进度和生成结果;</text>
      </view>
      <view class="btn" @click="toResult">去查看作品
      <image style="height:50rpx;width:50rpx;margin-left: 10rpx;margin-top: 5rpx" src="/static/home/<USER>"/>
      </view>
    </view>
  </view>
</template>

<script>
import {requestSubscribeRecord} from "@/utils/subscribeMsg";

export default {
  data() {
    return {
      resultUrl:'http://xiao-dr.oss-cn-beijing.aliyuncs.com/common/app_tab/project-gen-video-result.jpg'
    };
  },
  methods: {
    async toResult() {
      try {
        await requestSubscribeRecord(this.$appConfig.appInfo.wxTemplateId).catch(e => {
          console.log('订阅消息处理结果:', e); // todo 记录日志
        });
      } finally {
        uni.switchTab({
          url: '/pages/home_two/index'
        });
      }
    }
  },
}
</script>

<style lang="scss">
.g-container{
  background: #f3f5f8;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}
.main{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 75%;
  .img{
    width: 450rpx;
    height: 390rpx;
  }
  .sub-title{
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    margin-top: 40rpx;
    color: #adafae;
    text{
      margin-top: 5rpx;
      font-weight: normal;
      font-size: 24rpx;
    }
  }
  text{
    font-size: 40rpx;
    font-weight: 600;
  }
  .btn{
    margin-top: 150rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 60%;
    padding: 20rpx;
    border-radius: 20rpx;
    background: black;
    color: white;
  }
}

</style>
