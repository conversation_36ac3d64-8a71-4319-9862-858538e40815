<template>
	<view class="page">
		<vipVersion @show_model="show_model" :pageShow="page_show" @down_close="page_show = false"
			:fastPowerRange="fastPowerRange"></vipVersion>
		<view style="z-index: 999;" v-if="page_model">
			<InsufficientPermissions @closeModel="close"></InsufficientPermissions>
		</view>

		<view class="content_box">
			
			<!-- 模板列表 -->
			<view class="page_scroll">
        <!-- 添加卡片 -->
        <view class="add_card" @click="addTemplate">
          <u-icon name="plus" color="#999" size="36"></u-icon>
          <text class="add-text">添加模板</text>
        </view>
				
				<!-- 模板卡片 -->
				<view v-for="item in template_list" :key="item.id" class="card" @click="down_card(item)">
					<image :src="item.preUrl" class="image_" mode="aspectFill"></image>
					<view class="card-title">{{item.name}}</view>
				</view>
				

			</view>
		</view>
		
		<!-- 模板详情弹窗 -->
		<u-popup :show="templateModel" mode="center" round="20" @close="templateModel = false">
			<view class="template_box" >
				<u-icon name="close" size="24" color="#999" 
					style="position: absolute; top: 24rpx; right: 24rpx;"
					@click="templateModel = false"></u-icon>
				
				<view class="template-title">模版信息</view>
				
				<view style="text-align: center;">
					<image class="template-preview" mode="widthFix" :src="templateItem.preUrl"></image>
				</view>
				
				<view class="info-item">
					<view class="info-label">数字人</view>
					<view class="info-value">{{templateItem.name}}</view>
				</view>
				
				<view class="info-item">
					<view class="info-label">标题</view>
					<image class="info-image" mode="heightFix" :src="templateItem.titleUrl"></image>
				</view>
				
				<view class="info-item">
					<view class="info-label">字幕</view>
					<image class="info-image" mode="heightFix" :src="templateItem.captionUrl"></image>
				</view>
				
				<view class="info-item">
					<view class="info-label">身份栏</view>
					<view class="info-value">{{templateItem.introduceCardName}}</view>
				</view>
				
				<view class="delete-btn" @click="down_del(templateItem.id)">删除模板</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import {
		getTemplateList
	} from '../../api/numberUser/userType.js'
	
	import {requests} from '../../utils/request.js'
	export default {
		data() {
			return {
				template_list: '',
				templateModel:false,
				templateItem:''
			}
		},
		onShow() {
			//获取我的模版
			this.get_list()
		},
		methods: {
			get_list(){
				getTemplateList({
					pageNum: 1,
					pageSize: 100
				}).then(res => {
					this.template_list = res.rows
				})
			},
			
			down_card(item){
				this.templateItem = item
				this.templateModel = true
			},
			//添加模版
			addTemplate(){
				uni.navigateTo({
					url:'/subpkg/index/get_radio/add_stencil'
				})
			},
			
			down_del(id){
				requests({
					url:`/user/template/${id}`,
					method:'delete'
				}).then(res =>{
					this.templateModel = false 
					this.get_list()
					uni.showToast({
						title:'删除成功',
						icon:'none'
					})
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		padding: 24rpx;
		background-color: #f8f8f8;
		min-height: 100vh;
		box-sizing: border-box;
	}
	
	.content_box {
		margin-top: 20rpx;
	}
	
	/* 标题样式 */
	.my_text {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 30rpx 0 20rpx;
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		
		.right {
			display: flex;
			align-items: center;
			font-size: 24rpx;
			color: #666;
			font-weight: normal;
			
			text {
				margin-right: 8rpx;
			}
		}
	}
	
	/* 卡片列表 */
	.page_scroll {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}
	
	/* 卡片基础样式 */
	.card, .add_card {
		width: 31%;
		height: 302rpx;
		border-radius: 16rpx;
		overflow: hidden;
		position: relative;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
		transition: transform 0.2s;
		
		&:active {
			transform: scale(0.98);
		}
	}
	
	/* 卡片图片 */
	.image_ {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	
	/* 卡片标题 */
	.card-title {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
		color: #fff;
		padding: 16rpx;
		font-size: 24rpx;
		text-align: center;
	}
	
	/* 添加卡片样式 */
	.add_card {
		background: #f5f5f5;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		border: 1px dashed #ddd;
		
		.add-text {
			font-size: 24rpx;
			color: #999;
			margin-top: 16rpx;
		}
	}
	
	/* 模板详情弹窗 */
	.template_box {
		width: 80vw;
		padding: 40rpx;
		position: relative;
		background: #fff;
		border-radius: 20rpx;
	}
	
	.template-title {
		font-size: 36rpx;
		font-weight: 600;
		margin-bottom: 30rpx;
		text-align: center;
	}
	
	.template-preview {
		width: 300rpx;
		border-radius: 12rpx;
		margin-bottom: 30rpx;
	}
	
	/* 信息项样式 */
	.info-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 24rpx;
		font-size: 28rpx;
		
		.info-label {
			font-weight: 600;
			color: #333;
			width: 120rpx;
		}
		
		.info-value {
			flex: 1;
			text-align: right;
			color: #666;
		}
		
		.info-image {
			height: 60rpx;
			max-width: 60%;
		}
	}
	
	/* 删除按钮 */
	.delete-btn {
		// background: #ff4d4f;
		background: black;
		color: #fff;
		border-radius: 50rpx;
		padding: 16rpx 0;
		text-align: center;
		margin-top: 40rpx;
		font-size: 28rpx;
		transition: opacity 0.3s;
		
		&:active {
			opacity: 0.8;
		}
	}
</style>