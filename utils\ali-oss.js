import { getAliCredentials } from "../api/oss/oss";
import store from '../store'


/**
 * 获取OSS上传凭证
 */
export const getOssCredentials = async (dir = 'upload') => {
    uni.showLoading({
        title: '上传中...'
    });

    try {
        const response = await getAliCredentials(dir)
        console.log(response)
        if (response.code === 200) {
            return response.data;
        }
        throw new Error(response.msg);
    } catch (error) {
        console.error('获取OSS凭证失败:', error);
        throw error;
    }
};

/**
 * 上传文件到OSS
 * @param {Object} file - 文件对象
 * @param {String} dir - 上传目录
 * @returns {Promise<String>} 文件访问URL
 */
export const uploadToOss = async (file, dir = 'upload') => {

    try {
        // 获取上传凭证
        const credentials = await getOssCredentials(dir);

        // 生成文件名
        const fileName = `${dir}/${Date.now()}-${store.state.user.userInfo.userId}-${file.name || 'file'}`;

        // 使用uni.uploadFile上传
        const res = await uni.uploadFile({
            url: credentials.host,
            filePath: file.path,
            name: 'file',
            formData: {
                key: fileName,
                policy: credentials.policy,
                OSSAccessKeyId: credentials.accessKeyId,
                signature: credentials.signature,
                success_action_status: '200'
            }
        });
        console.log("上传alioss结果" + JSON.stringify(res))

        if (res.statusCode === 200) {
            return `${credentials.host}/${fileName}`;
        } else {
            uni.hideLoading();
            throw new Error('上传失败');
        }
    } catch (error) {
        uni.hideLoading();
        console.error('上传文件失败:', error);
        throw error;
    }
};

/**
 * 选择并上传图片
 * @param {Object} options - 上传选项
 * @returns {Promise<String>} 文件访问URL
 */
export const chooseAndUploadImage = async (options = {}) => {
    try {
        // 选择图片
        const res = await uni.chooseImage({
            count: 1,
            sizeType: ['original', 'compressed'],
            sourceType: ['album', 'camera'],
            extension: options.extension || ['.jpg', '.jpeg', '.png', '.gif']
        });


        const file = res.tempFiles[0];

        // 检查文件大小（默认限制10MB）
        const maxSize = options.maxSize || 10 * 1024 * 1024;
        if (file.size > maxSize) {
            throw new Error(`文件大小不能超过${maxSize / 1024 / 1024}MB`);
        }

        // 显示上传进度
        uni.showLoading({
            title: '上传中...'
        });

        // 上传文件
        const url = await uploadToOss(file, options.dir || 'app-user-img');

        uni.hideLoading();
        return url;
    } catch (error) {
        uni.hideLoading();
        console.error('选择并上传图片失败:', error);
        throw error;
    }
};

/**
 * 选择并上传视频
 * @param {Object} options - 上传选项
 * @returns {Promise<String>} 文件访问URL
 */
export const chooseAndUploadVideo = async (options = {}) => {
    try {
        // 选择视频
        const res = await uni.chooseVideo({
            sourceType: ['album', 'camera'],
            compressed: true,
            maxDuration: options.maxDuration || 60,
            camera: 'back'
        });
        // console.log(res);

        const file = {
            path: res.tempFilePath,
            size: res.size,
            name: res.tempFilePath.split('/').pop()
        };

        // 检查文件大小（默认限制100MB）
        const maxSize = options.maxSize || 100 * 1024 * 1024;
        if (file.size > maxSize) {
            throw new Error(`文件大小不能超过${maxSize / 1024 / 1024}MB`);
        }

        // 显示上传进度
        uni.showLoading({
            title: '上传中...'
        });

        // 上传文件
        const url = await uploadToOss(file, options.dir || 'videos');


        return url;
    } catch (error) {
        console.error('选择并上传视频失败:', error);
        throw error;
    }
};

/**
 * 视频转音频
 * 调用视频上传，然后定时器请求，直到响应成功或者异常，返回音频url
 */

/**
 * 上传base64到OSS
 * @param {String} base64 - base64字符串
 * @param {String} dir - 上传目录
 * @param {String} fileName - 文件名（可选）
 * @returns {Promise<String>} 文件访问URL
 */
export const uploadBase64ToOss = async (base64, dir = 'images', fileName = null) => {
    try {
        // 获取上传凭证
        const credentials = await getOssCredentials(dir);

        // 处理base64字符串
        const base64Data = base64.replace(/^data:image\/\w+;base64,/, '');
        const buffer = uni.base64ToArrayBuffer(base64Data);

        console.log(buffer)
        // 生成文件名
        const fileExt = base64.split(';')[0].split('/')[1] || 'png';
        const finalFileName = fileName || `${Date.now()}.${fileExt}`;
        const objectName = `${dir}/${finalFileName}`;

        // 将 buffer 保存为临时文件路径
        // const tempFilePath = await saveBufferToTempFilePath(buffer, finalFileName);

        const tempFilePath = await new Promise((resolve, reject) => {
            const fs = uni.getFileSystemManager();
            const tempFilePath = `${uni.env.USER_DATA_PATH}/${finalFileName}`;

            fs.writeFile({
                filePath: tempFilePath,
                data: base64Data,
                encoding: 'base64',
                success: () => resolve(tempFilePath),
                fail: reject
            });
        });


        // 使用uni.uploadFile上传
        const res = await uni.uploadFile({
            url: credentials.host,
            filePath: tempFilePath,
            name: 'file',
            formData: {
                key: objectName,
                policy: credentials.policy,
                OSSAccessKeyId: credentials.accessKeyId,
                signature: credentials.signature,
                success_action_status: '200'
            }
        });


        if (res.statusCode === 200) {
            // 返回文件访问URL
            return `${credentials.host}/${objectName}`;
        } else {
            throw new Error('上传失败');
        }
    } catch (error) {
        console.error('上传base64失败:', error);
        throw error;
    }
};

/**
 * 从canvas获取base64并上传
 * @param {Object} options - 上传选项
 * @returns {Promise<String>} 文件访问URL
 */
export const uploadCanvasToOss = async (options = {}) => {
    try {
        // 创建canvas上下文
        const ctx = uni.createCanvasContext('uploadCanvas');

        // 等待canvas绘制完成
        await new Promise(resolve => {
            ctx.draw(false, () => {
                setTimeout(resolve, 100);
            });
        });

        // 获取canvas的base64数据
        const res = await uni.canvasToTempFilePath({
            canvasId: 'uploadCanvas',
            fileType: options.fileType || 'png',
            quality: options.quality || 1
        });


        // 显示上传进度
        uni.showLoading({
            title: '上传中...'
        });

        // 上传base64
        const url = await uploadBase64ToOss(res.tempFilePath, options.dir || 'images', options.fileName);

        uni.hideLoading();
        return url;
    } catch (error) {
        uni.hideLoading();
        console.error('上传canvas失败:', error);
        throw error;
    }
};


//上传临时路径
export const uploadUrl = async (url, dir = 'dr_template') => {


    const timestamp = Date.now();
    const date = new Date(timestamp);

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 补零
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    const formattedDate = `${store.state.user.userInfo.userId}_${year}${month}${day}${hours}${minutes}${seconds}.png`;

    try {
        // 获取上传凭证
        const credentials = await getOssCredentials(dir);

        // 生成文件名
        const fileName = `${dir}/${formattedDate}`;

        // 使用uni.uploadFile上传
        const res = await uni.uploadFile({
            url: credentials.host,
            filePath: url,
            name: 'file',
            formData: {
                key: fileName,
                policy: credentials.policy,
                OSSAccessKeyId: credentials.accessKeyId,
                signature: credentials.signature,
                success_action_status: '200'
            }
        });
        console.log("上传alioss结果" + JSON.stringify(res))

        if (res.statusCode === 200) {
            // 返回文件访问URL
            return `${credentials.host}/${fileName}`;
        } else {
            throw new Error('上传失败');
        }
    } catch (error) {
        console.error('上传文件失败:', error);
        throw error;
    }
};



/**
 * 选择并上传音频
 * @param {Object} options - 上传选项
 * @returns {Promise<String>} 文件访问URL
 */
export const chooseAndUploadAudio = async (options = {}) => {
    try {
        // 选择音频文件
        // 1. 选择音频（仅小程序和 App 支持）
        const res = await uni.chooseFile({
            count: 1,
            type: 'file',
            extension: ['mp3', 'wav', 'aac', 'm4a']
        });

        console.log("选择音频结果" + JSON.stringify(res))

        const file = res.tempFiles[0];

        // 检查文件大小（默认限制10MB）
        const maxSize = options.maxSize || 10 * 1024 * 1024;
        if (file.size > maxSize) {
            throw new Error(`音频大小不能超过${maxSize / 1024 / 1024}MB`);
        }

        // 显示上传进度
        uni.showLoading({
            title: '上传中...'
        });

        console.log(file)

        // 提取文件后缀
        const fileExtension = file.split('.').pop(); // 这将得到"mp4"
        const allowedExtensions = ['mp3', 'aac', 'wav'];  // 允许的音频格式
        if (!allowedExtensions.includes(fileExtension)) {
            throw new Error(`不支持的音频格式：${fileExtension}`);
        }
        // 上传文件
        const url = await uploadToOss({ path: file.tempFilePath }, options.dir || 'user-music');

        uni.hideLoading();
        return url;
    } catch (error) {
        uni.hideLoading();
        console.error('选择并上传音频失败:', error);
        throw error;
    }
};

/**
 * 选择并上传文件
 * @param {Object} options - 上传选项
 * @returns {Promise<String>} 文件访问URL
 */
export const chooseAndUploadFile = async (options = {}) => {
    try {
        // 显示上传进度
        uni.showLoading({
            title: '上传中...'
        });

        // 选择文件
        const res = await uni.chooseMedia({
            count: 1,
            mediaType: ['image', 'video'],
            sourceType: ['album', 'camera'],
            maxDuration: 30,
            camera: 'back'
        })
        // 显示上传进度
        uni.showLoading({
            title: '上传中...'
        });
        console.log("选择文件结果" + JSON.stringify(res))

        const file = res.tempFiles[0];

        // 检查文件大小（默认限制100MB）
        const maxSize = options.maxSize || 100 * 1024 * 1024;
        if (file.size > maxSize) {
            throw new Error(`文件大小不能超过${maxSize / 1024 / 1024}MB`);
        }


        console.log(file)

        // 提取文件后缀
        const fileExtension = file.tempFilePath.split('.').pop();
        const allowedExtensions = ['png', 'jpg', 'jpeg', 'mp4', 'mov', 'avi'];
        if (!allowedExtensions.includes(fileExtension)) {
            throw new Error(`不支持的文件格式：${fileExtension}`);
        }
        // 上传文件
        const userInfo = store.state.user.userInfo
        const url = await uploadToOss({ name: `media@${userInfo.userId}.${fileExtension}`, path: file.tempFilePath }, options.dir || 'user-media');
        console.log("上传alioss结果" + url)
        uni.hideLoading();
        return url;
    } catch (error) {
        uni.hideLoading();
        console.error('选择并上传文件失败:', error);
        throw error;
    }
};
