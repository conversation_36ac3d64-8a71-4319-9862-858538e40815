<template>
  <view class="batch-video-page">
    <custom-navbar title="批量生产视频" :isBack="true" :isBlack="true"></custom-navbar>
    <!-- 主要展示图片 -->
    <image class="hero-image" referrerpolicy="no-referrer" src="/static/批量生产视频首页.png" />

    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 文本介绍区域 -->
      <view class="text-content">
        <view class="main-title">
          批量制作视频
          <br />
          化繁为简，效率增倍
        </view>

        <!-- 分割线 -->
        <view class="divider"></view>
        <view class="subtitle">轻松创建海量数字人视频</view>
        <view class="description">
          只需要选择数字人，提供文案，上传素材，即可批量生产专业视频。
          <br />
          省时省力，让创意事半功倍。
        </view>
      </view>

      <!-- 操作按钮区域 -->
      <view class="action-buttons" :style="{ paddingBottom: safeAreaBottom + 'px' }">
        <view class="my-works-btn" @click="myBatchVideo">
          <view class="btn-text">我的作品</view>
        </view>
        <view class="start-create-btn" @click="batchVideoConfig">
          <view class="btn-text">开始制作</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      safeAreaBottom: 0
    };
  },
  onLoad() {
    this.safeAreaBottom = uni.getSystemInfoSync().safeAreaInsets.bottom + uni.upx2px(12);

  },
  methods: {
    batchVideoConfig() {
      uni.navigateTo({
        url: '/subpkg/batchVideoConfig/index'
      })
    },
    myBatchVideo() {
      uni.navigateTo({
        url: '/subpkg/myBatchVideo/index'
      })
    }
  }
};
</script>

<style lang="scss">
.batch-video-page {
  background-color: rgba(243, 245, 248, 1);
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;

  .hero-image {
    width: 686rpx;
    height: 508rpx;
    margin: 30rpx 0 0 32rpx;
  }

  .content-container {
    width: 100%;

    .text-content {
      width: 654rpx;
      margin: 96rpx 0 0 48rpx;

      .main-title {
        width: 882rpx;
        height: 144rpx;
        overflow-wrap: break-word;
        color: #333333;
        font-size: 48rpx;
        font-family: ChillRoundGothic_Bold;
        font-weight: 700;
        text-align: left;
        line-height: 72rpx;
        margin-bottom: 32rpx;
      }

      .subtitle {
        width: 288rpx;
        height: 48rpx;
        overflow-wrap: break-word;
        color: #333333;
        font-size: 32rpx;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 48rpx;
        margin-top: 64rpx;
      }

      .description {
        width: 654rpx;
        height: 124rpx;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 24rpx;
        font-weight: 500;
        text-align: left;
        line-height: 36rpx;
        margin-top: 24rpx;
      }
    }

    .action-buttons {
      position: fixed;
      bottom: 0;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      background-color: rgba(255, 255, 255, 1);
      border-radius: 32rpx 32rpx 0 0;
      width: 100%;
      padding: 20rpx 0;
      // height: 98rpx;

      .my-works-btn {
        display: flex;
        flex-direction: column;
        background-color: rgba(239, 239, 239, 1);
        border-radius: 16rpx;
        height: 96rpx;
        width: 254rpx;
        margin: 2rpx 0 0 32rpx;

        .btn-text {
          width: 128rpx;
          height: 48rpx;
          color: rgba(51, 51, 51, 1);
          font-size: 32rpx;
          font-family: MiSans-Demibold;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 48rpx;
          margin: 24rpx 0 0 64rpx;
        }
      }

      .start-create-btn {
        display: flex;
        flex-direction: column;
        background-image: linear-gradient(135deg,
            rgba(34, 35, 44, 1) 19.837089%,
            rgba(15, 15, 15, 1) 100%);
        border-radius: 16rpx;
        height: 96rpx;
        width: 400rpx;
        margin: 2rpx 32rpx 0 0;

        .btn-text {
          width: 128rpx;
          height: 48rpx;
          color: rgba(255, 255, 255, 1);
          font-size: 32rpx;
          // font-family: asdsadwd;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 48rpx;
          margin: 24rpx 0 0 136rpx;
        }
      }
    }
  }

  .bottom-decoration {
    width: 100%;
    height: 68rpx;
  }

  .divider {
    border-bottom: 1rpx solid #D8D8D8;
    height: 0;
  }
}
</style>