<template>
	<view class="page">
		
		<view style="display: flex;justify-content: space-around;" v-if="page_current == 0">
			<video
			  :src='video_url'
			  :controls="false"
			  :show-progress="false"
			  :show-fullscreen-btn="false"
			  :show-play-btn="false"
			  :show-center-play-btn="false"
			  :show-loading="false"
			  object-fit="cover"
			  style="width: 36vw;height: 75vw;background: white;opacity: 0.8;"
			  class="card_center"
			>
			</video>
		</view>
		
		<view style="display: flex;justify-content: space-around;"v-if="page_current == 1">
			
			<video
			  :src='video_url'
			  :controls="false"
			  :show-progress="false"
			  :show-fullscreen-btn="false"
			  :show-play-btn="false"
			  :show-center-play-btn="false"
			  :show-loading="false"
			  object-fit="cover"
			  style="width: 80vw;height: 60vw;background: white;opacity: 0.8;"
			  class="card_center"
			>
			</video>
		</view>
		
		<view style="display: flex;justify-content: space-around;"v-if="page_current == 2">
			<video
			  :src='video_url'
			  :controls="false"
			  :show-progress="false"
			  :show-fullscreen-btn="false"
			  :show-play-btn="false"
			  :show-center-play-btn="false"
			  :show-loading="false"
			  object-fit="cover"
			  style="width: 75vw;height: 36vw;background: white;opacity: 0.8;"
			  class="card_center"
			>
			</video>
		</view>
		
		<view style="display: flex;justify-content: space-around;"v-if="page_current == 3">
			<video
			  :src='video_url'
			  :controls="false"
			  :show-progress="false"
			  :show-fullscreen-btn="false"
			  :show-play-btn="false"
			  :show-center-play-btn="false"
			  :show-loading="false"
			  object-fit="cover"
			  style="width: 60vw;height: 80vw;background: white;opacity: 0.8;"
			  class="card_center"
			>
			</video>
		</view>
		
		<view style="display: flex;justify-content: space-around;"v-if="page_current == 4">
			<video
			  :src='video_url'
			  :controls="false"
			  :show-progress="false"
			  :show-fullscreen-btn="false"
			  :show-play-btn="false"
			  :show-center-play-btn="false"
			  :show-loading="false"
			  object-fit="cover"
			  style="width: 60vw;height: 60vw;background: white;opacity: 0.8;"
			  class="card_center"
			>
			</video>
		</view>
		
		
		

		<view class="ratio-selector">
			<view v-for="item in list" class="ratio-item" :class="{ active: page_current === item.index }"
				@click="changeRatio(item.index)">
				<!-- <image :src="item.src" mode="aspectFit"></image> -->
				<view>{{item.name}}</view>
			</view>
		</view>

		<view class="project_btn" @click="go_save">
			完成
		</view>
		
		
		
		
		
		
		
		
		
		
		
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				video_url: '',
				list: [{
						src: 'stat',
						name: '9:16',
						index: 0
					},
					{
						src: '/static/ratio/4-3.png',
						name: '4:3',
						index: 1
					},
					{
						src: '/static/ratio/16-9.png',
						name: '16:9',
						index: 2
					},
					{
						src: '/static/ratio/3-4.png',
						name: '3:4',
						index: 3
					},
					{
						src: '/static/ratio/1-1.png',
						name: '1:1',
						index: 4
					}
				],
				page_current: 0,
				videoReady: false,
				showError: false
			}
		},
		onLoad() {
			this.video_url = this.$store.state.numberUserVideo || ''
		},
		methods: {
			changeRatio(index) {
				this.page_current = index
			},
			videoLoaded() {
				this.videoReady = true
				this.showError = false
			},
			videoError() {
				this.showError = true
				this.videoReady = false
			},
			go_save() {
				uni.navigateTo({
					url: `/subpkg/index/save_video/index?current=${this.page_current}`
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.page {
		height: 100vh;
		width: 100vw;
		background: #000;
		position: relative;
		padding: 40rpx;
		box-sizing: border-box;
		.video-container {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 70vh;
		}

		.card_center {
			border: 2rpx solid #fff;
			background: rgba(255, 255, 255, 0.8);
			transition: all 0.3s ease;

			.video-message {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				color: white;
				font-size: 28rpx;

				&.error {
					color: #ff4d4f;
				}
			}
		}

		.ratio-selector {
			display: flex;
			justify-content: space-between;
			margin-top: 100rpx;
			color: white;
			.ratio-item {
				margin: 0 10rpx;
				text-align: center;
				opacity: 0.6;
				transition: all 0.3s;

				&.active {
					opacity: 1;
					transform: scale(1.1);
				}

				image {
					width: 60rpx;
					height: 60rpx;
					display: block;
					margin: 0 auto 10rpx;
				}

				view {
					font-size: 24rpx;
				}
			}
		}

		.project_btn {
			background: #3ee551;
			color: #000;
			position: absolute;
			bottom: 80rpx;
			left: 5vw;
			width: 90vw;
			text-align: center;
			padding: 20rpx 0;
			border-radius: 50rpx;
			font-size: 32rpx;
			font-weight: bold;
		}
	}
</style>