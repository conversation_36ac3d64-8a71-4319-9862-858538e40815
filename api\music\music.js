import { requests } from '../../utils/request.js'

export const getMusicClass = (data) => {
	return requests({
		url: '/music/category',
		method: 'get',
		data: data
	})
}

export const getMusicList = (data) => {
	return requests({
		url: '/music/list',
		method: 'get',
		data: data
	})
}

export const postMusic = (data) => {
	return requests({
		url: '/music',
		method: 'post',
		data: data
	})
}

export const putMusic = (data) => {
	return requests({
		url: '/music',
		method: 'put',
		data: data
	})
}

export const deleteMusic = (id) => {
	return requests({
		url: `/music/${id}`,
		method: 'delete'
	})
}







