<template>
	<view class="page">
		<view class="title">链接地址</view>
		
		<view class="input-container">
			<u--textarea 
				v-model="page_url" 
				placeholder="支持抖音、快手、小红书等视频分享链接"
				border="none"

				maxlength="300"
				autoHeight
				count
			></u--textarea>
			
<!--			<text class="bottom_right" @tap="pasteFromClipboard">从剪切版中粘贴</text>-->
		</view>
		<view style="height: 300rpx;"></view>
		<!-- 底部按钮区域 -->
		<view class="button-container">
			<view class="action-btn extract-btn" @click="extractText">提取文案</view>
<!--			<view class="action-btn my-works-btn" @click="navigateToMyWorks">我的作品</view>-->
		</view>
	</view>
</template>

<script>
	
	export default {
		data() {
			return {
				page_url: '',
				from: '' // 存储来源参数
			}
		},
		onLoad(options) {
			// 在onLoad中获取from参数
			this.from = options.from || '';
		},
		methods: {
			// 从剪贴板粘贴
			async pasteFromClipboard() {
				try {
					const data = await uni.getClipboardData();
					if (data && data.data) {
						this.page_url = data.data;
						uni.showToast({
							title: '粘贴成功',
							icon: 'success'
						});
					}
				} catch (err) {
					uni.showToast({
						title: '粘贴失败',
						icon: 'none'
					});
					console.error('粘贴失败:', err);
				}
			},
			// 跳转到我的作品
			navigateToMyWorks() {
				uni.navigateTo({
					url: '/pages/myWorks/myWorks'
				});
			},
			extractText(){
				if (!this.page_url) {
					uni.showToast({
						title: '请先输入或粘贴链接',
						icon: 'none'
					});
					return;
				}
				
				let that = this
				uni.navigateTo({
					url: '/subpkg/index/extractionResults' + (this.from ? `?from=${this.from}` : ''),
					success(res) {
						res.eventChannel.emit('get_message', that.page_url)
					}
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.page {
		padding: 40rpx;
		display: flex;
		flex-direction: column;

		.title {
			font-weight: 600;
			font-size: 32rpx;
			color: #333;
			margin-bottom: 20rpx;
		}
		
		.input-container {
			margin-top: 40rpx;
			position: relative;
			background: #fff;
			border-radius: 16rpx;
			padding: 20rpx;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
			flex: 1;
		}
		
		.textarea {
			height: 600rpx;
			font-size: 28rpx;
			padding: 10rpx;
		}
		
		.bottom_right {
			position: absolute;
			bottom: 10rpx;
			right: 20rpx;
			font-size: 28rpx;
			font-weight: 600;
			color: $project_1_text_color;
			padding: 8rpx 16rpx;
			background: rgba(0, 0, 0, 0.03);
			border-radius: 8rpx;
			
			&:active {
				background: rgba(0, 0, 0, 0.08);
			}
		}
		
		.button-container {
			display: flex;
			justify-content: space-between;

			padding-bottom: 40rpx;
			gap: 20rpx;
		}
		
		.action-btn {
			flex: 1;
			height: 90rpx;
			border-radius: 20rpx;
			font-size: 32rpx;
			font-weight: 500;
			display: flex;
			align-items: center;
			justify-content: center;
			
		
		}
		
		.extract-btn {
			background: black;
			color: white;
		}
		
		.my-works-btn {
			background: linear-gradient(135deg, #8ff09e 0%, #49c140 100%);
			color: white;
		}
	}
  ::v-deep .u-textarea__field {
    height: 200px !important;
  }
</style>