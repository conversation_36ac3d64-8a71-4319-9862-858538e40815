<template>
  <view class="page" :class="type === 'pro' ? 'theme-gold' : 'theme-blue'">
    <!-- 粒子效果 - 使用视图元素代替canvas -->
    <view class="particles-container">
      <view v-for="(particle, index) in particlesData" :key="index" class="particle" :style="{
        top: particle.top + '%',
        left: particle.left + '%',
        width: particle.size + 'rpx',
        height: particle.size + 'rpx',
        opacity: particle.opacity,
        animationDelay: particle.delay + 's',
        animationDuration: particle.duration + 's'
      }"></view>
    </view>

    <!-- 使用全局导航栏组件 -->
    <custom-navbar :showBack="true" titleColor="white" :paddingBottom="10"></custom-navbar>
    <!-- 顶部区域 -->
    <view class="header">
      <view class="title-area">
        <text class="main-title">{{ trainingCompleted ? `克隆完成` : `克隆中...`
        }}（{{ completedCount + '/' + modelList.length }}）</text>
        <view class="reset-btn" @click="model_again = true">
          <u-icon name="reload" size="16" color="#999"></u-icon>
          <text>重新定制</text>
        </view>
      </view>

      <!-- 进度条 -->
      <view class="progress-bar">
        <view class="progress-inner" :style="{ width: progressWidth }"></view>
      </view>
    </view>

    <!-- 原始声音播放区域 -->
    <view class="audio-section">
      <view class="audio-box" @click="toggleOriginalSound">
        <view class="play-btn">
          <image v-if="!isPlayingOriginal" src="/static/播放按钮.png"></image>
          <!--          <image v-else class="playing-gif" src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/fitment/0702音谱_1.gif">-->
          <!--          </image>-->
          <wave-loading barStyle="height:15rpx" bar-width="5rpx" v-else />
        </view>
        <text class="audio-text">原始声音</text>
      </view>
    </view>

    <!-- 选择克隆声音区域 -->
    <view class="clone-section">
      <view class="section-header">
        <text class="section-title">请选择你的克隆声音</text>
        <view class="section-action" @click="toggleSelectMode">
          {{ isMultiSelect ? '取消多选' : '多选' }}
        </view>
      </view>

      <view class="model-list">
        <!-- 根据后端返回的模型列表显示 -->
        <view class="model-item-box" v-for="(model, index) in modelList" :key="index">
          <view class="model-item" @click="selectModel(model)" :class="{
            'model-active': model.selected,
            'model-pending': model.status === 'pending',
            'model-failed': model.status === 'failed'
          }">
            <view class="recommend-tag" v-if="index === 0">推荐声音</view>

            <!-- 训练中状态显示 -->
            <view class="model-loading" v-if="model.status === 'pending'">
              <!-- 3D加载动画 -->
              <view class="loader">
                <view class="inner one"></view>
                <view class="inner two"></view>
                <view class="inner three"></view>
              </view>
              <text class="loading-text">克隆中...</text>
            </view>

            <!-- 失败状态显示 -->
            <view class="model-failed-icon" v-else-if="model.status === 'failed'">
              <u-icon name="close-circle" color="#ff5252" size="40"></u-icon>
              <text class="failed-text">训练失败</text>
              <view class="view-reason-btn" @click.stop="showFailReason(model)">查看原因</view>
            </view>

            <!-- 成功状态显示 -->
            <template v-else>
              <!-- 播放按钮，根据是否正在播放显示不同内容 -->
              <view class="model-play-btn" @click.stop="toggleClonedSound(model)">
                <image v-if="!isPlayingCloned || currentPlayingModel.id !== model.id" src="/static/播放按钮.png"></image>
                <!--                <image v-else class="playing-gif"-->
                <!--                  src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/fitment/0702音谱_1.gif"></image>-->
                <wave-loading barStyle="height:15rpx" bar-width="5rpx" v-else />
              </view>
              <text class="model-name">{{ model.name }} 模型</text>
              <!-- 训练完成发光提醒效果 -->
              <view class="completion-glow"></view>
            </template>

            <!-- 选中时的发光效果 -->
            <view v-if="model.selected" class="model-glow"></view>

            <!-- 选中图标 -->
            <view v-if="model.selected" class="selected-icon">
              <image src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/424b2910478af6167807eb2a028f613.png"></image>
            </view>
          </view>
          <!-- 编辑名称 -->
          <view class="edit-name" @click="handleEditName(model)" v-if="isMultiSelect">
            <image
              src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/<EMAIL>">
            </image>
            {{ model.voiceName }}
          </view>
        </view>
      </view>

      <view class="model-description" v-if="!isMultiSelect && selectedModel.length > 0 && selectedModel[0].description">
        {{ selectedModel[0].description }}
      </view>
    </view>
    <!-- 声音名称区域 -->
    <view class="name-section" v-if="!isMultiSelect">
      <text class="section-title">声音名称</text>
      <view class="input-wrapper">
        <u--input placeholder="请给你的声音取个好听的名字" border="none" v-model="voiceName" @input="updateSelectedModelName"
          color="#FFFFFF" :maxlength="20" class="name-input"></u--input>
      </view>
    </view>

    <!-- 底部按钮区域 -->
    <view class="action-bottom">
      <view class="action-bottom-left" v-if="isMultiSelect">
        <view class="action-bottom-left-num">
          {{ selectedModel.length }}
        </view>
        <view class="action-bottom-left-text">
          已选
        </view>
      </view>
      <view class="action-button-text"
        @click="trainingCompleted ? (free ? handleUseVoice() : handleSaveVoice()) : null">
        <span class="action-bottom-save-text"> {{ trainingCompleted ? `${free ? '选用' : '保存'}声音` : '克隆中...' }}</span>
        <span class="action-bottom-num-text" v-if="type === 'pro' && selectedModel.length > 0"> 保存消耗{{
          selectedModel.length
          *
          500 }}算力</span>
      </view>
    </view>

    <!-- 弹窗 -->
    <projectModel v-if="model_again" title="提示" content="您确认要重新训练吗?重新录制将丢失当前克隆内容。" save="重新训练"
      @btn_close="model_again = false" @btn_save="againVideo">
    </projectModel>

    <!-- 修改失败原因弹窗，使按钮可见 -->
    <!-- 添加失败原因弹窗 -->
    <FailReasonModal 
      :show="showFailReasonDialog"
      :title="'失败原因'"
      :content="failReasonContent"
      cancelText="关闭"
      confirmText="我知道了"
      @cancel="showFailReasonDialog = false"
      @confirm="showFailReasonDialog = false"
    />

    <!-- 重命名弹窗 -->
    <u-popup :show="showRenameDialog" mode="center" round="16">
      <view class="rename-content">
        <view class="rename-title">重命名</view>
        <view class="rename-input-wrap">
          <input type="text" v-model="editingName" class="rename-input" maxlength="20" />
        </view>
        <view class="rename-buttons">
          <view class="rename-button cancel" @click="cancelRename">取消</view>
          <view class="rename-button confirm" @click="confirmRename">确定</view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import {
  getTrainVoice,
  getVoiceConfirm,
  postVoiceConfirm,
  getVoiceDetail
} from '../../../api/numberUser/dr.js'
import WaveLoading from "@/components/wave-loader/index.vue";
import FailReasonModal from "@/components/FailReasonModal/index.vue";

export default {
  components: { WaveLoading, FailReasonModal },
  data() {
    return {
      loading: false,
      source: '',
      dictValue: '',
      type: false,
      demoUrl: '',
      reason: '',
      voiceName: '',
      taskId: '',
      batchId: '', // 批量任务ID
      model_again: false,
      voicePath: '',
      innerAudioContext: null,
      isPlayingOriginal: false,
      isPlayingCloned: false,
      currentPlayingModel: null, // 当前正在播放的模型
      statusBarHeight: 0,
      navBarHeight: 0,
      customBarHeight: 0,
      free: false,
      page_delta: 2,
      currentTab: null,
      post_url: '', // 从上一页面传递的音频URL
      language: '', // 从上一页面传递的语种
      pollingTimer: null,
      progressPercent: 100, // 进度条百分比，默认为100%
      particlesData: [],  // 存储粒子数据
      modelList: [], // 批量训练的模型列表
      trainingCompleted: false, // 是否训练完成
      showRenameDialog: false, // 是否显示重命名弹窗
      editingName: '', // 正在编辑的名称
      editingModel: null, // 正在编辑的模型
      isMultiSelect: false, // 是否为多选模式，默认为单选
      currentTheme: 'gold', // 当前主题，默认金色主题
      showFailReasonDialog: false, // 是否显示失败原因弹窗
      failReasonContent: '', // 失败原因内容
    }
  },
  computed: {
    // 计算进度条宽度
    progressWidth() {
      return `${this.progressPercent}%`;
    },
    // 计算选中的模型
    selectedModel() {
      return this.modelList.filter(model => model.selected)
    },
    // 计算克隆完成数量
    completedCount() {
      return this.modelList.filter(model => model.status !== 'pending').length
    }
  },
  onLoad(option) {
    this.type = option?.type
    this.free = option?.free
    this.source = option?.source
    this.currentTab = option?.currentTab
    this.post_url = decodeURIComponent(option?.post_url || '')
    this.language = option?.language || ''
    this.voicePath = this.post_url
    // 移除初始化音频上下文，改为每次播放时创建
    // this.initAudioContext()

    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
    // #ifdef MP-WEIXIN
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
    this.navBarHeight = menuButtonInfo.bottom + menuButtonInfo.top - this.statusBarHeight
    // #endif
    this.customBarHeight = this.statusBarHeight + this.navBarHeight

    // 生成粒子数据
    this.generateParticles()

    // 直接开始训练声音
    if (this.post_url && this.language !== undefined) {
      this.trainVoice(this.language)
    }
  },
  onUnload() {
    this.cleanup()
  },
  methods: {
    // 切换主题
    toggleTheme() {
      this.currentTheme = this.currentTheme === 'gold' ? 'blue' : 'gold';
    },

    // 切换单选和多选模式
    toggleSelectMode() {
      this.isMultiSelect = !this.isMultiSelect;

      // 如果从多选切换到单选，只保留第一个选中的模型
      if (!this.isMultiSelect) {
        const selectedModels = this.modelList.filter(m => m.selected);
        if (selectedModels.length > 1) {
          // 保留第一个选中的模型，取消其他选中
          selectedModels.forEach((model, index) => {
            if (index > 0) {
              model.selected = false;
            }
          });

          // 确保主声音名称和ID跟随第一个选中模型
          if (selectedModels[0]) {
            this.demoUrl = selectedModels[0].demoUrl;
            this.taskId = selectedModels[0].id;
            this.voiceName = selectedModels[0].voiceName;
          }
        }
      }
    },

    selectModel(model) {
      if (model.status !== 'unconfirmed') return;
      if (!this.isMultiSelect) {
        // 单选模式：取消所有其他选中
        this.modelList.forEach(m => {
          m.selected = false;
        });
        model.selected = true

        // 设置选中状态
        this.demoUrl = model.demoUrl;
        this.taskId = model.id;
        this.voiceName = model.voiceName;
      } else {
        // 多选模式：切换当前模型的选中状态
        model.selected = !model.selected;

        // 如果是选中，更新主声音
        if (model.selected) {
          this.demoUrl = model.demoUrl;
          this.taskId = model.id;
          this.voiceName = model.voiceName;
        } else {
          // 如果当前模型取消选中，检查是否还有其他选中的模型
          const selectedModel = this.modelList.find(m => m.selected);
          if (selectedModel) {
            this.demoUrl = selectedModel.demoUrl;
            this.taskId = selectedModel.id;
            this.voiceName = selectedModel.voiceName;
          }
        }
      }
    },

    // 处理重命名
    handleEditName(model) {
      if (model.status === 'pending' || model.status === 'failed') {
        return; // 如果模型还在训练中或训练失败，不允许编辑
      }

      this.editingModel = model;
      this.editingName = model.voiceName;
      this.showRenameDialog = true;
    },

    // 取消重命名
    cancelRename() {
      this.showRenameDialog = false;
      this.editingModel = null;
      this.editingName = '';
    },

    // 确认重命名
    confirmRename() {
      if (!this.editingName || this.editingName.trim().length === 0) {
        uni.showToast({
          title: '请填写声音名称',
          icon: 'none'
        });
        return;
      }

      if (this.editingName.length > 20) {
        uni.showToast({
          title: '声音名称不能超过20个字符',
          icon: 'none'
        });
        return;
      }

      // 更新模型名称
      if (this.editingModel) {
        this.editingModel.voiceName = this.editingName.trim();

        // 如果当前编辑的是选中的模型，更新主名称
        if (this.editingModel.selected) {
          this.voiceName = this.editingName.trim();
        }
      }

      this.showRenameDialog = false;
      this.editingModel = null;
      this.editingName = '';
    },

    // 不再需要初始化音频上下文
    // initAudioContext() {
    //   this.innerAudioContext = wx.createInnerAudioContext()
    //
    //   this.innerAudioContext.onEnded(() => {
    //     this.isPlayingOriginal = false
    //     this.isPlayingCloned = false
    //   })
    //
    //   this.innerAudioContext.onError((err) => {
    //     console.error('音频播放错误:', err)
    //     this.isPlayingOriginal = false
    //     this.isPlayingCloned = false
    //   })
    //
    //   this.innerAudioContext.onPlay(() => {
    //     console.log('音频开始播放')
    //   })
    // },

    cleanup() {
      // 确保音频上下文被销毁
      this.stopAudio();

      if (this.pollingTimer) {
        clearTimeout(this.pollingTimer)
        this.pollingTimer = null
      }
    },

    goBack() {
      uni.navigateBack()
    },

    trainVoice(languageType) {
      getTrainVoice({
        name: Math.random().toString(36).substring(2, 10),
        language: languageType,
        type: this.type,
        mode: 'batch', // 默认使用多声音克隆
        url: this.post_url
      }).then(res => {
        console.log(res, '获取声音id')
        // 处理返回的taskId或batchId
        this.batchId = res.data
        this.getSoundAgain(res.data)
      }).catch(err => {
        console.error('训练声音失败:', err)
        uni.showToast({
          title: '训练声音失败',
          icon: 'none'
        })
      })
    },

    getSoundAgain(id) {
      getVoiceDetail(id, {
        mode: 'batch',
        originAudioUrl: this.post_url,
        type: this.type
      }).then(res => {
        console.log(`声音轮训结果:${JSON.stringify(res)}`)

        // 更新模型列表
        if (res.data.tasks && Array.isArray(res.data.tasks)) {
          this.updateModelList(res.data.tasks)

          // 检查所有模型是否都已完成训练（不是pending状态）
          const allTasksCompleted = res.data.tasks.every(task =>
            task.status !== 'pending'
          )

          // 检查是否所有模型都训练成功（uncomfirmed或succeed状态）
          const allTasksSuccessful = res.data.tasks.every(task =>
            task.status === 'unconfirmed'
          )

          // 更新进度
          this.progressPercent = this.calculateProgress(res.data.tasks)

          // 如果还有模型在训练中，继续轮询
          if (!allTasksCompleted) {
            this.pollingTimer = setTimeout(() => {
              this.getSoundAgain(id)
            }, 5000)
          } else {
            // 所有模型训练完成
            if (allTasksSuccessful) {
              this.trainingCompleted = true
              this.progressPercent = 100

              // 选择默认模型
              this.selectDefaultModel()
            } else {
              // 至少有一个模型失败
              if (res.data.status === 'failed') {
                uni.showToast({
                  title: '克隆失败，请重试',
                  icon: 'none'
                })
              } else {
                // 部分成功，部分失败
                this.trainingCompleted = true

                // 选择默认模型
                this.selectDefaultModel()
              }
            }
          }
        } else {
          // 处理没有tasks数组的情况
          if (res.data.status === 'pending') {
            // 单个任务仍在处理中
            this.pollingTimer = setTimeout(() => {
              this.getSoundAgain(id)
            }, 5000)
          } else if (res.data.status === 'failed') {
            uni.showToast({
              title: '克隆失败，请重试',
              icon: 'none'
            })
          } else {
            // 单个任务成功
            this.trainingCompleted = true
            this.progressPercent = 100
            this.demoUrl = res.data.demoUrl
          }
        }
      }).catch(err => {
        console.error('获取训练结果失败:', err)
        uni.showToast({
          title: '获取训练结果失败，请重试',
          icon: 'none'
        })
      })
    },

    // 更新模型列表
    updateModelList(tasks) {
      this.modelList = tasks.map((task, index) => {
        return {
          id: task.id,
          name: `V${index + 1}`,
          status: task.status,
          demoUrl: task.demoUrl,
          reason: task.reason,
          voiceName: `声音${index + 1}`,
          selected: false,
          description: task.modelDescription ? task.modelDescription : this.modelList[index].description
        }
      })
    },

    // 选择默认模型（第一个成功的）
    selectDefaultModel() {
      const successModel = this.modelList.find(model =>
        model.status === 'unconfirmed'
      )

      if (successModel) {
        // 在单选模式下，确保只有一个模型被选中
        if (!this.isMultiSelect) {
          this.modelList.forEach(m => {
            m.selected = m === successModel;
          });
        } else {
          successModel.selected = true;
        }

        this.demoUrl = successModel.demoUrl;
        this.taskId = successModel.id;
        this.voiceName = successModel.voiceName;
      }
    },

    // 计算总体进度百分比
    calculateProgress(tasks) {
      if (!tasks || tasks.length === 0) return 0

      const completedTasks = tasks.filter(task =>
        task.status === 'unconfirmed' ||
        task.status === 'failed'
      ).length

      return Math.floor((completedTasks / tasks.length) * 100)
    },

    againVideo() {
      uni.navigateBack({
        delta: this.page_delta
      })
    },

    toggleOriginalSound() {
      if (this.isPlayingOriginal) {
        // 如果正在播放，则停止
        this.stopAudio();
      } else {
        // 如果没有播放，则开始播放
        this.playOriginalSound();
      }
    },

    toggleClonedSound(model) {
      if (this.isPlayingCloned && this.currentPlayingModel && this.currentPlayingModel.id === model.id) {
        // 如果当前正在播放这个模型的声音，则停止
        this.stopAudio();
      } else {
        // 如果没有播放或播放的是其他模型，则播放这个模型的声音
        this.playClonedSound(model);
      }
    },

    playOriginalSound() {
      if (!this.voicePath) {
        uni.showToast({
          title: '音频文件不存在',
          icon: 'none'
        })
        return
      }

      this.stopAudio()
      this.isPlayingOriginal = true
      this.isPlayingCloned = false
      this.currentPlayingModel = null

      // 每次播放创建新的音频上下文
      this.innerAudioContext = wx.createInnerAudioContext({
      useWebAudioImplement: true
    })

      // 设置播放结束事件
      this.innerAudioContext.onEnded(() => {
        this.isPlayingOriginal = false
        this.isPlayingCloned = false
        // 播放结束后销毁上下文
        if (this.innerAudioContext) {
          this.innerAudioContext.destroy()
          this.innerAudioContext = null
        }
      })

      // 设置错误处理
      this.innerAudioContext.onError((err) => {
        console.error('音频播放错误:', err)
        this.isPlayingOriginal = false
        this.isPlayingCloned = false
        // 出错时销毁上下文
        if (this.innerAudioContext) {
          this.innerAudioContext.destroy()
          this.innerAudioContext = null
        }
      })

      this.innerAudioContext.src = this.voicePath
      this.innerAudioContext.play()
    },

    playClonedSound(model) {
      // 查找对应模型的demoUrl
      if (model.status === 'failed' || model.status === 'pending') {
        return
      }
      this.stopAudio()
      this.isPlayingOriginal = false
      this.isPlayingCloned = true
      this.currentPlayingModel = model

      // 每次播放创建新的音频上下文
      this.innerAudioContext = wx.createInnerAudioContext({
      useWebAudioImplement: true
    })

      // 设置播放结束事件
      this.innerAudioContext.onEnded(() => {
        this.isPlayingOriginal = false
        this.isPlayingCloned = false
        // 播放结束后销毁上下文
        if (this.innerAudioContext) {
          this.innerAudioContext.destroy()
          this.innerAudioContext = null
        }
      })

      // 设置错误处理
      this.innerAudioContext.onError((err) => {
        console.error('音频播放:', err)
        this.isPlayingOriginal = false
        this.isPlayingCloned = false
        // 出错时销毁上下文
        if (this.innerAudioContext) {
          this.innerAudioContext.destroy()
          this.innerAudioContext = null
        }
      })

      this.innerAudioContext.src = model.demoUrl
      this.innerAudioContext.play()
    },

    stopAudio() {
      this.isPlayingOriginal = false
      this.isPlayingCloned = false
      this.currentPlayingModel = null

      // 停止播放并销毁音频上下文
      if (this.innerAudioContext) {
        this.innerAudioContext.stop()

        this.innerAudioContext.destroy()
        this.innerAudioContext = null
      }
    },

    handleUseVoice() {
      if (this.selectedModel.length === 0) {
        uni.showToast({
          title: '请至少选择一个声音模型',
          icon: 'none'
        })
        return
      }

      if (!this.voiceName || this.voiceName.trim().length === 0) {
        uni.showToast({
          title: '请填写声音名称',
          icon: 'none'
        })
        return
      }

      if (this.voiceName.length > 20) {
        uni.showToast({
          title: '声音名称不能超过20个字符',
          icon: 'none'
        })
        return
      }

      // 不再使用Storage存储数据，改用uni.$emit发送事件
      // uni.setStorageSync('soundData', {
      //   voiceTaskId: this.taskId,
      //   voiceName: this.voiceName.trim(),
      //   current: this.currentTab || 1
      // })

      // 使用与postVoiceConfirm相同的数据格式
      uni.$emit('voice_selected', this.selectedModel.map(item => {
        return {
          ...item,
          name: item.voiceName.trim(),
          type: 'save',
          version: this.type,
          id: item.id
        }
      }))

      this.$store.commit('SetNumberUserMusic', this.selectedModel[0].demoUrl)
      uni.navigateBack({
        delta: 1
      })
    },

    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },

    async handleSaveVoice() {
      if (this.loading) return

      if (this.selectedModel.length === 0) {
        uni.showToast({
          title: '请至少选择一个声音模型',
          icon: 'none'
        })
        return
      }

      if (!this.voiceName || this.voiceName.trim().length === 0) {
        uni.showToast({
          title: '请填写声音名称',
          icon: 'none'
        })
        return
      }

      if (this.voiceName.length > 20) {
        uni.showToast({
          title: '声音名称不能超过20个字符',
          icon: 'none'
        })
        return
      }

      this.loading = true
      uni.showLoading({ title: '保存中...', mask: true })

      try {
        const res = await postVoiceConfirm(this.selectedModel.map(item => {
          return {
            ...item,
            name: item.voiceName.trim(),
            type: 'save',
            version: this.type,
            id: item.id
          }
        }))

        uni.hideLoading()
        uni.showToast({
          title: '克隆成功',
          icon: 'success',
          duration: 1000
        })

        await this.delay(1000)

        switch (this.source) {
          case 'me':
            uni.switchTab({ url: '/pages/home_four/index' })
            break
          case 'genVideo':
            uni.redirectTo({ url: '/subpkg/index/get_radio/index' })
            break
          case 'me—list':
            uni.redirectTo({ url: '/subpkg/home_four/music_list' })
            break
          case 'home':
            uni.switchTab({ url: '/pages/home_four/index' })
            break
          default:
            uni.navigateBack()
            break
        }
      } catch (err) {
        console.error('保存声音失败:', err)
        uni.hideLoading()
        uni.showToast({
          title: '操作失败: ' + (err.message || '请稍后重试'),
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 生成粒子数据
    generateParticles() {
      const particles = [];
      // 创建80个粒子，减少总数量
      for (let i = 0; i < 80; i++) {
        // 生成基础随机位置
        let left, top;

        // 60%的粒子分散在左上角区域
        if (i < 48) {
          // 扩大左上角区域到0-30%，使粒子更离散
          left = Math.random() * 30;
          top = Math.random() * 30;

          // 增加随机性，使分布更不均匀
          if (Math.random() > 0.5) {
            left += Math.random() * 10;
          }
          if (Math.random() > 0.5) {
            top += Math.random() * 10;
          }
        }
        // 40%的粒子分散在其他区域
        else {
          // 计算屏幕宽高，将rpx转换为百分比
          const screenHeightRpx = 1500; // 假设屏幕高度约为1500rpx
          const startLeftPercent = 0;
          const startTopPercent = (100 / screenHeightRpx) * 100;
          const endLeftPercent = (700 / screenHeightRpx) * 100;
          const endTopPercent = (1200 / screenHeightRpx) * 100;

          // 在线上随机选一点
          const t = Math.random(); // 线上的位置参数 (0-1)

          // 线性插值计算线上的点
          const baseLeft = startLeftPercent + t * (endLeftPercent - startLeftPercent);
          const baseTop = startTopPercent + t * (endTopPercent - startTopPercent);

          // 添加较大的随机偏移，使粒子更加离散
          const offsetPercent = 12; // 增大偏移量，使粒子更离散
          left = baseLeft + (Math.random() * 2 - 1) * offsetPercent;
          top = baseTop + (Math.random() * 2 - 1) * offsetPercent;

          // 确保坐标不超出范围
          left = Math.max(0, Math.min(100, left));
          top = Math.max(0, Math.min(100, top));
        }

        particles.push({
          top: top,
          left: left,
          size: 1.5 + Math.random() * 3.5, // 稍微减小粒子大小
          opacity: 0.15 + Math.random() * 0.45, // 降低透明度
          delay: Math.random() * 10, // 增加最大延迟，使动画更不同步
          duration: 6 + Math.random() * 10 // 增加动画持续时间，使移动更慢
        });
      }
      this.particlesData = particles;
    },

    updateSelectedModelName() {
      // 查找当前选中的模型
      const selectedModel = this.modelList.find(model => model.selected);
      if (selectedModel) {
        // 更新选中模型的名称
        selectedModel.voiceName = this.voiceName.trim();
      }
    },

    // 显示训练失败原因
    showFailReason(model) {
      if (model && model.reason) {
        this.failReasonContent = model.reason;
      } else {
        this.failReasonContent = '未知原因，请重新尝试或联系客服。';
      }
      this.showFailReasonDialog = true;
    }
  }
}
</script>

<style scoped lang="scss">
// 主题变量定义
// 金色主题变量
$gold-gradient-start: #FED1B2;
$gold-gradient-mid: #F8EEE4;
$gold-gradient-end: #FED1B2;
$gold-bg-dark: #222222;
$gold-text-main: #FFFFFF;
$gold-text-accent: #FED1B2;
$gold-text-secondary: #999999;
$gold-progress-gradient: linear-gradient(90deg, #FED1B2, #F8EEE4, #FED1B2);
$gold-title-gradient: linear-gradient(46.016054774705026deg, #FED1B2 20%, #F8EEE4 50%, #FED1B2 80%);
$gold-section-gradient: linear-gradient(45.97282907448251deg, #F8EEE4 0%, #FED1B2 100%);
$gold-button-gradient: linear-gradient(136deg, #F8EEE4 0%, #FED1B2 100%);
$gold-background-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/304d06ccc6f95fd52769d6494dd79a1.png');
$gold-play-bg-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/icon-%E8%83%B6%E7%89%87.png');
$gold-model-play-bg-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/icon-%E8%83%B6%E7%89%87(%E9%87%91).png');

// 蓝绿色主题变量
$blue-gradient-start: rgba(13, 222, 241, 1);
$blue-gradient-mid: rgba(154, 247, 252, 1);
$blue-gradient-end: rgba(1, 247, 112, 1);
$blue-bg-dark: rgba(34, 34, 34, 1);
$blue-text-main: #FFFFFF;
$blue-text-accent: rgba(179, 217, 232, 1);
$blue-text-secondary: rgba(153, 153, 153, 1);
$blue-progress-gradient: linear-gradient(136deg, rgba(13, 222, 241, 1) 0, rgba(1, 247, 112, 1) 100%);
$blue-title-gradient: linear-gradient(91deg, rgba(63, 220, 255, 1) 0, rgba(190, 240, 245, 1) 49.864703%, rgba(1, 247, 112, 1) 100%);
$blue-section-gradient: linear-gradient(135deg, rgba(7, 227, 210, 1) 0, rgba(1, 247, 112, 1) 100%);
$blue-button-gradient: linear-gradient(to right, rgba(9, 236, 131, 1), rgba(9, 229, 251, 1));
$blue-background-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/1ce789cad1f1d1cd11a1ed9e6a935db.png');
$blue-model-play-bg-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/7805dd775981a33d41dc797afd4a402.png');

.page {
  min-height: 100vh;
  padding: 0 30rpx 40rpx;
  box-sizing: border-box;
  color: #fff;
  position: relative;
  overflow: hidden;

  &.theme-gold {
    // background-color: #000000;

    .particles-container {
      background-image: $gold-background-image;
    }

    .main-title {
      background-image: $gold-title-gradient;
    }

    .progress-inner {
      background: $gold-progress-gradient;
    }

    .play-btn {
      background-image: $gold-play-bg-image;
    }

    .model-play-btn {
      background-image: $gold-model-play-bg-image;
    }

    .section-title,
    .model-name {
      background-image: $gold-section-gradient;
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;

      &::before {
        background: $gold-section-gradient;
      }
    }

    .action-button-text {
      background: $gold-button-gradient;
    }

    /* 金色主题下的action-bottom-left背景 */
    .action-bottom-left {
      background-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/738d04a55c9679a627eb34dc7bccc48.png');
    }

    .recommend-tag {
      background: linear-gradient(136deg, $gold-gradient-start 20%, $gold-gradient-mid 50%, $gold-gradient-end 80%);
    }

    .model-active {
      border: 2rpx solid $gold-gradient-start;
      background-color: rgba(254, 209, 178, 0.1) !important;
    }

    /* 金色主题下的训练中状态 */
    .model-pending {
      border: 4rpx dashed rgba(254, 209, 178, 0.3);
    }

    .selected-icon {
      background-color: #fccfa6;
    }

    .loading-text {
      color: $gold-gradient-start;
    }

    /* 金色主题下的加载动画颜色 */
    .inner.one {
      border-bottom: 8rpx solid $gold-gradient-start;
    }

    .inner.two {
      border-right: 8rpx solid $gold-gradient-mid;
    }

    .inner.three {
      border-top: 8rpx solid $gold-gradient-start;
    }

    /* 金色主题下的选中发光效果 */
    .model-glow {
      animation: glowAndFadeGold 1s ease-out;
    }

    /* 金色主题下的训练完成发光效果 */
    .completion-glow {
      animation: completionGlowGold 1s ease-out forwards;
    }

    /* 金色主题下的model-description样式 */
    .model-description {
      background: #2B2318;
      color: #C8B5A6;
    }
  }

  &.theme-blue {
    // background-image: linear-gradient(180deg, rgba(34, 35, 44, 1) 0, rgba(15, 15, 15, 1) 19.70842%);

    .particles-container {
      background-image: $blue-background-image;
    }

    .main-title {
      background-image: $blue-title-gradient;
    }

    .progress-inner {
      background: $blue-progress-gradient;
    }

    .play-btn {
      background-image: $gold-play-bg-image;
    }

    .model-play-btn {
      background-image: $blue-model-play-bg-image;
    }

    .section-title {
      background-image: $blue-section-gradient;
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;

      &::before {
        background: $blue-section-gradient;
      }
    }

    .model-name {
      color: $blue-text-accent;
    }

    .action-button-text {
      background: rgba(9, 236, 131, 1);
    }

    /* 蓝色主题下的action-bottom-left背景 */
    .action-bottom-left {
      background-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/8f89931f42be39837cdbf9d685a38f2.png');
    }

    .recommend-tag {
      background: linear-gradient(137deg, rgba(9, 229, 251, 1) 19.828895%, rgba(154, 247, 252, 1) 50.069827%, rgba(9, 229, 251, 1) 80.010718%);
    }

    .model-active {
      border: 2rpx solid $blue-gradient-start;
      background-color: rgba(13, 222, 241, 0.1) !important;
    }

    /* 蓝色主题下的训练中状态 */
    .model-pending {
      border: 4rpx dashed rgba(13, 222, 241, 0.3);
    }

    .selected-icon {
      background-color: rgba(9, 229, 251, 0.8);
    }

    .loading-text {
      color: $blue-gradient-start;
    }

    /* 蓝色主题下的加载动画颜色 */
    .inner.one {
      border-bottom: 8rpx solid $blue-gradient-start;
    }

    .inner.two {
      border-right: 8rpx solid $blue-gradient-mid;
    }

    .inner.three {
      border-top: 8rpx solid $blue-gradient-end;
    }

    /* 蓝色主题下的选中发光效果 */
    .model-glow {
      animation: glowAndFadeBlue 1s ease-out;
    }

    /* 蓝色主题下的训练完成发光效果 */
    .completion-glow {
      animation: completionGlowBlue 1s ease-out forwards;
    }

    /* 蓝色主题下的model-description样式 */
    .model-description {
      background: #1C2B28;
      color: #C1D1D0;
    }
  }

  /* 粒子容器 */
  .particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    /* 确保低于其他UI元素 */
    pointer-events: none;
    /* 不阻止用户交互 */
  }

  /* 粒子样式 */
  .particle {
    position: absolute;
    background-color: #fff;
    border-radius: 50%;
    animation: float 5s infinite linear;
  }

  /* 粒子漂浮动画 */
  @keyframes float {
    0% {
      transform: translate(0, 0) scale(1);
      opacity: 0;
    }

    25% {
      opacity: 0.5;
    }

    50% {
      transform: translate(120rpx, 120rpx) scale(1.2);
      opacity: 1;
    }

    75% {
      opacity: 0.5;
    }

    100% {
      transform: translate(250rpx, 250rpx) scale(1);
      opacity: 0;
    }
  }

  .header {
    .title-area {
      display: flex;
      justify-content: space-between;
      align-items: center;


      .main-title {
        font-size: 38rpx;
        font-weight: 600;
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
      }

      .reset-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10rpx;
        font-weight: 500;
        font-size: 28rpx;
        color: #999999;
      }
    }
  }
}

/* 进度条 */
.progress-bar {
  height: 8rpx;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4rpx;
  overflow: hidden;
  margin: 20rpx 0 50rpx;

  .progress-inner {
    height: 100%;
    border-radius: 4rpx;
    transition: width 0.3s ease-in-out;
  }
}

/* 原始声音播放区域 */
.audio-section {
  margin: 60rpx 0;

  .audio-box {
    border-radius: 24rpx;
    display: flex;
    align-items: center;
    padding: 32rpx 48rpx;
    background: #222222;


    .play-btn {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 20rpx;
      background-size: 100% 100%;
      background-repeat: no-repeat;

      image {
        width: 40rpx;
        height: 40rpx;
        margin-left: 8rpx;
      }
    }

    .audio-text {
      font-size: 32rpx;
    }
  }
}

/* 选择克隆声音区域 */
.clone-section {
  margin-bottom: 60rpx;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      position: relative;
      padding-left: 16rpx;
      display: flex;
      align-items: center;

      &::before {
        content: "";
        width: 4rpx;
        height: 24rpx;
        border-radius: 2rpx;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .section-action {
      width: 300rpx;
      height: 50rpx;
      font-size: 28rpx;
      color: #999;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }

  .model-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 20rpx;

    .model-item-box {
      display: flex;
      flex-direction: column;
      align-items: center;

      .model-item {
        background: #222222;
        border-radius: 24rpx;
        height: 232rpx;
        width: 100%;
        padding: 20rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        border-radius: 24rpx;
        box-sizing: border-box;
        transition: border-color 0.3s, background-color 0.3s;

        /* 训练中状态 */
        &.model-pending {
          opacity: 0.7;
        }

        /* 训练失败状态 */
        &.model-failed {
          opacity: 0.7;
          border: 2rpx dashed rgba(255, 82, 82, 0.3);
        }

        .model-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;

          /* 3D加载动画 */
          .loader {
            position: relative;
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
            perspective: 800rpx;
            margin-bottom: 16rpx;
          }

          .inner {
            position: absolute;
            box-sizing: border-box;
            width: 100%;
            height: 100%;
            border-radius: 50%;
          }

          .inner.one {
            left: 0%;
            top: 0%;
            animation: rotate-one 1s linear infinite;
            // border-bottom: 3rpx solid var(--accent-color, #FED1B2);
          }

          .inner.two {
            right: 0%;
            top: 0%;
            animation: rotate-two 1s linear infinite;
            // border-right: 3rpx solid var(--accent-color-mid, #F8EEE4);
          }

          .inner.three {
            right: 0%;
            bottom: 0%;
            animation: rotate-three 1s linear infinite;
            // border-top: 3rpx solid var(--accent-color, #FED1B2);
          }

          @keyframes rotate-one {
            0% {
              transform: rotateX(35deg) rotateY(-45deg) rotateZ(0deg);
            }

            100% {
              transform: rotateX(35deg) rotateY(-45deg) rotateZ(360deg);
            }
          }

          @keyframes rotate-two {
            0% {
              transform: rotateX(50deg) rotateY(10deg) rotateZ(0deg);
            }

            100% {
              transform: rotateX(50deg) rotateY(10deg) rotateZ(360deg);
            }
          }

          @keyframes rotate-three {
            0% {
              transform: rotateX(35deg) rotateY(55deg) rotateZ(0deg);
            }

            100% {
              transform: rotateX(35deg) rotateY(55deg) rotateZ(360deg);
            }
          }

          .loading-text {
            margin-top: 10rpx;
            font-size: 24rpx;
            opacity: 0.8;
          }
        }

        .model-failed-icon {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;

          .failed-text {
            margin-top: 20rpx;
            font-size: 24rpx;
            color: #ff5252;
          }

          .view-reason-btn {
            margin-top: 20rpx;
            font-size: 20rpx;
            color: #ffffff;
            background-color: rgba(255, 82, 82, 0.7);
            padding: 8rpx 16rpx;
            border-radius: 12rpx;
          }
        }

        .recommend-tag {
          position: absolute;
          top: -20rpx;
          left: 0;
          width: 104rpx;
          height: 40rpx;
          border-radius: 10rpx 12rpx 12rpx 2rpx;
          font-weight: 500;
          font-size: 20rpx;
          color: #333333;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10;
        }

        /* 选中图标样式 */
        .selected-icon {
          position: absolute;
          right: -2rpx;
          bottom: -2rpx;
          width: 40rpx;
          height: 40rpx;
          z-index: 10;
          border-radius: 16rpx 4rpx 24rpx 4rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          image {
            width: 30rpx;
            height: 30rpx;
          }
        }

        /* 发光效果 */
        .model-glow {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border-radius: 24rpx;
          pointer-events: none;
          z-index: 5;
        }

        .model-play-btn {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 20rpx;
          margin-top: 40rpx;
          background-size: 100% 100%;
          background-repeat: no-repeat;

          image {
            width: 40rpx;
            height: 40rpx;
            margin-left: 8rpx;
          }
        }

        .model-name {
          font-weight: 600;
          font-size: 24rpx;
        }

        /* 训练完成发光提醒效果 */
        .completion-glow {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border-radius: 24rpx;
          pointer-events: none;
          z-index: 6;
        }
      }

      .edit-name {
        display: flex;
        align-items: center;
        margin-top: 24rpx;
        color: #999999;
        width: 100%;
        justify-content: center;
        font-weight: 500;
        font-size: 24rpx;

        image {
          width: 24rpx;
          height: 24rpx;
          gap: 8rpx;
        }
      }
    }
  }

  .model-description {
    margin-top: 20rpx;
    padding: 24rpx;
    border-radius: 24rpx;
    box-sizing: border-box;
    font-weight: 500;
    font-size: 24rpx;
  }
}

/* 声音名称区域 */
.name-section {
  margin-bottom: 100rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    margin-bottom: 20rpx;
    display: block;
    position: relative;
    padding-left: 16rpx;
    display: flex;
    align-items: center;

    &::before {
      content: "";
      width: 4rpx;
      height: 24rpx;
      border-radius: 2rpx;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .input-wrapper {
    background: #222222;
    border-radius: 24rpx;
    padding: 24rpx 32rpx;

    ::v-deep .u-input__content__field-wrapper__field {
      color: #FFFFFF !important;
      font-size: 28rpx !important;
    }

    .name-input {
      padding: 20rpx !important;
      background-color: transparent !important;
    }
  }
}

/* 底部按钮区域 */
.action-bottom {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  bottom: calc(30rpx + env(safe-area-inset-bottom));
  left: 50%;
  transform: translateX(-50%);
  width: 690rpx;
  gap: 20rpx;

  .action-bottom-left {
    width: 96rpx;
    height: 96rpx;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10rpx;

    .action-bottom-left-num {
      font-weight: 600;
      font-size: 28rpx;
      color: #FFFFFF;
    }

    .action-bottom-left-text {
      font-weight: 500;
      font-size: 20rpx;
      color: #FFFFFF;
    }
  }

  .action-button-text {
    flex: 1;
    border-radius: 16rpx;
    height: 96rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .action-bottom-num-text {
      font-size: 26rpx;
      color: #a0a0a0;
    }

    .action-bottom-save-text {
      font-size: 36rpx;
      color: #333333;
      font-weight: 600;
    }

    &.button-disabled {
      opacity: 0.6;
      background: linear-gradient(136deg, #E0D6CE 0%, #D9C0AD 100%);
    }
  }
}


/* 重命名弹窗样式 */
.rename-content {
  width: 654rpx;
  background-color: #ffffff;
  border-radius: 32rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 32rpx 0;
}

.rename-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  text-align: center;
  padding: 16rpx 0;
}

.rename-input-wrap {
  padding: 24rpx 32rpx;
}

.rename-input {
  width: 100%;
  height: 80rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.rename-buttons {
  display: flex;
  gap: 32rpx;
  margin: 32rpx auto;
}

.rename-button {
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 600;
  font-size: 32rpx;
  width: 264rpx;
  height: 88rpx;
  border-radius: 16rpx;

}

.cancel {
  color: #333333;
  background: #F3F5F8;
}

.confirm {
  color: #FFFFFF;
  background: linear-gradient(135deg, #22232C 20%, #0F0F0F 100%);
}

/* 主题切换按钮 */
.theme-toggle {
  position: fixed;
  top: 120rpx;
  right: 30rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
  z-index: 100;
  font-size: 24rpx;
}

/* 覆盖原有样式 */
::v-deep .u-input__content__field-wrapper {
  background-color: transparent !important;
}

::v-deep .u-input__content__field-wrapper__field {
  color: #fff !important;
}

/* 金色主题的发光动画 */
@keyframes glowAndFadeGold {
  0% {
    box-shadow: 0 0 30rpx 15rpx rgba(254, 209, 178, 0.8);
    background-color: rgba(254, 209, 178, 0.4);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(254, 209, 178, 0);
    background-color: rgba(254, 209, 178, 0);
  }
}

@keyframes completionGlowGold {
  0% {
    box-shadow: inset 0 0 5rpx 2rpx rgba(254, 209, 178, 0);
    background-color: rgba(254, 209, 178, 0);
  }

  50% {
    box-shadow: inset 0 0 3rpx 1rpx rgba(254, 209, 178, 0.3), 0 0 10rpx 5rpx rgba(254, 209, 178, 0.4);
    background-color: rgba(254, 209, 178, 0.15);
  }

  100% {
    box-shadow: inset 0 0 0 0 rgba(254, 209, 178, 0), 0 0 0 0 rgba(254, 209, 178, 0);
    background-color: rgba(254, 209, 178, 0);
  }
}

/* 蓝色主题的发光动画 */
@keyframes glowAndFadeBlue {
  0% {
    box-shadow: 0 0 30rpx 15rpx rgba(13, 222, 241, 0.8);
    background-color: rgba(13, 222, 241, 0.4);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(13, 222, 241, 0);
    background-color: rgba(13, 222, 241, 0);
  }
}

@keyframes completionGlowBlue {
  0% {
    box-shadow: inset 0 0 5rpx 2rpx rgba(13, 222, 241, 0);
    background-color: rgba(13, 222, 241, 0);
  }

  50% {
    box-shadow: inset 0 0 3rpx 1rpx rgba(13, 222, 241, 0.3), 0 0 10rpx 5rpx rgba(13, 222, 241, 0.4);
    background-color: rgba(13, 222, 241, 0.15);
  }

  100% {
    box-shadow: inset 0 0 0 0 rgba(13, 222, 241, 0), 0 0 0 0 rgba(13, 222, 241, 0);
    background-color: rgba(13, 222, 241, 0);
  }
}

/* 播放GIF样式 */
.playing-gif {
  width: 40rpx !important;
  height: 40rpx !important;
  margin: 0 !important;
}

.model-play-btn .playing-gif {
  width: 48rpx !important;
  height: 48rpx !important;
}
</style>
