<template>
	<view class="channel-page">
		<custom-navbar title="频道" :showBackBtn="false"
			background="linear-gradient(6deg, rgba(255,255,255,0) 18%, rgba(222,249,255,1) 100%)" />

		<!-- 已加入频道区域 -->
		<view class="section-title">我加入的</view>

		<template v-if="isLoggedIn">
			<!-- 骨架屏 - 已加入频道 -->
			<view class="channel-list" v-if="loading">
				<view v-for="i in 3" :key="'skeleton-joined-' + i" class="channel-item skeleton-item">
					<view class="skeleton-icon skeleton-animate"></view>
					<view class="skeleton-info">
						<view class="skeleton-title skeleton-animate"></view>
						<view class="skeleton-desc skeleton-animate"></view>
					</view>
				</view>
			</view>

			<!-- 实际内容 -->
			<view class="channel-list" v-else-if="joinedChannels.length > 0">
				<view v-for="(channel, index) in joinedChannels" :key="index" class="channel-item"
					@click="navigateToChannelDetail(channel)">
					<image :src="channel.icon" class="channel-icon"></image>
					<view class="channel-info">
						<view class="channel-name">{{ channel.title }}</view>
						<view class="channel-description">{{ channel.description }}</view>
					</view>
				</view>
			</view>

			<view class="empty-tip" v-else>
				<view class="empty-title">你还没有加入任何频道</view>
				<view class="empty-subtitle">(请联系管理员邀请你加入吧)</view>
			</view>
		</template>

		<view v-else class="module-login-prompt">
			<view class="empty-title">登录后查看已加入的频道</view>
			<button class="login-btn" @click="handleLoginButtonClick">登录</button>
		</view>

		<!-- 推荐频道区域 -->
		<view class="section-title">推荐频道</view>

		<template v-if="isLoggedIn">
			<!-- 骨架屏 - 推荐频道 -->
			<view class="channel-list" v-if="loading">
				<view v-for="i in 2" :key="'skeleton-recommend-' + i" class="channel-item skeleton-item">
					<view class="skeleton-icon skeleton-animate"></view>
					<view class="skeleton-info">
						<view class="skeleton-title skeleton-animate"></view>
						<view class="skeleton-desc skeleton-animate"></view>
					</view>
				</view>
			</view>

			<!-- 实际内容 -->
			<view class="channel-list" v-else-if="recommendedChannels.length > 0">
				<view v-for="(channel, index) in recommendedChannels" :key="index" class="channel-item"
					@click="navigateToChannelDetail(channel)">
					<image :src="channel.icon" class="channel-icon"></image>
					<view class="channel-info">
						<view class="channel-name">{{ channel.title }}</view>
						<view class="channel-description">{{ channel.description }}</view>
					</view>
				</view>
			</view>

			<view class="empty-tip" v-else>
				<view class="empty-title">还没有推荐频道</view>
				<view class="empty-subtitle">(请联系管理员添加)</view>
			</view>
		</template>

		<view v-else class="module-login-prompt">
			<view class="empty-title">登录后查看推荐频道</view>
			<button class="login-btn" @click="handleLoginButtonClick">登录</button>
		</view>

		<TabBar :index="2" active-color="black" custom-style="background:white" />
		<globalLoginModal />
		<!-- 通知栏目 -->
    <pageTitle />
	</view>
</template>

<script>
import { getGroup } from '../../api/numberUser/copy.js'
import { mapGetters } from 'vuex'
import TabBar from '@/components/finalTabBar/index.vue'
export default {
	data() {
		return {
			loading: true, // 加载状态
			channelData: [],
			joinedChannels: [],
			recommendedChannels: []
		}
	},
	components: {
		TabBar
	},
	computed: {
		...mapGetters('user', ['isLogin', 'userInfo']),
		isLoggedIn() {
			return this.isLogin;
		}
	},
	onShow() {
		// 设置当前选中索引
		this.$store.commit('fitment/SET_CURRENT_TAB_INDEX', 2)
	},
	methods: {
		/**
		 * 导航到频道详情页
		 * @param {Object} channel 频道数据
		 */
		navigateToChannelDetail(channel) {
			uni.navigateTo({
				url: '/subpkg/home_three/message',
				success(res) {
					res.eventChannel.emit('get_message', channel)
				}
			})
		},

		/**
		 * 处理登录按钮点击
		 */
		handleLoginButtonClick() {
			this.$showLogin({
				success: () => {
					// 登录成功后加载数据
					this.loading = true;
					this.fetchChannelData();
				}
			});
		},

		/**
		 * 获取频道数据
		 */
		fetchChannelData() {
			this.loading = true;
			getGroup().then(res => {
				this.channelData = res.data;

				// 过滤已加入的频道
				this.joinedChannels = this.channelData.filter(item => {
					return item.joined;
				});

				// 过滤推荐的频道（平台频道且未加入）
				this.recommendedChannels = this.channelData.filter(item => {
					return (item.tenantId == 0 || item.publicStatus == 0) && !item.joined;
				});

				// 数据加载完成，关闭骨架屏
				this.loading = false;
			}).catch(err => {
				console.error('获取频道数据失败', err);
				this.loading = false;
			});
		}
	},
	onLoad() {
		uni.hideTabBar()
		// 判断登录状态，已登录则加载数据
		if (this.isLoggedIn) {
			this.loading = true;
			this.fetchChannelData();
		} else {
			this.loading = false;
		}
	}
}
</script>

<style scoped lang="scss">
.channel-page {
	min-height: 100vh;
	padding-bottom: 100rpx;
	background: #f7f8fc;
	padding: 20rpx;
	box-sizing: border-box;
}

.section-title {
	font-size: 34rpx;
	font-weight: 600;
	margin: 20rpx 0;
}

.channel-list {
	font-size: 28rpx;
	// padding: 20rpx 40rpx;
	border-radius: 20rpx;
	background: #f7f8fc;
	margin-bottom: 20rpx;

	.channel-item {
		display: flex;
		align-items: center;
		background: white;
		padding: 20rpx 30rpx;
		margin-bottom: 20rpx;
		border-radius: 20rpx;

		&.skeleton-item {
			height: 140rpx;
		}
	}

	.channel-icon {
		width: 100rpx;
		height: 100rpx;
		margin-right: 20rpx;
		border-radius: 10rpx;
	}

	.channel-info {
		flex: 1;
	}

	.channel-name {
		font-weight: 600;
		margin-bottom: 10rpx;
	}

	.channel-description {
		color: rgb(161, 162, 165);
		width: 60vw;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	// 骨架屏样式
	.skeleton-icon {
		width: 100rpx;
		height: 100rpx;
		margin-right: 20rpx;
		border-radius: 10rpx;
		background: #EEEEEE;
	}

	.skeleton-info {
		flex: 1;
	}

	.skeleton-title {
		height: 36rpx;
		width: 200rpx;
		background: #EEEEEE;
		border-radius: 4rpx;
		margin-bottom: 16rpx;
	}

	.skeleton-desc {
		height: 28rpx;
		width: 80%;
		background: #EEEEEE;
		border-radius: 4rpx;
	}
}

// 骨架屏动画
.skeleton-animate {
	position: relative;
	overflow: hidden;

	&::after {
		content: '';
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		transform: translateX(-100%);
		background: linear-gradient(90deg,
				rgba(255, 255, 255, 0) 0%,
				rgba(255, 255, 255, 0.3) 50%,
				rgba(255, 255, 255, 0) 100%);
		animation: shimmer 1.5s infinite;
	}
}

@keyframes shimmer {
	100% {
		transform: translateX(100%);
	}
}

.empty-tip {
	text-align: center;
	margin: 100rpx 0;

	.empty-title {
		font-weight: 600;
		font-size: 30rpx;
	}

	.empty-subtitle {
		font-size: 24rpx;
		color: rgb(161, 162, 165);
	}
}

.module-login-prompt {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: white;
	border-radius: 20rpx;
	padding: 40rpx 0;
	margin-bottom: 30rpx;

	.empty-title {
		font-weight: 600;
		font-size: 30rpx;
		margin-bottom: 30rpx;
		color: #666;
	}

	.login-btn {
		width: 200rpx;
		height: 70rpx;
		line-height: 70rpx;
		background-color: #000;
		color: #fff;
		border-radius: 35rpx;
		font-size: 28rpx;
	}
}

.login-prompt {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin-top: 200rpx;

	.login-prompt-image {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 30rpx;
	}

	.login-prompt-text {
		font-size: 30rpx;
		color: #666;
		margin-bottom: 40rpx;
	}

	.login-btn {
		width: 300rpx;
		height: 80rpx;
		line-height: 80rpx;
		background-color: #000;
		color: #fff;
		border-radius: 40rpx;
		font-size: 28rpx;
	}
}
</style>
