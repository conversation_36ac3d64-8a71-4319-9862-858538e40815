{"name": "test", "version": "1.0.0", "lockfileVersion": 1, "requires": true, "dependencies": {"@interactjs/actions": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/actions/-/actions-1.10.2.tgz", "integrity": "sha512-BHJcW84WCMf/LsKmha/1Yog7aH3+QBXbLvowvZvwYvgjdUIb3xCa1a7FUYXuWAeKNMyKPVjFun+WPce75B+1tA==", "requires": {"@interactjs/interact": "1.10.2"}}, "@interactjs/arrange": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/arrange/-/arrange-1.10.2.tgz", "integrity": "sha512-pPLA9o4RWMFN0VfalklOFSRLL4WqqXcD9no4XEuqV00goZPCxLBbMTztaWwnutlRy7evtOhUjUH+pZVsS+dZ4Q=="}, "@interactjs/auto-scroll": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/auto-scroll/-/auto-scroll-1.10.2.tgz", "integrity": "sha512-yYqzOawwvWd1NNnlqZdzrXoOMFafQ2/ws85erpJqdaNMQE221z2uP+QYhFRLQRgYUlTbHFfmjDpzhuJgq4uA8Q==", "requires": {"@interactjs/interact": "1.10.2"}}, "@interactjs/auto-start": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/auto-start/-/auto-start-1.10.2.tgz", "integrity": "sha512-nZudj8VzJzz+uEyDHqXwtKpvUYr+Oj1+xBrJEu21CywroHQWM2J4fCIiCgeCo3d5/p/TrzFk5b+YfAWePKiLxA==", "requires": {"@interactjs/interact": "1.10.2"}}, "@interactjs/clone": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/clone/-/clone-1.10.2.tgz", "integrity": "sha512-XzA8BRHSCwvysOegZ1kopg+IJF3erh4qzY6DRoZsIJovKAXawoa176E58IAzDbgYPJ2yoaSGT+XyzT2C0wa3pQ=="}, "@interactjs/core": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/core/-/core-1.10.2.tgz", "integrity": "sha512-SA5KRGo+gFJOhBj1Z2dLHhAf0/2nyHNd4SQ460aIQ3jj/QhqbJW6kGzmh7hBa2FzVGgxLhcQu7NZaP4rnDfUNw=="}, "@interactjs/dev-tools": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/dev-tools/-/dev-tools-1.10.2.tgz", "integrity": "sha512-aAd9NgTAGA3yVdFCYcAAYrM4TYQFuVqEvsF+xj+g5SlGyrJ7+GTjPZ2rScOyAsABY4Tz64L2pXvWmXMG87dncA==", "requires": {"@interactjs/interact": "1.10.2", "@interactjs/utils": "1.10.2"}}, "@interactjs/feedback": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/feedback/-/feedback-1.10.2.tgz", "integrity": "sha512-XlcoICGrFeUwwRtDgOpstOOvlU42WZoEg7gJHK3LwF7j0IctPd1+3blXofFlBeVvodle8MvUMepm5CRXz741fA=="}, "@interactjs/inertia": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/inertia/-/inertia-1.10.2.tgz", "integrity": "sha512-ZmN1joN6J36Q6SOp3V0iZOisXZOBMSAUj0STo8wbwCKy7K8IrC9vjUBbO2JM52cT6o7hg5ebHsp5c8FrebSHlg==", "requires": {"@interactjs/interact": "1.10.2", "@interactjs/offset": "1.10.2"}}, "@interactjs/interact": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/interact/-/interact-1.10.2.tgz", "integrity": "sha512-Ms5uVCY9IobVYpQyBnBdkP6Bk6iDY7TkC7GupsdUPUxzAvYSQCTEAGr/1PwxSrSS6dN/8O8TuyUWPbCaylr/JA==", "requires": {"@interactjs/core": "1.10.2", "@interactjs/types": "1.10.2", "@interactjs/utils": "1.10.2"}}, "@interactjs/interactjs": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/interactjs/-/interactjs-1.10.2.tgz", "integrity": "sha512-OwLl70af6lfZOOg/bvWKSNm1DS1nDI72QnzDYljSKfc2D8stqLIGDO1wPY2rhZudUG5q3t50EhmMUQF76yll/g==", "requires": {"@interactjs/actions": "1.10.2", "@interactjs/arrange": "1.10.2", "@interactjs/auto-scroll": "1.10.2", "@interactjs/auto-start": "1.10.2", "@interactjs/clone": "1.10.2", "@interactjs/core": "1.10.2", "@interactjs/dev-tools": "1.10.2", "@interactjs/feedback": "1.10.2", "@interactjs/inertia": "1.10.2", "@interactjs/interact": "1.10.2", "@interactjs/modifiers": "1.10.2", "@interactjs/multi-target": "1.10.2", "@interactjs/offset": "1.10.2", "@interactjs/pointer-events": "1.10.2", "@interactjs/react": "1.10.2", "@interactjs/reflow": "1.10.2", "@interactjs/utils": "1.10.2", "@interactjs/vue": "1.10.2"}}, "@interactjs/modifiers": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/modifiers/-/modifiers-1.10.2.tgz", "integrity": "sha512-3wYEucvZF2NTIslnVIKw5MWhkn9LM42cGCQaC19o3LZeWnbps7NnHJCyQp6zylJrCbwt7f+CSt4Oj2/s0f6XEA==", "requires": {"@interactjs/interact": "1.10.2", "@interactjs/snappers": "1.10.2"}}, "@interactjs/multi-target": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/multi-target/-/multi-target-1.10.2.tgz", "integrity": "sha512-O2GiIqgZBzjAVTOpL8doTnAcM9AtM3+H/Bb+An12wWKtNutVK7JbqUAO2nYueOk55/PP3yDLY9Qdr15RJns3lQ=="}, "@interactjs/offset": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/offset/-/offset-1.10.2.tgz", "integrity": "sha512-xLgQqinFUY7ZqSX9d9on7XRcxvQdHNEAktj2QFwxMsEwrA6zbKROpPVwt8WQ1yBAeJSFjgYGcmCMPW5K41dT0w==", "requires": {"@interactjs/interact": "1.10.2"}}, "@interactjs/pointer-events": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/pointer-events/-/pointer-events-1.10.2.tgz", "integrity": "sha512-O8s3N399hkGIzWGlcJVy0LJyDn5YWDh6XKjyowh/QivtlZSWPY8eglmlN2uZX0lmiqUYghbKI4CpQYP/cE0ZDA==", "requires": {"@interactjs/interact": "1.10.2"}}, "@interactjs/react": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/react/-/react-1.10.2.tgz", "integrity": "sha512-JXzPdANft+W2vq3SCSzprCwom5UuC8TaiAAhVdt8R+/P6xHbOeAX93XLS5YmDwT8e0Zh9J9jYvz55tkTdwjFZQ=="}, "@interactjs/reflow": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/reflow/-/reflow-1.10.2.tgz", "integrity": "sha512-pc6o6RRhSCYQC4auZexRb7z5FQkdSVev5HzlRfUAjfw4C076qgbcs63ESRKy4YXdSBtUTvARQZxpuWUNGquzJw==", "requires": {"@interactjs/interact": "1.10.2"}}, "@interactjs/snappers": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/snappers/-/snappers-1.10.2.tgz", "integrity": "sha512-wQ41Vn5GRn6VavjIEUtTkd9d5QgdKgC4+CPW0fjKkiQclLBmaic7VibNETO8twN0Jx5e73EoPj9K2nAVHIA0hA=="}, "@interactjs/types": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/types/-/types-1.10.2.tgz", "integrity": "sha512-l0T1bU8OHRv716ztQOYwP+K7b/lA76C0T3r/cdabbUv6CKeAFdFRRFlmNxYOM36SxMGWAiq5VWrN3SeXlB7Fow=="}, "@interactjs/utils": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/utils/-/utils-1.10.2.tgz", "integrity": "sha512-sOr+pu7XGAN4qv+ikajMo3RJygbkbMLegmx0Tv5Qf6e80sF42FjkmHeMGuV7fL98nwat0VmDiWerOFBnKctXow=="}, "@interactjs/vue": {"version": "1.10.2", "resolved": "https://registry.npmmirror.com/@interactjs/vue/-/vue-1.10.2.tgz", "integrity": "sha512-msLdc42DFsCPQZt6YBGZrw8Ro23kQcNnj+iLz2OUQcOrp/lma7WjorUuAwwfyFna2DevLtiYlMLbT0dpO/cVhg=="}, "base64-arraybuffer": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/base64-arraybuffer/-/base64-arraybuffer-1.0.2.tgz", "integrity": "sha512-I3yl4r9QB5ZRY3XuJVEPfc2XhZO6YweFPI+UovAzn+8/hb3oJ6lnysaFcjVpkCPfVWFUDvoZ8kmVDP7WyRtYtQ==", "dev": true}, "batch-processor": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/batch-processor/-/batch-processor-1.0.0.tgz", "integrity": "sha512-xoLQD8gmmR32MeuBHgH0Tzd5PuSZx71ZsbhVxOCRbgktZEPe4SQy7s9Z50uPp0F/f7iw2XmkHN2xkgbMfckMDA=="}, "cos-wx-sdk-v5": {"version": "1.7.2", "requires": {"fast-xml-parser": "^4.4.0", "mime": "^2.4.6"}}, "css-line-break": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/css-line-break/-/css-line-break-2.1.0.tgz", "integrity": "sha512-FHcKFCZcAha3LwfVBhCQbW2nCNbkZXn7KVUJcsT5/P8YmfsVja0FMPJr0B903j/E69HUphKiV9iQArX8SDYA4w==", "dev": true, "requires": {"utrie": "^1.0.2"}}, "deepmerge": {"version": "4.3.1", "resolved": "https://registry.npmmirror.com/deepmerge/-/deepmerge-4.3.1.tgz", "integrity": "sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A=="}, "element-resize-detector": {"version": "1.2.4", "resolved": "https://registry.npmmirror.com/element-resize-detector/-/element-resize-detector-1.2.4.tgz", "integrity": "sha512-Fl5Ftk6WwXE0wqCgNoseKWndjzZlDCwuPTcoVZfCP9R3EHQF8qUtr3YUPNETegRBOKqQKPW3n4kiIWngGi8tKg==", "requires": {"batch-processor": "1.0.0"}}, "eventemitter3": {"version": "4.0.7", "resolved": "https://registry.npmmirror.com/eventemitter3/-/eventemitter3-4.0.7.tgz", "integrity": "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw=="}, "fast-xml-parser": {"version": "4.5.3", "resolved": "https://registry.npmmirror.com/fast-xml-parser/-/fast-xml-parser-4.5.3.tgz", "integrity": "sha512-R<PERSON>ihhV+SHsIUGXObeVy9AXiBbFwkVk7Syp8XgwN5U3JV416+Gwp/GO9i0JYKmikykgz/UHRrrV4ROuZEo/T0ig==", "requires": {"strnum": "^1.1.1"}}, "html2canvas": {"version": "1.4.1", "dev": true, "requires": {"css-line-break": "^2.1.0", "text-segmentation": "^1.0.3"}}, "mime": {"version": "2.6.0", "resolved": "https://registry.npmmirror.com/mime/-/mime-2.6.0.tgz", "integrity": "sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg=="}, "shvl": {"version": "2.0.3", "resolved": "https://registry.npmmirror.com/shvl/-/shvl-2.0.3.tgz", "integrity": "sha512-V7C6S9Hlol6SzOJPnQ7qzOVEWUQImt3BNmmzh40wObhla3XOYMe4gGiYzLrJd5TFa+cI2f9LKIRJTTKZSTbWgw=="}, "strnum": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/strnum/-/strnum-1.1.2.tgz", "integrity": "sha512-vrN+B7DBIoTTZjnPNewwhx6cBA/H+IS7rfW68n7XxC1y7uoiGQBxaKzqucGUgavX15dJgiGztLJ8vxuEzwqBdA=="}, "text-segmentation": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/text-segmentation/-/text-segmentation-1.0.3.tgz", "integrity": "sha512-iOiPUo/BGnZ6+54OsWxZidGCsdU8YbE4PSpdPinp7DeMtUJNJBoJ/ouUSTJjHkh1KntHaltHl/gDs2FC4i5+Nw==", "dev": true, "requires": {"utrie": "^1.0.2"}}, "utrie": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/utrie/-/utrie-1.0.2.tgz", "integrity": "sha512-1MLa5ouZiOmQzUbjbu9VmjLzn1QLXBhwpUa7kdLUQK+KQ5KA9I1vk5U4YHe/X2Ch7PYnJfWuWT+VbuxbGwljhw==", "dev": true, "requires": {"base64-arraybuffer": "^1.0.2"}}, "vue-grid-layout": {"version": "2.4.0", "resolved": "https://registry.npmmirror.com/vue-grid-layout/-/vue-grid-layout-2.4.0.tgz", "integrity": "sha512-MRQVt1BdWDaPN4gKGEKOVVwiTyucqJhrGEyjiY9Muor+dzFFq4Hm0geSpYJpLvC1GLlTL8KWUwy0suKrHm+mqg==", "requires": {"@interactjs/actions": "1.10.2", "@interactjs/auto-scroll": "1.10.2", "@interactjs/auto-start": "1.10.2", "@interactjs/dev-tools": "1.10.2", "@interactjs/interactjs": "1.10.2", "@interactjs/modifiers": "1.10.2", "element-resize-detector": "^1.2.1"}}, "vuex-persistedstate": {"version": "4.1.0", "requires": {"deepmerge": "^4.2.2", "shvl": "^2.0.3"}}, "widget-ui": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/widget-ui/-/widget-ui-1.0.2.tgz", "integrity": "sha512-gDXosr5mflJdMA1weU1A47aTsTFfMJhfA4EKgO5XFebY3eVklf80KD4GODfrjo8J2WQ+9YjL1Rd9UUmKIzhShw==", "requires": {"eventemitter3": "^4.0.0"}}, "wxml-to-canvas": {"version": "1.1.1", "requires": {"widget-ui": "^1.0.2"}}}}