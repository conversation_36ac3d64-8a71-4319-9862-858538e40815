<template>
  <view class="custom-tabbar" :style="[tabbarStyle]">
    <view v-for="(item, index) in tabList" :key="item.id || index" class="tab-item"
      :class="{ 'active': currentIndex === index }" @click="() => handleTabClick(item, index)">
      <view class="tab-icon">
        <image v-if="item.icon" :src="currentIndex === index ? (item.activeIcon || item.icon) : item.icon"
          class="icon-image" mode="aspectFit" />
        <view v-else class="icon-placeholder" :class="{ 'active': currentIndex === index }">
          {{ item.type.charAt(0) }}
        </view>
      </view>

      <text class="tab-text" :style="[getTabTextStyle(index)]">
        {{ item.type }}
      </text>

      <!-- 红点提示 -->
      <view v-if="item.badge && item.badge > 0" class="tab-badge">
        <text class="badge-text">
          {{ item.badge > 99 ? '99+' : item.badge }}
        </text>
      </view>

      <!-- 小红点 -->
      <view v-else-if="item.dot" class="tab-dot"></view>
    </view>
  </view>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex'

export default {
  name: 'TabBar',
  props: {
    // 样式配置
    style: {
      type: Object,
      default: () => ({})
    },

    // 是否显示边框
    border: {
      type: Boolean,
      default: true
    },

    // 高度
    height: {
      type: String,
      default: '50px'
    },

    // 是否固定在底部
    fixed: {
      type: Boolean,
      default: true
    },

    // z-index
    zIndex: {
      type: Number,
      default: 1000
    }
  },
  mounted() {
    console.log('sadasdas');

  },
  computed: {
    // 从Vuex获取状态
    ...mapState('fitment', ['currentTabIndex']),
    ...mapGetters('fitment', ['tabList', 'navigationStyle']),

    // 文字颜色
    textColor() {
      return this.navigationStyle.textColor || '#666666'
    },

    // 选中文字颜色
    activeTextColor() {
      return this.navigationStyle.activeTextColor || '#21BD74'
    },

    // 背景颜色
    backgroundColor() {
      return this.navigationStyle.backgroundColor || '#FFFFFF'
    },

    // 文字大小
    fontSize() {
      return this.navigationStyle.fontSize || 12
    },

    // 当前选中索引
    currentIndex() {
      return this.currentTabIndex
    },

    // 计算tabbar样式
    tabbarStyle() {
      const style = {
        backgroundColor: this.backgroundColor,
        height: this.height,
        zIndex: this.zIndex,
        ...this.style
      }

      if (this.fixed) {
        style.position = 'fixed'
        style.bottom = '0'
        style.left = '0'
        style.width = '100%'
      }

      if (this.border) {
        style.borderTop = '1rpx solid #e5e5e5'
      }

      return style
    }
  },

  methods: {
    // 处理标签点击
    handleTabClick(item, index) {
      // 设置当前选中索引
      this.$store.commit('fitment/SET_CURRENT_TAB_INDEX', index)

      // 根据Tab类型进行页面跳转
      if (item) {
        console.log('TabBar切换到:', item.type, index)

        // 点击动态页面时清零频道小红点并隐藏视频生成通知
        if (item.type === '动态') {
          this.$store.commit('user/CLEAR_CHANNEL_BADGE_COUNT')
          this.$store.commit('user/HIDE_VIDEO_GENERATED_NOTIFICATION')
        }

        switch (item.type) {
          case '首页':
            uni.switchTab({ url: '/pages/index/index' })
            break
          case '动态':
            uni.switchTab({ url: '/pages/home_two/index' })
            break
          case '频道':
            uni.switchTab({ url: '/pages/home_three/index' })
            break
          case '我的':
            uni.switchTab({ url: '/pages/home_four/index' })
            break
          default:
            console.log('未知的Tab类型:', item.type)
        }
      }

      // 触发事件给父组件
      this.$emit('change', {
        item,
        index,
        current: index
      })
    },

    // 获取文字样式
    getTabTextStyle(index) {
      return {
        color: this.currentIndex === index ? this.activeTextColor : this.textColor,
        fontSize: `${this.fontSize * 2}rpx` // 转换为rpx单位
      }
    },

    // 设置徽章
    setBadge(index, badge) {
      if (this.tabList[index]) {
        this.$set(this.tabList[index], 'badge', badge)
      }
    },

    // 移除徽章
    removeBadge(index) {
      if (this.tabList[index]) {
        this.$set(this.tabList[index], 'badge', 0)
      }
    },

    // 显示小红点
    showDot(index) {
      if (this.tabList[index]) {
        this.$set(this.tabList[index], 'dot', true)
      }
    },

    // 隐藏小红点
    hideDot(index) {
      if (this.tabList[index]) {
        this.$set(this.tabList[index], 'dot', false)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-tabbar {
  display: flex;
  box-sizing: content-box;
  align-items: center;
  justify-content: space-around;
  background-color: #ffffff;
  padding-bottom: env(safe-area-inset-bottom);

  .tab-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    height: 100%;
    transition: all 0.3s ease;

    &.active {
      .tab-icon {
        transform: scale(1.1);
      }
    }

    .tab-icon {
      position: relative;
      margin-bottom: 8rpx;
      transition: transform 0.3s ease;

      .icon-image {
        width: 48rpx;
        height: 48rpx;
        display: block;
      }

      .icon-placeholder {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        background-color: #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20rpx;
        font-weight: bold;
        color: #999;

        &.active {
          background-color: #21BD74;
          color: #fff;
        }
      }
    }

    .tab-text {
      font-size: 20rpx;
      line-height: 1;
      transition: color 0.3s ease;
    }

    .tab-badge {
      position: absolute;
      top: 8rpx;
      right: 20rpx;
      min-width: 32rpx;
      height: 32rpx;
      background-color: #ff4757;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 8rpx;

      .badge-text {
        color: #ffffff;
        font-size: 18rpx;
        line-height: 1;
        font-weight: bold;
      }
    }

    .tab-dot {
      position: absolute;
      top: 12rpx;
      right: 28rpx;
      width: 16rpx;
      height: 16rpx;
      background-color: #ff4757;
      border-radius: 50%;
    }
  }
}

/* 适配不同主题 */
.custom-tabbar.theme-dark {
  background-color: #1a1a1a;
  border-top-color: #333;

  .tab-item {
    .icon-placeholder {
      background-color: #333;
      color: #666;

      &.active {
        background-color: #21BD74;
        color: #fff;
      }
    }
  }
}

/* 适配安全区域 */
.custom-tabbar.safe-area {
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
