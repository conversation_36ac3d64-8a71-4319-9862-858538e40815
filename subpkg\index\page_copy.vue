<template>
  <view class="page">
    <!-- <logingModel></logingModel> -->
    <view class="page_title" v-if="randomFlag" @click="handleRandomClick">
      <view>不知道该写什么 , 试试</view>
      <text>随机示例</text>
    </view>

    <view v-for="item in fields">
      <view class="text_title">
        {{ item.label }}
      </view>
      <u--textarea v-if="item.type == 'textarea'" height="300rpx" maxlength="500" v-model="item.value"
        :placeholder="item.placeholder" count></u--textarea>
      <u--input v-else :placeholder="item.placeholder" border="surround" v-model="item.value"></u--input>
    </view>

    <view>
      <view v-for="item in attr">
        <view class="text_title">{{ item.attrTitle }}</view>
        <view>
          <view v-for="name in item.attrValue" :class="!type_arr.includes(name) ? 'text_box' : 'active_text_box'"
            @click="down_type(item, name)">
            {{ name }}
          </view>
        </view>
      </view>
    </view>

    <view class="project_btn" @click="save">
      生成文案
    </view>

    <globalLoginModal />
  </view>
</template>

<script>
import { requests } from '../../utils/request.js'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      page_item: '',
      fields: '',
      attr: '',
      value: '',
      toolCode: '',

      page_toolTitle: '',
      page_fields: '',
      page_attr: [],

      type_arr: [],
      randomFlag: '',
      source: ''
    }
  },
  computed: {
    ...mapGetters('user', ['isLogin', 'userInfo']),
    isLoggedIn() {
      return this.isLogin;
    }
  },
  onLoad(options) {
    // 获取来源参数
    this.source = options.source || '';
    
    this.getOpenerEventChannel().on('get_message', (list) => {
      this.page_item = JSON.parse(list.ext)
      this.fields = this.page_item.fields

      this.fields.map(item => {
        if (item.type == 'input') item.value = ''
        if (item.type == 'textarea') item.value = ''
      })

      this.page_toolTitle = this.page_item.toolTitle
      this.attr = this.page_item.attr
      this.randomFlag = this.page_item.randomFlag || ''
      this.toolCode = list.toolCode

      uni.setNavigationBarTitle({
        title: list.name
      })
      this.attr?.map(item => {
        this.page_attr.push({
          attrTitle: item.attrTitle,
          attrValue: item.attrValue[0]
        })
        this.type_arr.push(item.attrValue[0])
      })
    })
  },
  methods: {
    // 处理随机按钮点击，根据登录状态决定行为
    handleRandomClick() {
      if (this.isLoggedIn) {
        this.get_message();
      } else {
        this.$showLogin({
          success: () => {
            // 登录成功后获取随机示例
            this.get_message();
          },
          cancel: () => {
            uni.showToast({
              title: '需要登录后才能使用随机示例',
              icon: 'none'
            });
          }
        });
      }
    },
    
    get_message() {
      requests({
        url: `/tool/random/text/${this.toolCode}`,
        method: 'get'
      }).then(res => {
        console.log(res)
        this.fields.map(item => {
          console.log(item)
          item.value = ''
          if (item.type == 'input') item.value = res.data.title
          if (item.type == 'textarea') {
            item.value = res.data.inputJson
          }
        })
        this.$forceUpdate()
      })
    },
    down_type(item, name) {
      console.log(this.type_arr)
      if (this.page_attr.length > 0) {
        this.page_attr.map(res => {
          if (res.attrTitle == item.attrTitle) {
            this.type_arr.map((item, index) => {
              if (item == res.attrValue) {
                this.$set(this.type_arr, index, name)
              }
            })
            res.attrValue = name
          }
        })
        let obj = this.page_attr.filter(res => {
          return res.attrTitle == item.attrTitle
        })
        if (obj.length == 0) {
          this.page_attr.push({
            attrTitle: item.attrTitle,
            attrValue: name
          })
          this.type_arr.push(name)
        }
      } else {
        this.page_attr.push({
          attrTitle: item.attrTitle,
          attrValue: name
        })
        this.type_arr.push(name)
      }
      this.$forceUpdate()
    },

    /**
     * 保存并处理用户输入的数据
     * 验证输入内容并导航到结果页面，同时传递数据
     */
    save() {
      // 检查第一个字段是否有值，确保用户输入了必要内容
      if (!this.fields[0].value) {
        // 如果没有输入内容，显示提示信息
        uni.showToast({
          title: '请填写内容',
          icon: 'none'
        })
      } else {
        // 检查登录状态，如未登录则弹出登录框
        this.$checkLogin({
          success: () => {
            // 登录成功，继续生成文案
            // 将当前对象引用保存到变量中，以便在回调函数内部使用
            let that = this
            // 导航到结果页面，传递source参数
            uni.navigateTo({
              url: '/subpkg/index/results' + (that.source ? `?source=${that.source}` : ''),
              // 页面导航成功后执行的回调函数
              success(res) {
                // 通过eventChannel向目标页面传递数据
                // 传递工具标题、表单字段数据和额外属性
                res.eventChannel.emit('get_message', {
                  toolTitle: that.page_toolTitle,  // 工具标题
                  fields: that.fields,             // 表单字段数据
                  attr: that.page_attr             // 额外属性/参数
                })
              }
            })
          },
          cancel: () => {
            // 用户取消登录，不执行后续操作
            uni.showToast({
              title: '需要登录后才能生成文案',
              icon: 'none'
            })
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.page {
  padding: 40rpx 30rpx;
  background-color: #f9f9f9;
  min-height: 100vh;

  .text_title {
    font-weight: 600;
    font-size: 32rpx;
    margin: 20rpx 0;
    color: #333;
  }

  .page_title {
    display: flex;
    align-items: center;
    font-size: 32rpx;

    view {
      color: #919191;
    }

    image {
      width: 60rpx;
      height: 60rpx;
      margin: 0 10rpx;
    }

    text {
      color: #80e171;
    }
  }

  .text_box {
    font-size: 28rpx;
    display: inline-block;
    padding: 10rpx 30rpx;
    background: white;
    margin-right: 20rpx;
    margin-top: 20rpx;
    border-radius: 20rpx;
    border: 4rpx solid white;
  }

  .active_text_box {
    font-size: 28rpx;
    display: inline-block;
    padding: 10rpx 30rpx;
    background: white;
    margin-right: 20rpx;
    border-radius: 20rpx;
    border: 4rpx solid rgb(100, 218, 175);
  }

  .project_btn {
    margin: auto;
    margin-top: 100rpx;
    background: black;
  }

  .project_btn.active {
    opacity: 1;
    pointer-events: auto;
  }

  u--input,
  u--textarea {
    background-color: white;
  }
}
</style>
