import {requests} from '../../utils/request.js'

export const getUploadFileSts = (data) =>{
	return requests({
		url: '/dr/upload/sts',
		method:'post',
		data:data
	})
}

export const getTrainVoice = (data) =>{
	return requests({
		url: '/dr/train/voice',
		method:'post',
		data:data
	})
}

export const getTrainPerson = (data) =>{
	return requests({
		url: '/dr/train/person',
		method:'post',
		data:data
	})
}

export const getRule = (data) =>{
	return requests({
		url: '/power/rule',
		method:'get',
		data:data
	})
}

export const getVoiceDetail = (id, data) =>{
	return requests({
		url: `/dr/voice/${id}/detail`,
		method:'get',
		data:data
	})
}

export const postVoiceConfirm = (data) =>{
	return requests({
		url: '/dr/voice/confirm',
		method:'post',
		data:data
	})
}

export const postCreateVideo = (data) =>{
	return requests({
		url: '/dr/video/create',
		method:'post',
		data:data
	})
}
export const postTemplateoList = (data) =>{
	return requests({
		url: '/dr/official/template/list',
		method:'get',
		data:data
	})
}

export const postTemplateoTitle = (data) =>{
	return requests({
		url: '/dr/official/template/material/list',
		method:'post',
		data:data
	})
}   

export const postListenerAudio = (data) =>{
	return requests({
		url: '/dr/listener/audio',
		method:'post',
		data:data
	})
}  











