<template>
  <view>
    <view class="fitment-theme-page" v-if="isCustomTemplate && !loading">
      <!-- 背景层 -->
      <view class="background-layer" :style="[backgroundStyle]">
        <image v-if="fitmentData.background && fitmentData.background.type === '上传背景' && fitmentData.background.image"
          :src="fitmentData.background.image" class="background-image" mode="aspectFill" />
      </view>

      <!-- 主要内容 -->
      <view class="page-content">
        <!-- 组件层 -->
        <view class="components-layer">
          <view v-for="item in layoutItems" :key="item.id" class="component-item" :style="[getComponentStyle(item)]">
            <!-- 图片组件 -->
            <template v-if="item.type === '图片'">
              <image :src="item.config.imageUrl || defaultImage" class="image-component"
                :style="[getImageComponentStyle(item)]" @error="handleImageError" />
            </template>

            <!-- 文字组件 -->
            <template v-else-if="item.type === '文字'">
              <text class="text-component" :style="[getTextComponentStyle(item)]">
                {{ item.config.content || '示例文字' }}
              </text>
            </template>

            <!-- 功能组件 -->
            <template v-else-if="item.type === '功能'">
              <view class="function-component" :style="[getFunctionComponentStyle(item)]"
                @tap="handleFunctionClick(item)">
                <!-- 背景图片 -->
                <image v-if="item.config.backgroundMode === 'image' && item.config.backgroundImageUrl"
                  :src="item.config.backgroundImageUrl" class="function-background-image"
                  :style="[getFunctionBackgroundImageStyle(item)]" @error="handleImageError" />

                <view class="function-content-wrapper">
                  <!-- 默认样式(1x1) -->
                  <template v-if="item.config.style === 'default'">
                    <view class="function-default">
                      <image v-if="item.config.icon" :src="item.config.icon" class="function-icon"
                        :style="[getFunctionIconStyle(item)]" @error="handleImageError" />
                      <text class="function-name" :style="[getFunctionTitleStyle(item)]">
                        {{ item.config.name || '默认功能' }}
                      </text>
                    </view>
                  </template>

                  <!-- 小图标左侧+右侧名称(2x1) -->
                  <template v-else-if="item.config.style === 'smallAround'">
                    <view class="function-small-around">
                      <image v-if="item.config.icon" :src="item.config.icon" class="function-icon"
                        :style="[getFunctionIconStyle(item)]" @error="handleImageError" />
                      <text class="function-name" :style="[getFunctionTitleStyle(item)]">
                        {{ item.config.name || '默认功能' }}
                      </text>
                    </view>
                  </template>

                  <!-- 小图标左侧+右侧名称和描述(2x1) -->
                  <template v-else-if="item.config.style === 'smallAroundDesc'">
                    <view class="function-small-desc">
                      <image v-if="item.config.icon" :src="item.config.icon" class="function-icon"
                        :style="[getFunctionIconStyle(item)]" @error="handleImageError" />
                      <view class="function-content">
                        <text class="function-name" :style="[getFunctionTitleStyle(item)]">
                          {{ item.config.name || '默认功能' }}
                        </text>
                        <text class="function-desc" :style="[getFunctionDescStyle(item)]">
                          {{ item.config.description || '' }}
                        </text>
                      </view>
                    </view>
                  </template>

                  <!-- 大图标左侧+右侧名称和描述(4x1) -->
                  <template v-else-if="item.config.style === 'bigIconLeft'">
                    <view class="function-big-left">
                      <image v-if="item.config.icon" :src="item.config.icon" class="function-icon-big"
                        :style="[getFunctionBigIconStyle(item)]" @error="handleImageError" />
                      <view class="function-content">
                        <text class="function-name" :style="[getFunctionTitleStyle(item)]">
                          {{ item.config.name || '默认功能' }}
                        </text>
                        <text class="function-desc" :style="[getFunctionDescStyle(item)]">
                          {{ item.config.description || '' }}
                        </text>
                      </view>
                    </view>
                  </template>

                  <!-- 左侧名称和描述+大图标右侧(4x1) -->
                  <template v-else-if="item.config.style === 'bigIconRight'">
                    <view class="function-big-right">
                      <view class="function-content">
                        <text class="function-name" :style="[getFunctionTitleStyle(item)]">
                          {{ item.config.name || '默认功能' }}
                        </text>
                        <text class="function-desc" :style="[getFunctionDescStyle(item)]">
                          {{ item.config.description || '' }}
                        </text>
                      </view>
                      <image v-if="item.config.icon" :src="item.config.icon" class="function-icon-big"
                        :style="[getFunctionBigIconStyle(item)]" @error="handleImageError" />
                    </view>
                  </template>

                  <!-- 上图下文(2x2) -->
                  <template v-else-if="item.config.style === 'bigUpDown'">
                    <view class="function-up-down">
                      <image v-if="item.config.icon" :src="item.config.icon" class="function-icon-big"
                        :style="[getFunctionBigIconStyle(item)]" @error="handleImageError" />
                      <view class="function-content">
                        <text class="function-name" :style="[getFunctionTitleStyle(item)]">
                          {{ item.config.name || '默认功能' }}
                        </text>
                        <text class="function-desc" :style="[getFunctionDescStyle(item)]">
                          {{ item.config.description || '' }}
                        </text>
                      </view>
                    </view>
                  </template>
                </view>
              </view>
            </template>

            <!-- 工具组件（空白占位） -->
            <template v-else-if="item.type === '工具'">
              <view class="spacer-component"></view>
            </template>
          </view>
        </view>
      </view>
    </view>
    <view v-else-if="!isCustomTemplate && !loading">
      <QlXfIndex :config-info="configInfo" v-if="isQl" />
      <HwIndex :config-info="configInfo" v-else-if="isHw" />
      <LyIndex :config-info="configInfo" v-else-if="isLy" />
      <CZIndex :config-info="configInfo" v-else-if="isCZ" />
    </view>
    <!-- 加载状态 -->
    <custom-loading :show="loading" :color="'#02f191'" :background="'#ffffff'" text="加载中..." />
    <!-- 底部导航栏 -->
    <TabBar v-if="isCustomTemplate && !loading" ref="tabBar" :index="0" active-color="white" inactive-color="white"
      custom-style="background:rgb(29,36,41)" />
    <!-- 通知栏目 -->
    <pageTitle />
  </view>
</template>

<script>
import TabBar from '@/components/finalTabBar/index.vue'
import { mapState, mapGetters, mapActions } from 'vuex'
import { styleHelpers } from '@/store/modules/fitment.js'
import QlXfIndex from './components/Ql-Xf-Index.vue';
import HwIndex from './components/Hw-Index.vue';
import LyIndex from './components/Ly-Index.vue';
import CZIndex from './components/CZ-Index.vue';
import { getAppConfig } from '@/api/numberUser/userType.js'
import CustomLoading from "@/components/custom-loading/index.vue";

export default {
  components: {
    CustomLoading,
    TabBar,
    QlXfIndex,
    HwIndex,
    LyIndex,
    CZIndex,
  },
  data() {
    return {
      currentComponent: null, //当前使用组件
      //传递给子组件用于判断是否是否可以极速克隆
      configInfo: {
        shareImg: '',
        shareText: '',
        fastPowerRange: false,
        enableFastClone: '',
      },
    }
  },

  computed: {
    // 从Vuex获取状态
    ...mapState('fitment', ['loading', 'error', 'defaultImage', 'fitmentData']),
    ...mapGetters('fitment', [
      'layoutItems',
      'backgroundStyle',
      'isCustomTemplate',
      'tabList',
      'showTabBar',
      'pageTitle',
      'pageStatistics',
      'containerMinHeight'
    ]),

    // 样式计算方法
    getComponentStyle() {
      return styleHelpers.getComponentStyle
    },
    getImageComponentStyle() {
      return styleHelpers.getImageComponentStyle
    },
    getTextComponentStyle() {
      return styleHelpers.getTextComponentStyle
    },
    getFunctionComponentStyle() {
      return styleHelpers.getFunctionComponentStyle
    },
    getFunctionIconStyle() {
      return styleHelpers.getFunctionIconStyle
    },
    getFunctionBigIconStyle() {
      return styleHelpers.getFunctionBigIconStyle
    },
    getFunctionTitleStyle() {
      return styleHelpers.getFunctionTitleStyle
    },
    getFunctionDescStyle() {
      return styleHelpers.getFunctionDescStyle
    },
    getFunctionBackgroundImageStyle() {
      return styleHelpers.getFunctionBackgroundImageStyle
    },
    isQl() {
      return this.currentComponent === 'QlXfIndex';
    },
    isHw() {
      return this.currentComponent === 'HwIndex';
    },
    isLy() {
      return this.currentComponent === 'LyIndex';
    },
    isCZ() {
      return this.currentComponent === 'CZIndex';
    },
    isCustom() {
      return this.customTemplateObj != null;
    }
  },

  // 页面生命周期
  async onLoad(options) {
    uni.hideTabBar();
    const appId = uni.getAccountInfoSync().miniProgram.appId;
    console.log('appId', appId);
    this.queryAppConfig(appId);
    const appComponentMap = {
      'wxf3e2c2907f669982': 'CZIndex',//超赞
      'wx7ccb8b4b4784214f': 'QlXfIndex', //起量
      'wx561f5390d721dfdf': 'QlXfIndex', //小福
      'wxd15ae7339845e87f': 'HwIndex', //和我
      'wxd73824f025ba3716': 'LyIndex',//绿叶
    };
    this.currentComponent = appComponentMap[appId];

    try {
      // 直接初始化页面数据，无需设置themeId
      await this.initPageData()
    } catch (error) {
      console.error('页面初始化失败:', error)
      uni.showModal({
        title: '加载失败',
        content: '页面加载失败',
        showCancel: false,
        confirmText: '确定'
      })
    }
  },

  onShow() {
    // 设置当前选中索引
    this.$store.commit('fitment/SET_CURRENT_TAB_INDEX', 0)
    console.log('页面显示')
  },

  onReady() {
    console.log('页面渲染完成')
  },

  onHide() {
    console.log('页面隐藏')
  },

  onUnload() {
    console.log('页面卸载')
  },

  // 下拉刷新
  async onPullDownRefresh() {
    console.log('下拉刷新')
    try {
      await this.refreshData()
      uni.stopPullDownRefresh()
      uni.showToast({
        title: '刷新成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('刷新失败:', error)
      uni.stopPullDownRefresh()
      uni.showToast({
        title: '刷新失败',
        icon: 'none'
      })
    }
  },

  onShareAppMessage() {
    return {
      title: this.configInfo.shareText, // 分享标题
      path: '/pages/index/index', // 分享路径，当前页面 path ，必须是以 / 开头的完整路径
      imageUrl: this.configInfo.shareImg, // 分享图片路径
      success(res) {
        console.log('分享成功', res);
      },
      fail(err) {
        console.log('分享失败', err);
      }
    }
  },
  methods: {
    // 从Vuex获取actions
    ...mapActions('fitment', [
      'initPageData',
      'refreshData'
    ]),

    //查询小程序配置
    queryAppConfig(appId) {
      getAppConfig({ appId }).then(res => {
        console.log(res.data, 'asdsad');

        this.configInfo = res.data
      })
    },

    /**
     * 处理功能组件点击
     */
    handleFunctionClick(item) {
      // 非ai工具
      if (item.config.textToolFlag === 0) {
        uni.navigateTo({
          url: item.config.componentPath
        })
      }
      // ai工具
      if (item.config.textToolFlag === 1) {
        uni.navigateTo({
          url: '/subpkg/index/page_copy?source=home',
          success: (res) => {
            res.eventChannel.emit('get_message', item.config)
          }
        });
      }
    },

    /**
     * 处理图片加载错误
     */
    handleImageError(e) {
      console.warn('图片加载失败:', e)
    },

    /**
     * 设置TabBar徽章
     * @param {Number} index 标签索引
     * @param {Number} badge 徽章数字
     */
    setTabBadge(index, badge) {
      this.$refs.tabBar?.setBadge(index, badge)
    },

    /**
     * 移除TabBar徽章
     * @param {Number} index 标签索引
     */
    removeTabBadge(index) {
      this.$refs.tabBar?.removeBadge(index)
    },

    /**
     * 显示TabBar小红点
     * @param {Number} index 标签索引
     */
    showTabDot(index) {
      this.$refs.tabBar?.showDot(index)
    },

    /**
     * 隐藏TabBar小红点
     * @param {Number} index 标签索引
     */
    hideTabDot(index) {
      this.$refs.tabBar?.hideDot(index)
    }
  }
}
</script>

<style lang="scss" scoped>
/* 页面样式 */
.fitment-theme-page {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  overflow-x: hidden;
}

/* 背景层 */
.background-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;

  .background-image {
    width: 100%;
    height: 100%;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 页面内容 */
.page-content {
  position: relative;
  width: 100%;
  min-height: 100vh;
  padding-bottom: calc(env(safe-area-inset-bottom) + 50px);
  z-index: 2;
}

/* 组件层 */
.components-layer {
  position: relative;
  width: 100%;
  min-height: calc(100vh - 120rpx);
  z-index: 10;
  padding: 16rpx;
}

.component-item {
  box-sizing: border-box;
}

/* 图片组件 */
.image-component {
  width: 100%;
  height: 100%;
  display: block;
}

/* 文字组件 */
.text-component {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  word-wrap: break-word;
  word-break: break-all;
}

/* 功能组件 */
.function-component {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;

  .function-background-image {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    z-index: 1;
  }

  .function-content-wrapper {
    position: relative;
    z-index: 2;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  // 各种功能组件样式
  .function-default,
  .function-small-around,
  .function-small-desc,
  .function-big-left,
  .function-big-right,
  .function-up-down {
    width: 100%;
    height: 100%;
  }

  // 默认样式 (1x1)
  .function-default {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;

    .function-icon {
      margin-bottom: 8rpx;
    }

    .function-name {
      font-size: 24rpx;
      line-height: 1.2;
    }
  }

  // 小图标左侧样式 (2x1)
  .function-small-around {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 16rpx;

    .function-icon {
      margin-right: 16rpx;
      flex-shrink: 0;
    }

    .function-name {
      flex: 1;
      font-size: 28rpx;
      line-height: 1.2;
    }
  }

  // 小图标+名称+描述 (2x1)
  .function-small-desc {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 16rpx;

    .function-icon {
      margin-right: 16rpx;
      flex-shrink: 0;
    }

    .function-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .function-name {
        font-size: 28rpx;
        line-height: 1.2;
        margin-bottom: 4rpx;
      }

      .function-desc {
        font-size: 20rpx;
        line-height: 1.2;
        opacity: 0.8;
      }
    }
  }

  // 大图标左侧样式 (4x1)
  .function-big-left {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 24rpx;

    .function-icon-big {
      margin-right: 24rpx;
      flex-shrink: 0;
    }

    .function-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .function-name {
        font-size: 32rpx;
        line-height: 1.2;
        margin-bottom: 8rpx;
        font-weight: 600;
      }

      .function-desc {
        font-size: 24rpx;
        line-height: 1.3;
        opacity: 0.8;
      }
    }
  }

  // 大图标右侧样式 (4x1)
  .function-big-right {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 24rpx;

    .function-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-right: 24rpx;

      .function-name {
        font-size: 32rpx;
        line-height: 1.2;
        margin-bottom: 8rpx;
        font-weight: 600;
      }

      .function-desc {
        font-size: 24rpx;
        line-height: 1.3;
        opacity: 0.8;
      }
    }

    .function-icon-big {
      flex-shrink: 0;
    }
  }

  // 上图下文样式 (2x2)
  .function-up-down {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    padding: 16rpx;

    .function-icon-big {
      margin-bottom: 16rpx;
    }

    .function-content {
      display: flex;
      flex-direction: column;
      align-items: center;

      .function-name {
        font-size: 28rpx;
        line-height: 1.2;
        margin-bottom: 8rpx;
        font-weight: 600;
      }

      .function-desc {
        font-size: 22rpx;
        line-height: 1.3;
        opacity: 0.8;
      }
    }
  }
}

/* 空白组件 */
.spacer-component {
  width: 100%;
  height: 100%;
  // 移动端不显示边框，保持透明
}
</style>