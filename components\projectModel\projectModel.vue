<template>
	<view class="project_model_1">
		<view class="project_model_2">
			<view class="title">{{title}}</view>
			<view class="center">{{content}}</view>
			<slot>

			</slot>

			<view class="page_btn" v-if="btn">
				<view class="btn1" @click="btn_close">{{close}}</view>
				<view class="btn2" @click="btn_save">{{save}}</view>
			</view>

		</view>
	</view>
</template>

<script>
	export default{
		data(){
			return{

			}
		},
		props: {
			title: {
				type: String,
				default: ''
			},
			content: {
				type: String,
				default: ''
			},
			save: {
				type: String,
				default: '确定'
			},
			close: {
				type: String,
				default: '取消'
			},
			btn:{
				type: Boolean,
				default: true
			}
		},
		methods:{
			btn_close(){
				this.$emit('btn_close');
			},
			btn_save(){
				this.$emit('btn_save');
			}
		}
	}
</script>

<style scoped lang="scss">
	.project_model_1 {
		position: fixed;
		width: 100vw;
		height: 100vh;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 99;
	}

	.project_model_2 {
		width: 88vw;
		padding: 45rpx;
		background: white;
		border-radius: 24rpx;
		z-index: 999;
		position: fixed;
		top: 43%;
		left: 50%;
		transform: translate(-50%, -50%);
	}
	.title{
		font-size: 34rpx;
		font-weight: 600;
		text-align: center;
		color: #333333;
	}
	.center{
		font-size: 30rpx;
		color: #545454;
		margin: 40rpx 0 60rpx 0;
		text-align: center;
	}
	.page_btn {
		display: flex;
		justify-content: space-around;
    align-items: center;
		margin-top: 32rpx;
		.btn1 {
			text-align: center;
			background: #e5e5e5;
			border-radius: 20rpx;
			width: 240rpx;
			padding: 20rpx 35rpx;
		}
		.btn2 {
			text-align: center;
			background: black;
			color: white;
			border-radius: 20rpx;
			width: 240rpx;
      padding: 20rpx 35rpx;
		}
	}
</style>
