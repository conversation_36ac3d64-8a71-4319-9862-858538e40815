import { View } from "XrFrame/kanata/lib/frontend"

<template>
	<view class="page">
		<view class="page_border">
			<view v-if="content_list" style="font-size: 30rpx;">
				<!-- <view style="font-size: 30rpx;font-weight: 600;text-align: center;">{{page_title}}</view> -->
				<view style="margin-top: 20rpx;line-height: 1.6;" >
					{{content_list}}
				</view>
			</view>
			<view v-else>
				<view style="margin-bottom: 20rpx;">文案生成中</view>
				<u-skeleton rows="6" loading :animate="true"></u-skeleton>
			</view>
		</view>

		<view class="button-group">
			<view class="btn_1" @click="again">重新生成</view>
			<view class="btn_2" @click="save">使用文案</view>
		</view>
		
		
		<projectModel
		   v-if="page_show"
		   title="温馨提示"
		   content="未提取到文案内容,请检查链接是否有误"
		   :btn="false"
		>
			<view class="project_btn" @click="down_back">重新提取</view>
		</projectModel>
	</view>
</template>

<script>
	import {
		postGenText
	} from '../../api/numberUser/copy.js'
	import {
		Copywriting
	} from '../../utils/file.js'
	import {getExtractText} from '../../api/ai/extract.js'
	export default {
		data() {
			return {
				page_message: '',
				page_title: '',
				content_list: '',
				option: '',
				page_url:'',
				page_show:false,
				fromSelectWriter: false // 标记是否来自selectWriter组件
			}
		},
		onLoad(options) {
			// 检查是否包含来源标记
			this.fromSelectWriter = options.from === 'selectWriter';
			
			this.getOpenerEventChannel().on('get_message', (url) => {
				this.page_url = url
				this.get_data()
			})
		},
		methods: {
			down_back(){
				uni.navigateBack()
			},
			get_data() {
				getExtractText({
					urlInfo:this.page_url
				}).then(res =>{
					this.content_list = res.data 
					if(!res.data){
						this.page_show = true
					}
				})
			},
			again() {
				this.page_title = ''
				this.content_list = ''
				this.get_data()
			},
			save() {
				// 保留原有逻辑
				let arr = []
				arr[0] = this.content_list
				this.$store.commit('setMessage_save', true)
				this.$store.commit('setAiMessage', {
					content: arr,
					title: this.page_title
				})
				
				// 判断来源，如果来自selectWriter，则发送事件并返回
				if (this.fromSelectWriter) {
					// 添加事件发送，将提取的文案数据发送回selectWriter组件
					uni.$emit('return-writer-data', {
						title: this.page_title || '提取的视频文案',
						content: this.content_list
					});
					
					// 返回上一页
					uni.navigateBack({
						delta: 2  // 返回两级，跳过extractionCopy.vue直接回到selectWriter.vue
					});
				} else {
					// 原有逻辑：跳转到首页
				uni.redirectTo({
					url: '/subpkg/index/get_radio/index'
					});
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.page {
		padding: 60rpx 40rpx;
		padding-bottom: 200rpx;

		.page_border {
			padding: 20rpx;
			min-height: 60vh;
			--borderWidth: 4rpx;
			background: white;
			position: relative;
			border-radius: 20rpx;
		}

		.page_border:after {
			content: '';
			position: absolute;
			top: calc(-1 * var(--borderWidth));
			left: calc(-1 * var(--borderWidth));
			height: calc(100% + var(--borderWidth) * 2);
			width: calc(100% + var(--borderWidth) * 2);
			background: linear-gradient(60deg, #f79533, #f37055, #ef4e7b, #a166ab, #5073b8, #1098ad, #07b39b, #6fba82);
			border-radius: 20rpx;
			z-index: -1;
			animation: animatedgradient 3s ease alternate infinite;
			background-size: 300% 300%;
		}

		.button-group {
			position: fixed;
			display: flex;
			justify-content: space-around;
			width: 100%;
			bottom: 40rpx;
			left: 0;

			.btn_1 {
				background: #ffffff;
				color: black;
				padding: 20rpx 40rpx;
				border-radius: 20rpx;
			}

			.btn_2 {
				background: #000000;
				color: white;
				padding: 20rpx 40rpx;
				border-radius: 20rpx;
			}
		}
	}
	
	.project_btn{
		background:black;
	}
</style>