import {requests} from '../../utils/request.js'

export const getGroup = (data) =>{
	return requests({
		url: '/group/list',
		method:'get',
		data:data
	})
}

export const getGroupList = (data) =>{
	return requests({
		url: '/group/category/list',
		method:'get',
		data:data
	})
}

export const getGroupContent = (data) =>{
	return requests({
		url: '/group/content/list',
		method:'get',
		data:data
	})
}

export const getRandomText = (data) =>{
	return requests({
		url: '/tool/random/text/voice',
		method:'get',
		data:data
	})
}

export const postGenText = (data) =>{
	return requests({
		url: '/ai/gen-text',
		method:'post',
		data:data
	})
}










