<template>
  <view class="page-container">
    <!-- 分类选项卡 -->
    <view class="category-tabs">
      <view v-for="(category, index) in categoryList" :key="index"
        :class="activeTabIndex === index ? 'tab-active' : 'tab-normal'" @click="handleTabChange(category, index)">
        {{ category.name }}
      </view>
    </view>

    <!-- 未登录状态显示登录提示 -->
    <view v-if="!isLoggedIn" class="module-login-prompt">
      <image src="/static/home_too/icon_dongtai.png" mode="widthFix" class="login-prompt-image"></image>
      <view class="login-prompt-text">登录后查看{{ categoryList[activeTabIndex].name }}内容</view>
      <button class="login-btn" @click="handleLoginButtonClick">立即登录</button>
    </view>

    <!-- 已登录且有数据时显示消息列表 -->
    <scroll-view v-else-if="messageList.length > 0" scroll-y="true" class="message-scroll"
      @scrolltolower="handleLoadMore">
      <view v-for="(message, index) in messageList" :key="message.id">
        <view class="message-card">
          <view class="message-icon">
            <image :src="getMessageIcon(message)"></image>
          </view>
          <view class="message-content">
            <!-- 非工具类消息 -->
            <view v-if="message.feedType != 'tool'">
              <view class="message-header">
                <view class="message-title">{{ message.title }}</view>
                <view class="message-time">{{ message.updateTime }}</view>
              </view>
              <view class="message-text">{{ message.message.text }}</view>
              <text class="message-detail-btn" @click="navigateToDetail(message)">查看详情</text>
            </view>

            <!-- 工具类消息 -->
            <view v-else>
              <view class="message-header">
                <view class="message-title">
                  {{ message.title }}
                </view>
                <view class="message-time">{{ message.updateTime }}</view>
              </view>
              <view style="font-size: 28rpx;margin-top: 10rpx"
                v-if="message.message.text.includes('#@#') && message.message.text.split('#@#')[0] != null">
                {{ message.message.text.split('#@#')[0] }}
              </view>
              <view class="message-text truncate-text">
                {{ message.message.text.split('#@#')[1] || message.message.text }}
                <view class="view-more-btn" v-if="message.message.text.length > 60"
                  @click="navigateToMessageDetail(message)">
                  ...查看全文
                </view>
              </view>

              <!-- 视频缩略图 -->
              <view @click="handleVideoPlay(message.message.url)"
                v-show="message.linkType === 'video' && message.message.status == 'success'" class="video-thumbnail">
                <image :src="message.message.cover" mode="aspectFill" class="video-cover"></image>
                <view class="play-icon">
                  <image :src="ossPath + '/project1_video_play.png'" mode="aspectFit"></image>
                </view>
              </view>

              <!-- 合集视频缩略图 -->
              <view @click="handleBatchVideoPlay(message)"
                v-show="message.linkType === 'batchVideo' && message.message.status == 'success'"
                class="batch-video-thumbnail">
                <image :src="message.message.cover" mode="aspectFill" class="video-cover"></image>
                <view class="tag">合集</view>
                <view class="bottom-text">查看全部 ></view>
                <view class="mask"></view>
              </view>

              <!-- 生成中状态 -->
              <view v-if="message.message.status == 'generating' || message.message.status == 'fail'"
                class="generating-container">
                <image
                  :class="message.message.status == 'generating' || message.message.status == 'fail' ? 'blur-background' : ''"
                  mode="widthFix" class="generating-bg"
                  src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/video_loading_bg.png">
                </image>
                <view class="generating-content">
                  <template v-if="message.message.status == 'generating'">
                    <view>
                      <image class="loading-icon" src="/static/project_loding.png"></image>
                    </view>
                    <view class="generating-text">生成中</view>
                    <view class="generating-time">(正在加速生成中...)</view>
                  </template>
                  <template v-if="message.message.status == 'fail'">
                    <view class="generating-text">生成失败</view>
                    <view class="generating-time">{{ message.message.reason }}</view>
                  </template>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="message-divider"></view>
      </view>
    </scroll-view>

    <!-- 已登录但无数据时显示空状态 -->
    <view v-else-if="isDataLoaded" class="empty-state">
      <noneMessage type="message" content="暂无动态" pad_top="0rpx"></noneMessage>
    </view>

    <TabBar :index="1" active-color="black" custom-style="background:white" />
    <globalLoginModal />
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import TabBar from '@/components/finalTabBar/index.vue'
export default {
  data() {
    return {
      ossPath: this.$appConfig.appInfo.ossPath,
      isDataLoaded: false,
      categoryList: [{
        name: '全部',
        type: ''
      },
      {
        name: '工具动态',
        type: 'tool'
      },
      {
        name: '系统通知',
        type: 'system'
      },
      {
        name: '频道通知',
        type: 'group'
      }
      ],
      totalCount: 0,
      activeTabIndex: 0,
      messageList: [],
      currentPage: 1,
      hasMoreData: true,
      currentFeedType: ''
    }
  },
  components: {
    TabBar
  },
  computed: {
    ...mapGetters('user', ['isLogin', 'userInfo']),
    isLoggedIn() {
      return this.isLogin;
    }
  },
  onShow() {
    // 设置当前选中索引
    this.$store.commit('fitment/SET_CURRENT_TAB_INDEX', 1)
    // 清除未读消息计数
    this.$store.commit('setunReadmessage', 0);

    // 检查登录状态并加载数据
    if (this.isLoggedIn) {
      this.fetchMessageData();
    } else {
      this.isDataLoaded = true;
    }
  },
  onLoad() {
    uni.hideTabBar()
  },
  /**
   * 下拉刷新处理函数
   */
  onPullDownRefresh() {
    // 如果用户未登录，直接停止下拉刷新
    if (!this.isLoggedIn) {
      uni.stopPullDownRefresh();
      return;
    }

    // 重置当前页码为1
    this.currentPage = 1;

    // 显示刷新加载动画
    uni.showLoading({
      title: '刷新中...',
      mask: true
    });

    // 刷新消息数据
    this.fetchMessageData().then(() => {
      // 完成后隐藏加载提示并停止下拉刷新动画
      uni.hideLoading();
      uni.stopPullDownRefresh();

      // 显示刷新成功提示
      uni.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      });
    }).catch(err => {
      console.error('刷新数据失败:', err);
      uni.hideLoading();
      uni.stopPullDownRefresh();

      // 显示刷新失败提示
      uni.showToast({
        title: '刷新失败',
        icon: 'none',
        duration: 1500
      });
    });
  },
  methods: {
    /**
     * 处理登录按钮点击
     */
    handleLoginButtonClick() {
      this.$showLogin({
        success: () => {
          // 登录成功后重新获取数据
          this.fetchMessageData();
        }
      });
    },

    /**
     * 获取消息数据
     * @returns {Promise} 返回一个Promise对象
     */
    async fetchMessageData() {
      try {
        const res = await this.$api.get_msgList({
          feedType: this.currentFeedType,
          pageNum: this.currentPage,
          pageSize: 10
        });

        // 处理工具类消息的特殊格式
        res.rows.forEach(item => {
          if (item.feedType === 'tool') {
            item.message = JSON.parse(item.content);
          } else {
            item.message = { text: JSON.parse(item.content).text }; // 非工具类也统一结构
          }
        });

        // 根据当前页码决定是追加还是替换数据
        if (this.currentPage === 1) {
          this.messageList = res.rows;
        } else {
          this.messageList = [...this.messageList, ...res.rows];
        }

        this.totalCount = res.total;
        this.hasMoreData = this.messageList.length < this.totalCount;
        this.isDataLoaded = true;

        return Promise.resolve(); // 明确返回一个已解决的Promise
      } catch (error) {
        this.isDataLoaded = true;
        return Promise.reject(error); // 返回一个被拒绝的Promise
      }
    },

    /**
     * 导航到消息详情页
     * @param {Object} message 消息数据
     */
    navigateToMessageDetail(message) {
      uni.navigateTo({
        url: '/subpkg/home_two/message',
        success(res) {
          res.eventChannel.emit('get_message', message);
        }
      });
    },

    /**
     * 根据消息类型导航到对应详情页
     * @param {Object} message 消息数据
     */
    navigateToDetail(message) {
      let targetUrl = '';

      switch (message.linkType) {
        case 'power':
          targetUrl = '/subpkg/home_four/myPower';
          break;
        case 'tran':  // 训练数字人
          targetUrl = '/subpkg/home_four/dr_list';
          break;
        case 'add':
          uni.switchTab({ url: '/pages/home_three/index' });
          break;
        case 'remove':
          uni.showToast({
            title: "您已被移除频道",
            icon: "none"
          });
          return;
      }

      uni.navigateTo({
        url: targetUrl
      });
    },

    /**
     * 获取消息图标
     * @param {Object} message 消息数据
     * @returns {String} 图标URL
     */
    getMessageIcon(message) {
      if (message.icon) {
        return message.icon;
      } else if (message.feedType === 'system') {
        return this.$appConfig.appInfo.ossPath + '/project1_home2_tool_icon2.png';
      } else {
        if (message.linkType === 'video') {
          return '/static/home_too/icon_dongtai.png';
        } else {
          return '/static/批量视频头像.png';
        }
      }
    },

    /**
     * 处理视频播放
     * @param {String} url 视频URL
     */
    handleVideoPlay(url) {
      uni.navigateTo({
        url: `/subpkg/index/get_radio/look_my_video?url=${encodeURIComponent(url)}`
      });
    },

    /**
     * 处理合集视频播放
     * @param {Object} message 消息数据
     */
    handleBatchVideoPlay(message) {
      if (message.message.status === 'generating') {
        uni.showToast({
          title: '视频生成中，请稍等',
          icon: 'none'
        });
        return;
      }

      if (message.message.status === 'success') {
        // 如果有batchId，跳转到批量视频详情页
        if (message.linkId) {
          uni.navigateTo({
            url: `/subpkg/batchVideoDetail/index?workId=${message.linkId}`
          });
        } else {
          uni.showToast({
            title: '视频地址不存在',
            icon: 'none'
          });
        }
      } else {
        uni.showToast({
          title: '视频还未生成完成',
          icon: 'none'
        });
      }
    },

    /**
     * 处理分类标签切换
     * @param {Object} category 分类数据
     * @param {Number} index 分类索引
     */
    handleTabChange(category, index) {
      this.activeTabIndex = index;
      this.currentFeedType = category.type;
      this.currentPage = 1;

      // 只有在登录状态下才请求数据
      if (this.isLoggedIn) {
        this.fetchMessageData();
      }
    },

    /**
     * 处理加载更多数据
     */
    handleLoadMore() {
      if (!this.hasMoreData || !this.isLoggedIn) return;

      this.currentPage++;
      this.fetchMessageData();
    }
  }
}
</script>

<style scoped lang="scss">
.page-container {
  background: $project_1_bg_color;
  padding: 20rpx;
  height: 100vh;
}

.message-scroll {
  margin-top: 40rpx;
  height: calc(100vh - 280rpx);
  background: white;
  border-radius: 20rpx;
}

.category-tabs {
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 600rpx;
  border-radius: 50rpx;
  height: 60rpx;
  background: white;
  padding: 40rpx 20rpx;

  .tab-normal {
    font-size: 26rpx;
    color: rgb(179, 178, 183);
    border-radius: 30rpx;
    padding: 10rpx 20rpx;
  }

  .tab-active {
    font-size: 26rpx;
    border-radius: 30rpx;
    padding: 10rpx 20rpx;
    color: white;
    background: black;
  }
}

.message-card {
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
  padding: 0 20rpx;

  .message-icon {
    width: 80rpx;
    margin-right: 20rpx;

    image {
      width: 80rpx;
      height: 80rpx;
    }
  }

  .message-content {
    flex: 1;
  }

  .message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .message-title {
      font-size: 31rpx;

      color: #04cf60;
    }

    .message-time {
      font-size: 24rpx;
      color: #919191;
    }
  }

  .message-text {
    margin-top: 5rpx;
    margin-bottom: 8rpx;
    font-size: 28rpx;
    color: #666666;
    line-height: 36rpx;
  }

  .message-detail-btn {
    display: inline-block;
    font-size: 24rpx;
    color: #575757;
    background: #f6f6f6;
    border-radius: 10rpx;
    padding: 8rpx 12rpx;
  }
}

.video-thumbnail {
  width: 200rpx;
  height: 300rpx;
  position: relative;

  .video-cover {
    width: 100%;
    height: 100%;
    border-radius: 20rpx;
    margin-top: 10rpx;
  }
}

.batch-video-thumbnail {
  background-image: url('/static/大堆叠.png');
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;

  .video-cover {
    width: 192rpx;
    height: 292rpx;
    position: absolute;
    border-radius: 20rpx;
    left: -4rpx;
    top: -4rpx;
  }

  .tag {
    position: absolute;
    left: 20rpx;
    top: 20rpx;
    color: white;
    background-color: rgba(0, 0, 0, 0.4);
    font-size: 24rpx;
    padding: 5rpx 10rpx;
    border-radius: 10rpx;
    z-index: 999;
  }

  .bottom-text {
    width: 192rpx;
    position: absolute;
    bottom: 10rpx;
    left: -4rpx;
    padding: 6rpx;
    background-color: rgba(0, 0, 0, 0.4);
    font-size: 24rpx;
    border-bottom-left-radius: 20rpx;
    border-bottom-right-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    z-index: 999;
  }

  .mask {
    width: 192rpx;
    height: 292rpx;
    position: absolute;
    border-radius: 20rpx;
    left: -4rpx;
    top: -4rpx;
    z-index: 2;
    background-color: rgba(0, 0, 0, 0.3);
  }
}

.batch-video-thumbnail {
  width: 200rpx;
  height: 300rpx;
  position: relative;
  border-radius: 16rpx;
  overflow: hidden;

  .batch-video-cover {
    width: 100%;
    height: 100%;
    border-radius: 16rpx;
    margin-top: 10rpx;
  }

  .batch-video-placeholder {
    position: absolute;
    width: 64rpx;
    height: 32rpx;
    background: linear-gradient(135deg, #22232C 20%, #0F0F0F 100%);
    border-radius: 8rpx 4rpx 16rpx 4rpx;
    right: 10rpx;
    bottom: 18rpx;
    font-weight: 600;
    font-size: 16rpx;
    color: #FFFFFF;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
  }

  .batch-loading {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
    // 苹果毛玻璃效果
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10rpx);
    -webkit-backdrop-filter: blur(10rpx);
    border-radius: 16rpx;
    border: 1rpx solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

    // 兼容性处理：如果不支持backdrop-filter，使用渐变背景模拟
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
          rgba(255, 255, 255, 0.3) 0%,
          rgba(255, 255, 255, 0.1) 50%,
          rgba(255, 255, 255, 0.2) 100%);
      border-radius: 16rpx;
      z-index: -1;
    }

    image {
      z-index: 1;
      position: relative;
      width: 48rpx;
      height: 48rpx;
    }
  }
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 100rpx;

  image {
    width: 60rpx;
    height: 60rpx;
  }
}

.truncate-text {
  margin-top: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  position: relative;
  font-size: 26rpx;
}

.view-more-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  font-size: 26rpx;
  color: #6f8dee;
  background: white;
}

.message-divider {
  background: white;
  padding: 15rpx;
  width: 95%;
  margin: auto;
  border-bottom: 1px solid #D8D8D8;
  transform: scaleY(0.5);
  transform-origin: 0 100%;
}

.generating-container {
  position: relative;
  width: 30vw;

  .generating-bg {
    width: 30vw;
    border-radius: 20rpx;
    margin-top: 10rpx;
  }
}

.generating-content {
  position: absolute;
  width: 100%;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  font-size: 24rpx;

  .generating-text {
    font-weight: bold;
    color: #eeeeee;
  }

  .generating-time {
    color: #efefef;
  }
}

.loading-icon {
  animation: rotate 1s linear infinite;
  width: 60rpx;
  height: 60rpx;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.blur-background {
  filter: blur(7rpx);
}

.empty-state {
  margin-top: 300rpx;
  text-align: center;
}

.module-login-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 60rpx;
  background: white;
  border-radius: 20rpx;
  padding: 100rpx 0;
  text-align: center;

  .login-prompt-image {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 30rpx;
  }

  .login-prompt-text {
    font-size: 30rpx;
    color: #666;
    margin-bottom: 40rpx;
  }

  .login-btn {
    width: 240rpx;
    height: 80rpx;
    line-height: 80rpx;
    background-color: #000;
    color: #fff;
    border-radius: 40rpx;
    font-size: 28rpx;
  }
}

.login-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 200rpx;

  .login-prompt-image {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 30rpx;
  }

  .login-prompt-text {
    font-size: 30rpx;
    color: #666;
    margin-bottom: 40rpx;
  }

  .login-btn {
    width: 300rpx;
    height: 80rpx;
    line-height: 80rpx;
    background-color: #000;
    color: #fff;
    border-radius: 40rpx;
    font-size: 28rpx;
  }
}
</style>