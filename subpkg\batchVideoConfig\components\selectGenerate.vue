<template>
  <view class="generate-container" :style="{ paddingBottom: generateContainerBottom + 'px' }">
    <!-- 批量对象区域 -->
    <view class="section-header">
      <text class="section-title">批量对象</text>
      <view class="section-title-line"></view>
    </view>

    <!-- 批量对象内容 -->
    <view class="batch-objects-card">
      <!-- 模板信息 -->
      <view class="object-item" :style="{ paddingBottom: '0rpx', paddingTop: '0rpx', borderBottom: 'none' }">
        <view class="item-title">
          <image class="icon-img" src="/static/模板icon.png"></image>
          <text class="title-text">模板</text>
        </view>
        <view class="item-count">
          <text class="count-text">共<text class="count-number">{{ templates.length }}</text> 个</text>
          <!-- <image class="arrow-img" src="/static/右箭头icon.png"></image> -->
        </view>
      </view>

      <!-- 模板预览 -->
      <view class="template-preview" style="border-bottom:1rpx solid #f0f0f0;">
        <view class="template-item" v-for="(item, index) in templates" :key="index">
          <image class="template-img" :src="item.preUrl"></image>
          <view class="template-name">
            <text class="name-text">{{ item.name }}</text>
          </view>
        </view>
      </view>

      <!-- 文案信息 -->
      <view class="object-item">
        <view class="item-title">
          <image class="icon-img" src="/static/文案icon.png"></image>
          <text class="title-text">文案</text>
        </view>
        <view class="item-count">
          <text class="count-text">共<text class="count-number">{{ topicList.length }}</text> 条</text>
          <!-- <image class="arrow-img" src="/static/右箭头icon.png"></image> -->
        </view>
      </view>

      <!-- 素材信息 -->
      <view class="object-item">
        <view class="item-title">
          <image class="icon-img" src="/static/素材icon.png"></image>
          <text class="title-text">素材</text>
        </view>
        <view class="item-count">
          <text class="count-text">共<text class="count-number">{{ assetInfo.selectedIds.length }}</text> 个</text>
          <!-- <image class="arrow-img" src="/static/右箭头icon.png"></image> -->
        </view>
      </view>

      <!-- 音乐信息 -->
      <view class="object-item" :style="{ borderBottom: 'none', paddingBottom: '0rpx' }">
        <view class="item-title">
          <image class="icon-img" src="/static/音乐icon.png"></image>
          <text class="title-text">音乐</text>
        </view>
        <view class="item-count">
          <text class="count-text">共<text class="count-number">{{ bgMuseicList.length }}</text> 首</text>
          <!-- <image class="arrow-img" src="/static/右箭头icon.png"></image> -->
        </view>
      </view>
    </view>

    <!-- 生成规则区域 -->
    <view class="section-header">
      <text class="section-title">生成规则</text>
      <view class="section-title-line"></view>
    </view>

    <!-- 生成规则内容 -->
    <view class="generate-rules-card" style="">
      <!-- 生成视频的数量 -->
      <view class="rule-title">
        <image class="rule-icon" src="/static/视频规则icon.png"></image>
        <text class="rule-text">生成视频的数量</text>
      </view>

      <!-- 每条文案生成视频的数量 -->
      <view class="video-count-setting">
        <text class="setting-label">每条文案生成视频的数量</text>
        <view class="count-adjuster">
          <view :class="['btn-minus', { 'disabled': rule.sginGenCount <= 1 }]" @click="decreaseCount">
            <image class="btn-icon" src="/static/减号.png"></image>
          </view>
          <view class="count-input">
            <text class="count-value">{{ rule.sginGenCount }}</text>
          </view>
          <view class="btn-plus" @click="increaseCount">
            <image class="btn-icon" src="/static/加号.png"></image>
          </view>
        </view>
      </view>

      <!-- 使用规则 -->
      <view class="rule-title" style="padding-top: 32rpx;">
        <image class="rule-icon" src="/static/使用规则icon.png"></image>
        <text class="rule-text">使用规则</text>
      </view>

      <!-- 使用数字人模板 -->
      <view class="rule-setting">
        <text class="setting-label">使用数字人 / 模板</text>
        <view class="setting-options">
          <view :class="['option-item', { 'active': rule.randomDrFlag === true }]" @click="setTemplateRule(true)">
            <text class="option-text">顺序使用</text>
          </view>
          <view :class="['option-item', { 'active': rule.randomDrFlag === false }]" @click="setTemplateRule(false)">
            <text class="option-text">随机使用</text>
          </view>
        </view>
      </view>

      <!-- 使用素材 -->
      <view class="rule-setting">
        <text class="setting-label">使用素材</text>
        <view class="setting-option active">
          <text class="option-text">文案语义匹配</text>
        </view>
      </view>

      <!-- 使用背景音乐 -->
      <view class="rule-setting" style="padding-bottom: 32rpx;border-bottom:1rpx solid #f0f0f0;">
        <text class="setting-label">使用背景音乐</text>
        <view class="setting-options">
          <view :class="['option-item', { 'active': rule.randomBgFlag === true }]" @click="setMusicRule(true)">
            <text class="option-text">顺序使用</text>
          </view>
          <view :class="['option-item', { 'active': rule.randomBgFlag === false }]" @click="setMusicRule(false)">
            <text class="option-text">随机使用</text>
          </view>
        </view>
      </view>

      <!-- 去重方式 -->
      <view class="rule-title">
        <image class="rule-icon" src="/static/去重方式icon.png"></image>
        <text class="rule-text">去重方式</text>
      </view>

      <!-- AI去重改写文案 -->
      <view class="switch-setting" style="margin-bottom: 32rpx;">
        <view class="switch-label">
          <text class="label-text">AI去重改写文案</text>
          <image class="help-icon" src="/static/batch_video/问号.png"></image>
        </view>
        <switch color="#21BD74" :checked="rule.rewriteFlag" @change="toggleAiRewriteText"></switch>
      </view>

      <!-- AI智能配图 -->
      <view class="switch-setting" style="margin-bottom: 0rpx;">
        <view class="switch-label">
          <text class="label-text">AI智能配图</text>
          <image class="help-icon" src="/static/batch_video/问号.png"></image>
        </view>
        <switch color="#21BD74" :checked="rule.aiSmartPicFlag" @change="toggleAiSmartPic"></switch>
      </view>
    </view>

    <!-- 底部按钮区域需添加上一步按钮 -->
    <view class="bottom-btn-container" :style="{ paddingBottom: safeAreaBottom + 'px' }">
      <view class="prev-btn" @click="prevStep">
        <text class="prev-btn-text">上一步</text>
      </view>
      <view class="bottom-btn" @click="startGenerate">
        <text class="btn-text">批量生成视频（{{ totalVideoCount }}个）</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      safeAreaBottom: 0, // 安全区域底部
      generateContainerBottom: 0 // 生成容器底部
    };
  },
  props: {
    rule: {
      type: Object,
      default: () => ({})
    },
    templates: {
      type: Array,
      default: () => []
    },
    topicList: {
      type: Array,
      default: () => []
    },
    assetInfo: {
      type: Object,
      default: () => ({})
    },
    bgMuseicList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    // 计算总共要生成的视频数量
    totalVideoCount() {
      return this.rule.sginGenCount * this.topicList.length;
    }
  },
  mounted() {
    this.safeAreaBottom = uni.getSystemInfoSync().safeAreaInsets.bottom + uni.upx2px(16);
    this.generateContainerBottom = this.safeAreaBottom + uni.upx2px(140);
  },
  methods: {
    // 从store中加载数据
    loadDataFromStore() {
      // 这里可以从vuex或全局数据中读取前面步骤保存的数据
      // 例如获取模板数量、文案数量等
      // 这里示例暂时使用硬编码的数据
    },

    // 减少每条文案生成视频的数量
    decreaseCount() {
      // 确保数量不能小于1
      if (this.rule.sginGenCount > 1) {
        this.$emit('saveRule', {
          ...this.rule,
          sginGenCount: this.rule.sginGenCount - 1
        })
      }
    },

    // 增加每条文案生成视频的数量
    increaseCount() {
      this.$emit('saveRule', {
        ...this.rule,
        sginGenCount: this.rule.sginGenCount + 1
      })
    },

    // 设置模板使用规则
    setTemplateRule(value) {
      this.$emit('saveRule', {
        ...this.rule,
        randomDrFlag: value
      })
    },

    // 设置音乐使用规则
    setMusicRule(value) {
      this.$emit('saveRule', {
        ...this.rule,
        randomBgFlag: value
      })
    },

    // 切换AI去重改写文案开关
    toggleAiRewriteText(e) {
      this.$emit('saveRule', {
        ...this.rule,
        rewriteFlag: e.detail.value
      })
    },

    // 切换AI智能配图开关
    toggleAiSmartPic(e) {
      this.$emit('saveRule', {
        ...this.rule,
        aiSmartPicFlag: e.detail.value
      })
    },

    // 返回上一步
    prevStep() {
      uni.$emit('batch-video-prev-step');
    },

    // 开始生成视频
    startGenerate() {
      this.$emit('startGenerate')
    }
  }
};
</script>

<style lang="scss">
.generate-container {
  background-color: #f3f5f8;
  width: 100%;
  height: 100%;
  padding-bottom: 140rpx; // 为底部按钮预留空间

  // 区域标题样式
  .section-header {
    position: relative;
    margin: 40rpx 0 30rpx 32rpx;

    .section-title {
      font-size: 36rpx;
      font-weight: bold;
      background-image: linear-gradient(135deg, #22232c 19.84%, #0f0f0f 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      line-height: 56rpx;
    }

    .section-title-line {
      position: absolute;
      left: 72rpx;
      bottom: -8rpx;
      width: 72rpx;
      height: 6rpx;
      background-image: linear-gradient(135deg, #07e3d2 0%, #01f770 100%);
      border-radius: 4rpx 2rpx 2rpx 4rpx;
    }
  }

  // 批量对象卡片
  .batch-objects-card {
    // margin: 0 32rpx;
    background-color: #FFFFFF;
    border-radius: 24rpx;
    padding: 32rpx;

    // 对象项目
    .object-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      .item-title {
        display: flex;
        align-items: center;

        .icon-img {
          width: 32rpx;
          height: 32rpx;
          margin-right: 16rpx;
        }

        .title-text {
          font-size: 28rpx;
          color: #333333;
          font-weight: 600;
          line-height: 40rpx;
        }
      }

      .item-count {
        display: flex;
        align-items: center;

        .count-text {
          font-size: 24rpx;
          color: #999999;
          margin-right: 8rpx;
          text-align: right;

          .count-number {
            color: #21BD74;
          }
        }

        .arrow-img {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }

    // 模板预览
    .template-preview {
      display: flex;
      // margin: 16rpx 0;
      padding: 32rpx 0;

      .template-item {
        width: 144rpx;
        height: 200rpx;
        margin-right: 16rpx;
        position: relative;
        border-radius: 8rpx;
        overflow: hidden;

        .template-img {
          width: 100%;
          height: 100%;
          border-radius: 8rpx;
        }

        .template-name {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background-color: rgba(0, 0, 0, 0.15);
          height: 40rpx;
          display: flex;
          align-items: center;
          padding: 0 16rpx;
          border-radius: 0 0 8rpx 8rpx;

          .name-text {
            font-size: 20rpx;
            color: #FFFFFF;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }

  // 生成规则卡片
  .generate-rules-card {
    // margin: 0 32rpx;
    background-color: #FFFFFF;
    border-radius: 24rpx;
    padding: 32rpx;

    // 规则标题
    .rule-title {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      .rule-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 16rpx;
      }

      .rule-text {
        font-size: 28rpx;
        color: #333333;
        font-weight: 600;
      }
    }

    // 视频数量设置
    .video-count-setting {
      display: flex;
      align-items: center;
      padding-bottom: 32rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .setting-label {
        font-size: 24rpx;
        color: #333333;
        margin-right: 70rpx;
      }

      .count-adjuster {
        display: flex;
        align-items: center;

        .btn-minus,
        .btn-plus {
          width: 64rpx;
          height: 64rpx;
          border: 1rpx solid #f0f0f0;
          background-color: #FFFFFF;
          border-radius: 8rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          .btn-icon {
            width: 32rpx;
            height: 32rpx;
          }

          &.disabled {
            background-color: #f5f5f5;
            border-color: #e0e0e0;
            opacity: 0.6;
            cursor: not-allowed;

            .btn-icon {
              opacity: 0.5;
            }
          }
        }

        .count-input {
          width: 128rpx;
          height: 64rpx;
          border: 1rpx solid #f0f0f0;
          margin: 0 16rpx;
          border-radius: 8rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          .count-value {
            font-size: 24rpx;
            color: #333333;
          }
        }
      }
    }

    // 规则设置
    .rule-setting {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;

      .setting-label {
        font-size: 24rpx;
        color: #333333;
      }

      .setting-options {
        display: flex;
        width: 288rpx;
        height: 56rpx;
        background-color: #F3F5F8;
        border-radius: 12rpx;
        overflow: hidden;

        .option-item {
          width: 144rpx;
          height: 56rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          .option-text {
            font-size: 24rpx;
            color: #999999;
          }

          &.active {
            background-image: linear-gradient(135deg, #22232c 19.84%, #0f0f0f 100%);

            .option-text {
              color: #FFFFFF;
            }
          }
        }
      }

      .setting-option {
        width: 192rpx;
        height: 56rpx;
        background-image: linear-gradient(135deg, #22232c 19.84%, #0f0f0f 100%);
        border-radius: 12rpx;
        display: flex;
        align-items: center;
        padding-left: 24rpx;

        .option-text {
          font-size: 24rpx;
          color: #FFFFFF;
        }
      }
    }

    // 开关设置
    .switch-setting {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;

      .switch-label {
        display: flex;
        align-items: center;

        .label-text {
          font-size: 24rpx;
          color: #333333;
          margin-right: 8rpx;
        }

        .help-icon {
          width: 24rpx;
          height: 24rpx;
        }
      }
    }
  }

  // 底部按钮容器
  .bottom-btn-container {
    position: fixed;
    width: 100%;
    padding: 16rpx 32rpx;
    left: 0rpx;
    bottom: 0rpx;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .prev-btn {
      width: 120rpx;
      height: 96rpx;
      background-color: #efefef;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;

      .prev-btn-text {
        font-size: 28rpx;
        color: #666666;
        font-weight: 600;
      }
    }

    .bottom-btn {
      flex: 1;
      height: 96rpx;
      background-image: linear-gradient(135deg, #22232c 0%, #0f0f0f 100%);
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .btn-text {
        font-size: 34rpx;
        color: #FFFFFF;
        font-weight: 400;
        font-family: asdasd;
      }
    }
  }
}
</style>