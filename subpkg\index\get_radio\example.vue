<template>
	<view>
    <video :src="demoUrl" controls style="width: 100%; height: 100vh;"></video>

  </view>
</template>

<script>
	import {getPageConfigInfo} from "@/api/app/config";

  export default{
		data(){
			return{
				demoUrl:''
			}
		},
    onLoad() {
      getPageConfigInfo({type:'demoUrl'}).then(res=>{

      this.demoUrl = JSON.parse(res.data).demoVideoUrl
      })
    },
	}
</script>

<style scoped lang="scss">
</style>