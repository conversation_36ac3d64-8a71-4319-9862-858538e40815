<template>
  <view class="test-container">
    <custom-navbar title="画布拖拽测试" titleColor="#333333"></custom-navbar>

    <!-- 拖拽测试区域 -->
    <view class="canvas-container">
      <movable-area class="movable-area">
        <movable-view class="movable-view" :x="position.x" :y="position.y" :scale="scale" :scale-min="scaleMin"
          :scale-max="scaleMax" :scale-value="scaleValue" :direction="direction" :disabled="disabled" :inertia="inertia"
          :out-of-bounds="outOfBounds" :damping="damping" :friction="friction" :animation="animation" @change="onChange"
          @scale="onScale" @htouchmove="onHTouchMove" @vtouchmove="onVTouchMove" v-if="show">
          <view class="content-box-relative">
            <view class="content-box-absolute">
              <text>{{position.x}}</text>
              <view class="coordinate">
                <text>{{ position.y }}</text>
                <text v-if="scaleEnabled">缩放: {{ scaleValue.toFixed(2) }}</text>
              </view>
            </view>
          </view>
        </movable-view>
      </movable-area>
    </view>

    <!-- 控制面板 -->
    <view class="control-panel">
      <view class="panel-title">控制面板</view>

      <!-- 方向控制 -->
      <view class="control-group">
        <text class="group-title">拖拽方向:</text>
        <view class="button-group">
          <button class="control-btn" :class="{ active: direction === 'all' }" @tap="setDirection('all')">全部</button>
          <button class="control-btn" :class="{ active: direction === 'horizontal' }"
            @tap="setDirection('horizontal')">水平</button>
          <button class="control-btn" :class="{ active: direction === 'vertical' }"
            @tap="setDirection('vertical')">垂直</button>
          <button class="control-btn" :class="{ active: direction === 'none' }" @tap="setDirection('none')">无</button>
        </view>
      </view>

      <!-- 拖拽开关 -->
      <view class="control-item">
        <text>禁用拖拽:</text>
        <switch :checked="disabled" @change="toggleDisabled" />
      </view>

      <!-- 缩放开关 -->
      <view class="control-item">
        <text>启用缩放:</text>
        <switch :checked="scaleEnabled" @change="toggleScale" />
      </view>

      <!-- 惯性开关 -->
      <view class="control-item">
        <text>启用惯性:</text>
        <switch :checked="inertia" @change="toggleInertia" />
      </view>

      <!-- 越界回弹开关 -->
      <view class="control-item">
        <text>允许越界:</text>
        <switch :checked="outOfBounds" @change="toggleOutOfBounds" />
      </view>

      <!-- 动画开关 -->
      <view class="control-item">
        <text>启用动画:</text>
        <switch :checked="animation" @change="toggleAnimation" />
      </view>

      <!-- 位置重置 -->
      <view class="control-group">
        <button class="reset-btn" @tap="resetPosition">重置位置</button>
      </view>
    </view>

    <!-- 日志输出 -->
    <view class="log-container">
      <text class="log-title">操作日志:</text>
      <scroll-view class="log-box" scroll-y="true">
        <view class="log-item" v-for="(log, index) in logs" :key="index">
          {{ log }}
        </view>
      </scroll-view>
      <button class="control-btn" @tap="clearLogs">清除日志</button>
    </view>
  </view>
</template>

<script>
import customNavbar from '@/components/custom-navbar/custom-navbar.vue';

export default {
  components: {
    customNavbar
  },
  data() {
    return {
      // 位置信息
      position: {
        x: 100,
        y: 100
      },
      // 移动属性
      direction: 'all', // all, horizontal, vertical, none
      disabled: false,
      // 缩放属性
      scaleEnabled: false,
      scale: false,
      scaleMin: 0.5,
      scaleMax: 2,
      scaleValue: 1,
      // 惯性与回弹
      inertia: false,
      outOfBounds: false,
      damping: 20,
      friction: 2,
      // 动画
      animation: false,
      // 日志
      logs: [],
      show: false
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.addLog('画布拖拽测试初始化完成');
    });
    setTimeout(() => {
      this.show = true
    }, 1000)
  },
  methods: {
    // 设置拖拽方向
    setDirection(direction) {
      this.direction = direction;
      this.addLog(`设置拖拽方向: ${direction}`);
    },

    // 切换禁用拖拽
    toggleDisabled(e) {
      this.disabled = e.detail.value;
      this.addLog(`${this.disabled ? '禁用' : '启用'}拖拽`);
    },

    // 切换缩放
    toggleScale(e) {
      this.scaleEnabled = e.detail.value;
      this.scale = this.scaleEnabled;
      this.addLog(`${this.scaleEnabled ? '启用' : '禁用'}缩放`);
    },

    // 切换惯性
    toggleInertia(e) {
      this.inertia = e.detail.value;
      this.addLog(`${this.inertia ? '启用' : '禁用'}惯性`);
    },

    // 切换越界
    toggleOutOfBounds(e) {
      this.outOfBounds = e.detail.value;
      this.addLog(`${this.outOfBounds ? '允许' : '禁止'}越界`);
    },

    // 切换动画
    toggleAnimation(e) {
      this.animation = e.detail.value;
      this.addLog(`${this.animation ? '启用' : '禁用'}动画`);
    },

    // 重置位置
    resetPosition() {
      this.position = {
        x: 150,
        y: 150
      };
      this.scaleValue = 1;
      this.addLog('重置位置和缩放');
    },

    // 移动事件
    onChange(e) {
      // const { x, y, source } = e.detail;
      // this.position.x = x;
      // this.position.y = y;

      // // 只有用户操作才记录日志，避免日志过多
      // if (source === 'touch') {
      //   this.addLog(`移动: X=${x.toFixed(0)}, Y=${y.toFixed(0)}`);
      // }
    },

    // 缩放事件
    onScale(e) {
      const { scale } = e.detail;
      this.scaleValue = scale;
      this.addLog(`缩放: ${scale.toFixed(2)}`);
    },

    // 水平触摸移动
    onHTouchMove(e) {
      // 可以添加水平移动的特殊处理
    },

    // 垂直触摸移动
    onVTouchMove(e) {
      // 可以添加垂直移动的特殊处理
    },

    // 添加日志
    addLog(message) {
      const now = new Date();
      const timeStr = [
        now.getHours().toString().padStart(2, '0'),
        now.getMinutes().toString().padStart(2, '0'),
        now.getSeconds().toString().padStart(2, '0')
      ].join(':');

      this.logs.unshift(`${timeStr} - ${message}`);

      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs.pop();
      }
    },

    // 清空日志
    clearLogs() {
      this.logs = [];
      this.addLog('日志已清除');
    }
  }
}
</script>

<style lang="scss">
.test-container {
  padding: 20rpx;

  .canvas-container {
    width: 100%;
    padding: 20rpx 0;
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30rpx;

    .movable-area {
      width: 600rpx;
      height: 600rpx;
      background-color: #e0e0e0;
      border: 2rpx dashed #999;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        width: 100%;
        height: 2rpx;
        background-color: rgba(0, 0, 0, 0.1);
      }

      &::after {
        content: '';
        position: absolute;
        left: 50%;
        top: 0;
        width: 2rpx;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.1);
      }

      .movable-view {
        // width: 200rpx;
        // height: 200rpx; 
        background: linear-gradient(135deg, #4CAF50, #2196F3);
        border-radius: 10rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);

        .content-box-relative {
          position: relative;
          width: 100%;
          height: 100%;

          .content-box-absolute {
            position: absolute;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            left: 30rpx;
            top: 30rpx;

            text {
              font-size: 32rpx;
              font-weight: bold;
              margin-bottom: 10rpx;
            }

            .coordinate {
              font-size: 24rpx;
              display: flex;
              flex-direction: column;
              align-items: center;
            }
          }
        }
      }
    }
  }

  .control-panel {
    background-color: #f5f5f5;
    border-radius: 10rpx;
    padding: 20rpx;
    margin-bottom: 30rpx;

    .panel-title {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
      text-align: center;
    }

    .control-group {
      margin-bottom: 20rpx;

      .group-title {
        font-size: 28rpx;
        margin-bottom: 10rpx;
        display: block;
      }

      .button-group {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        .control-btn {
          width: 23%;
          margin-bottom: 10rpx;
          font-size: 24rpx;
          padding: 0;
          background-color: #e0e0e0;
          color: #333;

          &.active {
            background-color: #2196F3;
            color: white;
          }
        }
      }
    }

    .control-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      padding: 10rpx;
      border-bottom: 1rpx solid #e0e0e0;

      text {
        font-size: 28rpx;
      }
    }

    .reset-btn {
      width: 100%;
      background-color: #FF9800;
      color: white;
      font-weight: bold;
    }
  }

  .log-container {
    background-color: #f5f5f5;
    border-radius: 10rpx;
    padding: 20rpx;

    .log-title {
      font-size: 28rpx;
      font-weight: bold;
      margin-bottom: 10rpx;
      display: block;
    }

    .log-box {
      height: 300rpx;
      border: 1px solid #ddd;
      border-radius: 10rpx;
      background-color: #fff;
      padding: 10rpx;
      margin-bottom: 20rpx;

      .log-item {
        font-size: 24rpx;
        padding: 10rpx;
        border-bottom: 1px solid #eee;
      }
    }

    .control-btn {
      width: 100%;
      background-color: #f44336;
      color: white;
    }
  }
}
</style>
