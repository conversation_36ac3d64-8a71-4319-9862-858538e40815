<template>
	<view>
		<view class="box">
			<view class="card">
				<view style="width: 80rpx;margin-right: 20rpx;">
					<image :src="item.icon ? item.icon : '/static/home_too/icon_dongtai.png'"></image>
				</view>
				<view class="card_text">
					<view class="title">{{ item.title }}</view>
					<view class="time">{{ item.createTime }}</view>
				</view>
			</view>

			<view class="message_content">
				<view v-if="page_title" style="font-size: 30rpx;font-weight: 600;font-size: 36rpx;">{{ page_title }}</view>
				<view style="margin-top: 20rpx;font-size: 32rpx;line-height: 1.8;" v-for="item in content_list">
					{{ item }}
				</view>

				<!-- 视频预览区域 -->
				<view v-if="videoUrl" class="video-preview" @click="playVideo">
					<image class="video-cover" :src="videoCover || '/static/home_too/video_placeholder.png'" mode="aspectFill">
					</image>
					<view class="play-icon">
						<image :src="ossPath + '/project1_video_play.png'" mode="aspectFit"></image>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作按钮 -->
		<view class="bottom-actions" :style="{ paddingBottom: safeArea + 'px' }">
			<view class="action-btn refresh-btn" @click="regenerateVideo">
				<image src="/static/再次生成.png"></image>
				<text>再次生成</text>
			</view>
			<view class="action-btn copy-btn" @click="copyContent">
				<image src="/static/复制文案.png"></image>
				<text>复制文案</text>
			</view>
			<view class="action-btn download-btn" @click="downloadVideo">
				<image src="/static/下载视频.png"></image>
				<text>下载视频</text>
			</view>
		</view>
	</view>
</template>

<script>
import {
	Copywriting
} from '../../utils/file.js'
import { mapState } from 'vuex'

export default {
	data() {
		return {
			item: {},
			page_title: '',
			content_list: '',
			videoUrl: '', // 视频URL
			videoCover: '', // 视频封面
			contentRaw: '', // 原始内容字符串
			safeArea: 0, // 安全区域
			ossPath: this.$appConfig.appInfo.ossPath
		}
	},
	onLoad() {
		// 获取安全区域
		this.safeArea = uni.getSystemInfoSync().safeAreaInsets.bottom + uni.upx2px(20);

		this.getOpenerEventChannel().on('get_message', (data) => {
			this.item = data;
			console.log(data, 'data');


			try {
				const contentJson = JSON.parse(data.content);
				let option = Copywriting(contentJson.text);
				this.page_title = option.title;
				this.content_list = option.content_list;
				this.contentRaw = contentJson.text;
				console.log(contentJson, 'contentJson');

				// 处理视频URL和封面
				if (contentJson.url) {
					this.videoUrl = contentJson.url;
				}

				if (contentJson.cover) {
					this.videoCover = contentJson.cover;
				}


				console.log('视频链接:', this.videoUrl);
			} catch (e) {
				console.error('内容解析错误:', e);
			}
		});
	},
	methods: {
		//复制
		copy_message(message) {
			uni.setClipboardData({
				data: message,
				success: () => {
					uni.showToast({
						title: '复制成功',
						icon: 'none'
					});
				},
				fail: () => {
					uni.showToast({
						title: '复制失败',
						icon: 'none'
					});
				}
			});
		},

		// 播放视频
		playVideo() {
			if (this.videoUrl) {
				uni.navigateTo({
					url: `/subpkg/index/get_radio/look_my_video?url=${encodeURIComponent(this.videoUrl)}`
				});
			} else {
				uni.showToast({
					title: '视频链接不存在',
					icon: 'none'
				});
			}
		},

		// 复制内容
		copyContent() {
			if (this.contentRaw) {
				this.copy_message(this.contentRaw);
			} else {
				let content = `${this.page_title}\n`;
				if (this.content_list && this.content_list.length) {
					content += this.content_list.join('\n');
				}
				this.copy_message(content);
			}
		},

		// 再次生成
		regenerateVideo() {
			// 参考home_three/message.vue中的toGenerateVideo逻辑
			this.$store.commit('setMessage_save', true);
			this.$store.commit('setAiMessage', {
				content: this.content_list || [this.contentRaw],
				title: this.page_title
			});

			uni.navigateTo({
				url: '/subpkg/index/get_radio/index'
			});
		},

		// 下载视频
		downloadVideo() {
			if (!this.videoUrl) {
				uni.showToast({
					title: '视频链接不存在',
					icon: 'none'
				});
				return;
			}

			uni.navigateTo({
				url: `/subpkg/index/get_radio/look_my_video?url=${encodeURIComponent(this.videoUrl)}`
			});
		}
	}
}
</script>

<style scoped lang="scss">
.box_list {
	font-size: 28rpx;
	margin-top: 60rpx;
	padding: 20rpx 40rpx;
	color: white;

	.list_content {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}
}

.box {
	padding: 60rpx 40rpx 0 40rpx;

	.card {
		display: flex;
		align-items: center;

		image {
			width: 80rpx;
			height: 80rpx;
		}

		.card_text {
			margin-left: 20rpx;

			.title {
				font-size: 28rpx;
				font-weight: 600;
				margin-bottom: 10rpx;
			}

			.time {
				font-size: 26rpx;
				opacity: 0.8;
			}
		}
	}

	.message_content {
		margin-top: 40rpx;
		font-size: 32rpx;
		padding-bottom: 150rpx;
	}

	.message {
		margin-top: 20rpx;
		font-size: 32rpx;
	}

	.footer {
		display: flex;
		justify-content: space-around;
		position: fixed;
		width: 100vw;
		bottom: 20rpx;
		left: 0;
		padding-top: 20rpx;
		border-top: 2rpx solid #dedede;

		.footer_box {
			padding: 10rpx 20rpx;
			border-radius: 20rpx;
			display: flex;
			align-items: center;

			text {
				font-size: 26rpx;
			}
		}
	}

	// 视频预览样式
	.video-preview {
		margin-top: 40rpx;
		width: 324rpx;
		height: 576rpx;
		position: relative;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
		background-color: #000;

		.play-icon {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			background: rgba(0, 0, 0, 0.3);
			border-radius: 50%;
			padding: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100rpx;
			height: 100rpx;

			image {
				width: 60rpx;
				height: 60rpx;
			}
		}

		.video-cover {
			width: 100%;
			height: 100%;
			object-fit: cover;
			opacity: 0.85;
		}

		.play-icon-container {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 100rpx;
			height: 100rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: rgba(0, 0, 0, 0.3);
			border-radius: 50%;

			.play-icon {
				width: 56rpx;
				height: 56rpx;
			}
		}
	}
}

// 底部操作按钮
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	// height: 100rpx;
	display: flex;
	align-items: center;
	justify-content: space-around;
	background-color: white;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	padding: 20rpx 30rpx;
	box-sizing: border-box;

	.action-btn {
		display: flex;
		flex-direction: column;
		align-items: center;

		image {
			width: 42rpx;
			height: 42rpx;
			margin-bottom: 8rpx;
		}

		text {
			font-size: 24rpx;
			color: #333;
		}
	}
}
</style>