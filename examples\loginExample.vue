<template>
	<view class="container">
		<button class="login-button" @click="showLoginDialog">显示登录弹窗</button>
		<button class="check-button" @click="checkUserLogin">检查登录状态</button>
		<button class="logout-button" v-if="isUserLoggedIn" @click="logoutUser">退出登录</button>
		
		<view class="user-info" v-if="isUserLoggedIn">
			<view class="info-title">当前用户信息:</view>
			<view class="info-item" v-if="userInfo">手机号: {{ formatPhone(userInfo.phone) }}</view>
		</view>
	</view>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
	computed: {
		...mapGetters('user', {
			isUserLoggedIn: 'isLogin',
			userInfo: 'userInfo'
		})
	},
	methods: {
		showLoginDialog() {
			// 调用全局登录弹窗
			this.$showLogin({
				success: (userData) => {
					console.log('登录成功', userData);
					uni.showToast({
						title: '登录成功',
						icon: 'success'
					});
				},
				cancel: () => {
					console.log('用户取消登录');
					uni.showToast({
						title: '取消登录',
						icon: 'none'
					});
				},
				fail: (err) => {
					console.error('登录失败', err);
					uni.showToast({
						title: '登录失败',
						icon: 'none'
					});
				}
			});
		},
		checkUserLogin() {
			// 检查登录状态
			const isLoggedIn = this.$checkLogin({
				success: (userData) => {
					uni.showToast({
						title: '已登录',
						icon: 'success'
					});
				},
				cancel: () => {
					uni.showToast({
						title: '取消登录',
						icon: 'none'
					});
				}
			});
			
			if (isLoggedIn) {
				console.log('用户已登录');
			} else {
				console.log('用户未登录，已显示登录弹窗');
			}
		},
		logoutUser() {
			this.$logout(() => {
				uni.showToast({
					title: '已退出登录',
					icon: 'success'
				});
			});
		},
		formatPhone(phone) {
			if (!phone) return '';
			return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	padding: 40rpx;
	
	button {
		width: 100%;
		height: 88rpx;
		line-height: 88rpx;
		margin-bottom: 20rpx;
		font-size: 32rpx;
		font-weight: 500;
		border-radius: 44rpx;
		box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
		transition: all 0.2s ease;
		
		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
			opacity: 0.9;
		}
	}
	
	.login-button {
		background: #000;
		color: #fff;
	}
	
	.check-button {
		background: #3FDCFF;
		color: #000;
	}
	
	.logout-button {
		background: #FF3F3F;
		color: #fff;
	}
	
	.user-info {
		margin-top: 40rpx;
		padding: 20rpx;
		border-radius: 16rpx;
		background: #f5f5f5;
		
		.info-title {
			font-size: 32rpx;
			font-weight: bold;
			margin-bottom: 16rpx;
		}
		
		.info-item {
			font-size: 28rpx;
			line-height: 1.6;
		}
	}
}
</style> 