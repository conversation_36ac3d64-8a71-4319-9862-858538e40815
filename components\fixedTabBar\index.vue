<template>
  <u-tabbar :fixed="true" :placeholder="true" :safeAreaInsetBottom="false" :active-color="activeColor"
    :inactive-color="inactiveColor"
    :custom-style="`${customStyle};padding-bottom:${safeAreaInsetBottom ? safeAreaInsetBottom + 'px' : '20rpx'}`"
    :border="false">
    <view class="tabbar-content">
      <view v-for="(item, idx) in actualTabItems" :key="idx" class="page_tabbar" :class="{ active: index === idx }"
        :style="{ color: index === idx ? activeColor : inactiveColor }" @click="down_click(idx)">
        <image :src="getImageSrc(item, idx)" />
        <view>{{ item.text }}</view>
        <!-- 小红点显示 -->
        <view v-if="item.badge && !item.badgeCount" class="badge"></view>
        <!-- 带数字的小红点 -->
        <view v-if="item.badge && item.badgeCount && item.badgeCount > 0" class="badge-count">
          {{ item.badgeCount > 99 ? '99+' : item.badgeCount }}
        </view>
      </view>
    </view>
  </u-tabbar>
</template>

<script>
import { mapMutations, mapState } from 'vuex'
export default {
  props: {
    index: {
      type: [String, Number],
      default: 0
    },
    activeColor: {
      type: String,
      default: 'white'
    },
    inactiveColor: {
      type: String,
      default: 'black'
    },
    customStyle: {
      type: String,
      default: 'background:rgb(29,36,41);'
    },
    imagePrefix: {
      type: String,
      default: 'project1_home'
    },
    tabItems: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      image_url_start: 'https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/app_tab/',
      defaultTabItems: [
        { text: '工具', path: '/pages/index/index', badge: false },
        { text: '动态', path: '/pages/home_two/index', badge: false },
        { text: '频道', path: '/pages/home_three/index', badge: false },
        { text: '我的', path: '/pages/home_four/index', badge: false }
      ],
      safeAreaInsetBottom: 0
    };
  },
  computed: {
    ...mapState('user', ['channelBadgeCount']),
    actualTabItems() {
      // 用外部传入的 tabItems 覆盖默认项，长度按默认值为准
      return this.defaultTabItems.map((defaultItem, idx) => {
        const override = this.tabItems[idx] || {};
        const item = { ...defaultItem, ...override };

        // 根据索引设置小红点显示
        if (idx === 1) {
          // 动态页面，显示频道小红点
          item.badge = this.channelBadgeCount.length > 0;
          item.badgeCount = this.channelBadgeCount.length;
        }

        return item;
      });
    }
  },
  created() {
    this.safeAreaInsetBottom = uni.getSystemInfoSync().safeAreaInsets.bottom;
  },
  methods: {
    ...mapMutations('user', ['CLEAR_CHANNEL_BADGE_COUNT', 'HIDE_VIDEO_GENERATED_NOTIFICATION']),

    getImageSrc(item, index) {
      // 优先使用外部传入的 icon / iconActive
      if (this.index === index && item.iconActive) return item.iconActive;
      if (this.index !== index && item.icon) return item.icon;

      // 否则使用默认拼接方式
      const mapIndex = index + 1;
      const state = this.index === index ? 'true' : 'false';
      return `${this.image_url_start}${this.imagePrefix}${mapIndex}_${state}.png`;
    },
    down_click(index) {
      uni.vibrateShort({
        type: 'light',
        success: function () {
          console.log('success');
        }
      });
      const item = this.actualTabItems[index];

      // 点击动态页面时清零频道小红点并隐藏视频生成通知
      if (index == 1) {
        this.CLEAR_CHANNEL_BADGE_COUNT()
        this.HIDE_VIDEO_GENERATED_NOTIFICATION()
      }

      if (item?.path) {
        uni.switchTab({ url: item.path });
      }
    }
  }
};
</script>

<style scoped lang="scss">
.tabbar-content {
  display: flex;
  width: 100%;
  justify-content: space-around;
  align-items: center;
  height: 100rpx;
}

.page_tabbar {
  position: relative;
  font-size: 24rpx;
  text-align: center;
  padding: 10rpx 40rpx;
  transition: all 0.3s;
  color: white;

  &.active {
    color: v-bind(activeColor);
  }

  image {
    width: 44rpx;
    height: 44rpx;
    margin-bottom: 4rpx;
  }
}

.badge {
  position: absolute;
  top: 5rpx;
  left: 5rpx;
  width: 12rpx;
  height: 12rpx;
  background-color: red;
  border-radius: 50%;
}

.badge-count {
  position: absolute;
  top: 10rpx;
  right: 30rpx;
  min-width: 30rpx;
  height: 30rpx;
  background-color: red;
  border-radius: 15rpx;
  color: white;
  font-size: 18rpx;
  line-height: 30rpx;
  text-align: center;
  padding: 0 6rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
