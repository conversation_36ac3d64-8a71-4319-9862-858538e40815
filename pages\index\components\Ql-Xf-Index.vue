<template>
  <view class="page"
    :style="{ backgroundImage: `url(${backgroundImage})`, backgroundRepeat: 'no-repeat', backgroundSize: 'contain' }">

    <!--    <logingModel></logingModel>-->

    <view :style="{ height: '656rpx', position: 'relative' }">

      <view class="bg_number_user">
        <view class="user_video" @click="get_radio">
          <view class="user_video_content">
            <view>创作</view>
            <view>数字人视频</view>
          </view>
          <image src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/app_tab/project1_messsage_add.png"></image>

        </view>
        <!-- 声音克隆 -->
        <view class="clone" @click="go_getSound">
          <view class="clone_content">
            <view>声音克隆</view>
            <view style="font-weight: 500;font-size: 20rpx;font-family: ChillRoundGothic_Medium;">克隆音色，精准还原
            </view>
          </view>
          <image src="/static/首页声音.png"></image>
        </view>
      </view>
    </view>


    <vipVersion :pageShow="page_show" @show_model="show_model" @down_close="page_show = false" source="home"
      :fastPowerRange="configInfo.fastPowerRange" :numberType="number_type"></vipVersion>


    <view style="display: flex;justify-content: space-between;">
      <view class="box_video" @click="go_batchVideo">
        <view class="title">批量视频</view>
        <view class="text">高效量产，一键智作</view>
        <view class="btn">
          立即创作
        </view>
        <image mode="widthFix" src="/static/index/plsp.png">

        </image>
      </view>

      <view>
        <view class="number_user_box1" @click="addNumberUser">
          <view class="number_user_box1_content">
            <view class="title">定制数字人</view>
            <view class="text">形象克隆,分身有数</view>
          </view>
          <image src="/static/home/<USER>"></image>
        </view>

        <view class="number_user_box1" @click="go_get_message">
          <view class="number_user_box1_content">
            <view class="title">视频文案提取</view>
            <view class="text">智能解析，文案速记</view>
          </view>
          <image src="/static/提取文字.png"></image>
        </view>
      </view>
    </view>


    <view class="page_tool">
      <image src="/static/tool.png"></image>
      <view class="text_1">工具</view>
      <view>助手</view>
    </view>

    <!-- 	<scroll-view scroll-y="true" class="scroll-y" @scrolltolower="get_again">
    </scroll-view> -->

    <view class="box_list">
      <view v-for="item in listData" class="list_content" :key="item.id" @click="go_copy(item)">
        <image :src="item.icon" style="width: 80rpx;height: 80rpx;"></image>
        <view style="width: 180rpx;">
          <view class="ellipsis">{{ item.name }}</view>
          <view class="ellipsis-sub">{{ item.description }}</view>
        </view>
      </view>
    </view>
    <TabBar :index="0" active-color="white" inactive-color="white" custom-style="background:rgb(29,36,41)" />
    <InsufficientPermissions v-if="page_model" @closeModel="close"></InsufficientPermissions>
    <globalLoginModal />
  </view>

</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import TabBar from "@/components/fixedTabBar/index.vue";

export default {
  components: { TabBar },
  props: {
    configInfo: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      pageReady: false,
      page_show: false,
      model_login: false,
      listData: [],
      page_model: false,
      number_type: '',
      appName: '',
      appId: ''
    }
  },
  computed: {
    ...mapGetters('user', ['isLogin', 'userInfo'])
  },
  watch: {
    '$appConfig.appInfo': {
      handler(newAppInfo) {
        if (newAppInfo && newAppInfo.appName) {
          this.appName = newAppInfo.appName;
        }
      },
      deep: true,  // 如果需要深度监听
      immediate: true  // 是否立即执行一次
    }
  },
  async mounted() {
    this.appId = uni.getAccountInfoSync().miniProgram.appId
    console.log(this.appId, 'this.appId');

    this.get_tool()
  },
  computed: {
    backgroundImage() {
      if (this.appId == 'wx561f5390d721dfdf') {
        return 'https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/common/index-xiaofu-bg.png'
      } else if (this.appId == 'wx7ccb8b4b4784214f') {
        return 'https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/common/index-qiliang-bg.jpg'
      }
    }
  },
  onShow() {
    this.number_type = ''
    this.page_show = false
  },
  methods: {
    // 检查登录状态，如未登录则弹出登录框
    checkLoginStatus(targetUrl, options = {}) {
      if (!targetUrl && !options.callback) return false;

      return this.$checkLogin({
        success: (userData) => {
          // 登录成功，根据参数处理后续操作
          if (options.callback && typeof options.callback === 'function') {
            // 如果提供了回调函数，直接调用
            options.callback(userData);
          } else if (targetUrl) {
            // 如果提供了目标URL，则跳转
            uni.navigateTo({
              url: targetUrl,
              success: options.success,
              fail: options.fail,
              complete: options.complete
            });
          }
        },
        cancel: () => {
          // 用户取消登录，不执行后续操作
          uni.showToast({
            title: options.cancelTip || '需要登录后才能访问',
            icon: 'none'
          });

          if (options.onCancel && typeof options.onCancel === 'function') {
            options.onCancel();
          }
        },
        fail: (err) => {
          // 登录失败
          if (options.onFail && typeof options.onFail === 'function') {
            options.onFail(err);
          }
        }
      });
    },

    go_get_message() {
      this.checkLoginStatus('/subpkg/index/extractionCopy', {
        cancelTip: '需要登录后才能使用文案提取'
      });
    },

    go_batchVideo() {
      uni.navigateTo({
        url: '/subpkg/batchVideoHome/index'
      })
    },

    get_again() {

    },

    close(value) {
      this.page_model = value
    },

    show_model(value) {
      this.page_show = false
      this.page_model = value
    },

    addNumberUser() {
      this.checkLoginStatus(null, {
        callback: () => {
          this.number_type = ''
          if (this.configInfo.enableFastClone == '0') {
            this.page_show = true
          } else {
            uni.navigateTo({
              url: '/subpkg/index/numbe_user/index?type=pro'
            })
          }
        },
        cancelTip: '需要登录后才能定制数字人'
      });
    },

    go_copy(item) {
      console.log('item', item);
      // 直接跳转到文案页面，不检查登录状态
      uni.navigateTo({
        url: '/subpkg/index/page_copy?source=home',
        success: (res) => {
          res.eventChannel.emit('get_message', item)
        }
      });
    },

    go_getSound() {
      this.checkLoginStatus(null, {
        callback: () => {
          this.number_type = 'sound'
          if (this.configInfo.enableFastClone == '0') {
            this.page_show = true
          } else {
            uni.navigateTo({
              url: '/subpkg/index/get_sound/index?type=pro'
            })
          }
        },
        cancelTip: '需要登录后才能使用声音克隆'
      });
    },

    get_tool() {
      this.$api.get_authorization({ ext: this.appId }).then(res => {
        this.listData = res.rows
      })
    },

    get_radio() {
      this.checkLoginStatus('/subpkg/index/get_radio/index', {
        cancelTip: '需要登录后才能创作数字人视频'
      });
    }
  }
}
</script>

<style scoped lang="scss">
.page {
  padding: 32rpx;
  color: white;
  font-size: 28rpx;
  background: black;
  box-sizing: border-box;
  min-height: 100vh;
  padding-bottom: 100rpx;
}


.box_video {
  width: 320rpx;
  height: 288rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #30353D 0%, #30353D 100%);
  border-image: linear-gradient(135deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)) 2 2;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  position: relative;
  background-image: url('/static/index/plxg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .title {
    color: #3FDCFF;
    font-size: 32rpx;
  }

  .text {
    margin-top: 10rpx;
    font-size: 24rpx;
    color: #FFFFFF;
  }

  .btn {
    display: inline-block;
    padding: 10rpx 20rpx;
    margin-top: 20rpx;
    margin-left: 10rpx;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 24rpx 24rpx 24rpx 24rpx;
    border-image: linear-gradient(136deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)) 1 1;
  }

  image {
    position: absolute;
    width: 144rpx;
    bottom: 0;
    right: 0;
  }
}


.number_user_box1 {
  width: 350rpx;
  height: 136rpx;
  padding: 20rpx;
  background: #404040;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  border-image: linear-gradient(135deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)) 2 2;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-image: url('/static/index/dzszr.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin-bottom: 12rpx;

  .title {
    color: #3FDCFF;
    font-size: 32rpx;
  }

  .text {
    margin-top: 10rpx;
    font-size: 24rpx;
    color: #FFFFFF;
  }

  image {
    width: 64rpx;
    height: 64rpx;
  }
}

.mini-box {
  padding: 10rpx 20rpx;
  background: #404040;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  border-image: linear-gradient(135deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)) 2 2;
  text-align: center;
  margin-top: 16rpx;
  background-image: url('/static/index/sykl.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.page_tool {
  font-size: 30rpx;
  font-weight: 800;
  margin: 40rpx 0;
  display: flex;
  align-items: center;
  font-family: ChillRoundGothic_Bold;

  image {
    width: 40rpx;
    height: 40rpx;
    margin-right: 10rpx;
  }

  .text_1 {
    font-weight: bold;
    color: #01F675;
    line-height: 56rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-right: 4rpx;
  }
}


.bg_number_user {
  position: absolute;
  left: 0rpx;
  bottom: 40rpx;
  width: 100%;
  height: 144rpx;

}

.bg_text {
  font-family: ChillRoundGothic_Bold;
  font-size: 40rpx;
  position: absolute;
  left: 0;
  bottom: 200rpx;
}

.bg_img {
  width: 300rpx;
  position: absolute;
  right: 0;
  bottom: 0;
}


.box_list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;

  .list_content {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 20rpx;
    width: 320rpx;
    height: 132rpx;
    background: #303132;
    border-radius: 24rpx 24rpx 24rpx 24rpx;
    margin-bottom: 20rpx;
  }
}

.box_list::after {
  content: '';
  width: 320rpx;
}


.box_1 {
  width: 90vw;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;

  .first-text {
    border-radius: 30rpx;
    width: 59vw;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;
    background: rgb(37, 234, 209);
    color: black;
    height: 140rpx;
  }

  .add {
    width: 30vw;
    color: white;
    background: black;
    display: inline-block;
    border-radius: 50%;
    width: 60rpx;
    height: 60rpx;
    text-align: center;
    font-size: 40rpx;
  }
}

.box_2 {
  width: 90vw;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user_video {
  // background: $project_1_bg;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 354rpx;
  border-radius: 20rpx;
  height: 144rpx;
  color: #333333;
  font-weight: 600;
  font-size: 32rpx;
  font-family: ChillRoundGothic_Medium;
  background-image: url('/static/首页底板.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .user_video_content {
    height: 80rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  image {
    width: 56rpx;
    height: 56rpx;
  }
}

.clone {
  // background: $project_1_bg;
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 354rpx;
  border-radius: 20rpx;
  height: 144rpx;
  padding: 32rpx;
  font-weight: bold;
  font-size: 32rpx;
  color: #FFFFFF;
  font-family: ChillRoundGothic_Medium;
  background-image: url('/static/首页地板2.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .clone_content {
    height: 80rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  image {
    width: 56rpx;
    height: 56rpx;
  }
}

.scroll-y {
  height: 400rpx;
}

.ellipsis {
  font-weight: 600;
  font-size: 28rpx;
  color: #FFFFFF;
  line-height: 40rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.ellipsis-sub {
  font-size: 20rpx;
  color: #999999;
}
</style>
