<template>
  <view class="page flex-col">
    <!-- 主要内容区 -->
    <view class="main-section flex-row">
      <view class="content-box flex-col">
        <!-- 功能图标区域 -->
        <view class="feature-icons flex-row">
          <view class="feature-icon flex-col" @click="addNumberUser">
            <image class="icon-wrapper flex-col"
              src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/common/lvye/icon-%E5%AE%9A%E5%88%B6%E6%95%B0%E5%AD%97%E4%BA%BA.png" />
            <text class="icon-text">定制数字人</text>
          </view>
          <view class="feature-icon flex-col" @click="go_getSound">
            <image class="icon-wrapper flex-col"
              src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/common/lvye/icon-%E5%A3%B0%E9%9F%B3%E5%85%8B%E9%9A%86.png" />
            <text class="icon-text">声音克隆</text>
          </view>
          <view class="feature-icon flex-col" @click="go_get_message">
            <image class="icon-wrapper flex-col"
              src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/common/lvye/icon-%E8%A7%86%E9%A2%91%E6%96%87%E6%A1%88%E6%8F%90%E5%8F%96.png" />
            <text class="icon-text">视频文案提取</text>
          </view>
        </view>

        <!-- 工具助手标题 -->
        <view class="tools-title flex-row">
          <image src="/static/icon-tool-assistant.png" class="tools-icon"></image>
          <!-- 下划线 -->
          <image src="/static/Vector.png" class="underline"></image>
          <text class="tools-text">工具助手</text>
        </view>

        <!-- 工具列表网格布局 -->
        <view class="tools-grid">
          <view v-for="(item, index) in listData" class="tool-item flex-col" :key="item.id" @click="go_copy(item)">
            <image class="tool-icon-wrapper flex-col" :src="item.icon" />
            <text class="tool-name">{{ item.name }}</text>
          </view>
        </view>
      </view>

      <!-- 底部导航栏，不修改，由TabBar组件提供 -->
      <TabBar
        :tabItems="[{ iconActive: 'https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/app_tab/lv-index-icon-checked.png' }]"
        :index="0" active-color="#41b7f0" inactive-color="black" custom-style="background:rgb(255,255,255)" />
    </view>

    <!-- 顶部区域 -->
    <view class="header-section flex-col">
      <image class="header-bg" src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/common/index-lv-bg.png" />

      <!-- 创作数字人视频卡片 -->
      <view class="create-video-card flex-col" @click="get_radio">
        <view class="card-content flex-row">
          <view class="card-text flex-col justify-between">
            <text class="card-title">创作数字人视频</text>
            <text class="card-subtitle">开启您的创作之旅吧</text>
          </view>
        </view>
        <view class="card-action flex-row justify-between">
          <view class="action-button flex-row">
            <image class="button-bg" src="/static/index/icon-bjb.png" />
            <view class="button-text flex-row justify-between">
              <text class="button-label">去创作</text>
              <image class="arrow-icon" referrerpolicy="no-referrer" src="/static/index/icon.png" />
            </view>
          </view>
          <image class="card-icon" referrerpolicy="no-referrer" src="/static/index/icon-czszrsp.png" />
        </view>
      </view>

      <!-- 批量视频卡片 -->
      <view class="batch-video-card flex-col" @click="go_batchVideo">
        <view class="card-content flex-row">
          <view class="card-text flex-col justify-between">
            <text class="card-title">批量视频</text>
            <text class="card-subtitle">开启您的创作之旅吧</text>
          </view>
        </view>
        <view class="card-action flex-row justify-between">
          <view class="action-button flex-row">
            <image class="button-bg" src="/static/index/icon-bjb.png" />
            <view class="button-text flex-row justify-between">
              <text class="button-label">去创作</text>
              <image class="arrow-icon" referrerpolicy="no-referrer" src="/static/index/icon.png" />
            </view>
          </view>
          <image class="card-icon" referrerpolicy="no-referrer" src="/static/index/icon-plsp.png" />
        </view>
      </view>
    </view>

    <!-- 保留原有的弹窗组件 -->
    <vipVersion :pageShow="page_show" @show_model="show_model" @down_close="page_show = false" source="home"
      :fastPowerRange="configInfo.fastPowerRange" :numberType="number_type"></vipVersion>
    <InsufficientPermissions v-if="page_model" @closeModel="close"></InsufficientPermissions>
    <globalLoginModal />
  </view>
</template>

<script>
import {
  page_img_statr
} from '../../../api/getData.js'
import { mapGetters, mapActions } from 'vuex'
import TabBar from "@/components/fixedTabBar/index.vue";

export default {
  components: {TabBar},
  props:{
    configInfo:{
      type:Object,
      default:{}
    }
  },
  data() {
    return {
      pageReady: false,
      fastPowerRange: false,
      page_show: false,
      model_login: false,
      listData: [],
      page_model: false,
      number_type: '',
      shareImg: '',
      shareText: '',
      appName: '',
      page_img_statr: '',
      appId: ''
    }
  },
  computed: {
    ...mapGetters('user', ['isLogin', 'userInfo'])
  },
  watch: {
    '$appConfig.appInfo': {
      handler(newAppInfo) {
        if (newAppInfo && newAppInfo.appName) {
          this.appName = newAppInfo.appName;
        }
      },
      deep: true,  // 如果需要深度监听
      immediate: true  // 是否立即执行一次
    }
  },
  async mounted() {
    this.page_img_statr = page_img_statr
    this.appId = uni.getAccountInfoSync().miniProgram.appId
    this.get_tool()
  },
  onShow() {
    this.number_type = ''
    this.page_show = false
  },
  methods: {
    // 检查登录状态，如未登录则弹出登录框
    checkLoginStatus(targetUrl, options = {}) {
      if (!targetUrl && !options.callback) return false;

      return this.$checkLogin({
        success: (userData) => {
          // 登录成功，根据参数处理后续操作
          if (options.callback && typeof options.callback === 'function') {
            // 如果提供了回调函数，直接调用
            options.callback(userData);
          } else if (targetUrl) {
            // 如果提供了目标URL，则跳转
            uni.navigateTo({
              url: targetUrl,
              success: options.success,
              fail: options.fail,
              complete: options.complete
            });
          }
        },
        cancel: () => {
          // 用户取消登录，不执行后续操作
          uni.showToast({
            title: options.cancelTip || '需要登录后才能访问',
            icon: 'none'
          });

          if (options.onCancel && typeof options.onCancel === 'function') {
            options.onCancel();
          }
        },
        fail: (err) => {
          // 登录失败
          if (options.onFail && typeof options.onFail === 'function') {
            options.onFail(err);
          }
        }
      });
    },

    go_get_message() {
      this.checkLoginStatus('/subpkg/index/extractionCopy', {
        cancelTip: '需要登录后才能使用文案提取'
      });
    },

    go_batchVideo() {
      uni.navigateTo({
        url: '/subpkg/batchVideoHome/index'
      })
    },

    get_again() {

    },

    close(value) {
      this.page_model = value
    },

    show_model(value) {
      this.page_show = false
      this.page_model = value
    },

    addNumberUser() {
      this.checkLoginStatus(null, {
        callback: () => {
          this.number_type=''
          if (this.configInfo.enableFastClone == '0') {
            this.page_show = true
          } else {
            uni.navigateTo({
              url: '/subpkg/index/numbe_user/index?type=pro'
            })
          }
        },
        cancelTip: '需要登录后才能定制数字人'
      });
    },

    go_copy(item) {
      console.log('item', item);
      // 直接跳转到文案页面，不检查登录状态
      uni.navigateTo({
        url: '/subpkg/index/page_copy?source=home',
        success: (res) => {
          res.eventChannel.emit('get_message', item)
        }
      });
    },

    go_getSound() {

      this.checkLoginStatus(null, {
        callback: () => {
          this.number_type = 'sound'
          if (this.configInfo.enableFastClone == '0') {


            this.page_show = true
          } else {
            uni.navigateTo({
              url: '/subpkg/index/get_sound/index?type=pro'
            })
          }
        },
        cancelTip: '需要登录后才能使用声音克隆'
      });
    },

    get_tool() {
      this.$api.get_authorization({ ext: this.appId }).then(res => {
        this.listData = res.rows
      })
    },

    get_radio() {
      this.checkLoginStatus('/subpkg/index/get_radio/index', {
        cancelTip: '需要登录后才能创作数字人视频'
      });
    }
  }
}
</script>

<style scoped lang="scss">
.page {
  position: relative;
  width: 750rpx;
  min-height: 100vh;
  overflow: hidden;
  background-color: rgba(243, 245, 248, 1);

  .flex-col {
    display: flex;
    flex-direction: column;
  }

  .flex-row {
    display: flex;
    flex-direction: row;
  }

  .justify-between {
    justify-content: space-between;
  }

  /* 主内容区域 */
  .main-section {
    width: 750rpx;
    height: 928rpx;
    margin-top: 796rpx;

    /* 内容盒子 */
    .content-box {
      width: 750rpx;
      height: 764rpx;

      /* 功能图标区域 */
      .feature-icons {
        width: 690rpx;
        margin: 82rpx 0 0 38rpx;
        justify-content: space-between;

        .feature-icon {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 210rpx;
          padding: 15rpx;
          gap: 8rpx;
          margin-right: 16rpx;
          background: white;
          border-radius: 25rpx;
          font-family: Chill Round Gothic-Bold;
          font-weight: 550;
          box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 2px 0px;

          .icon-wrapper {

            height: 60rpx;
            width: 60rpx;
          }

          .icon-text {
            font-size: 12px;
            color: #333333;
          }
        }
      }

      /* 工具助手标题 */
      .tools-title {
        // width: 144rpx;
        height: 56rpx;
        margin: 40rpx 0 0 32rpx;
        display: flex;
        align-items: center;
        position: relative;

        .tools-icon {
          width: 40rpx;
          height: 40rpx;
          margin-right: 8rpx;
        }

        .underline {
          position: absolute;
          width: 64rpx;
          height: 12rpx;
          background-size: cover;
          left: 60rpx;
          bottom: -5rpx;
        }

        .tools-text {
          background: linear-gradient(44.92253464055979deg, #4A78F6 0%, #0ADAEC 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
          width: 144rpx;
          height: 56rpx;
          overflow-wrap: break-word;
          font-size: 36rpx;
          font-family: Chill Round Gothic-Bold;
          font-weight: 700;
          text-align: left;
          white-space: nowrap;
          line-height: 56rpx;
        }
      }

      /* 工具列表网格布局 */
      .tools-grid {
        width: 688rpx;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        grid-template-rows: repeat(2, 144rpx);
        gap: 16rpx;
        margin: 24rpx 0 148rpx 32rpx;

        .tool-item {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 12px;
          width: 160rpx;
          height: 144rpx;
          align-items: center;
          display: flex;
          flex-direction: column;
          box-shadow: rgba(27, 31, 35, 0.04) 0px 1px 0px, rgba(255, 255, 255, 0.25) 0px 1px 0px inset;

          .tool-icon-wrapper {
            background-color: rgba(243, 245, 248, 1);
            border-radius: 10px;
            height: 56rpx;
            width: 56rpx;
            margin-top: 24rpx;
            margin-bottom: 8rpx;
          }

          .tool-name {
            width: 124rpx;
            height: 32rpx;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 24rpx;
            font-family: MiSans-Demibold;
            font-weight: normal;
            text-align: center;
            white-space: nowrap;
            line-height: 32rpx;
            margin-bottom: 24rpx;
            overflow: hidden;
            /* 隐藏超出部分 */
            text-overflow: ellipsis;
            /* 显示省略号 */
          }
        }
      }
    }
  }

  /* 头部区域 */
  .header-section {
    position: absolute;
    left: 0;
    top: 0;
    width: 750rpx;
    height: 696rpx;

    .header-bg {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      z-index: 0;
    }

    /* Logo区域 */
    .logo-wrapper {
      height: 88rpx;
      width: 750rpx;
      margin: 28rpx 0 520rpx 0;

      .logo-image {
        width: 174rpx;
        height: 64rpx;
        margin: 12rpx 0 0 536rpx;
      }
    }

    /* 创作数字人视频卡片 */
    .create-video-card {
      height: 208rpx;
      border-radius: 12px 12px 12px 12px;
      background: white;
      width: 336rpx;
      position: absolute;
      left: 32rpx;
      top: 636rpx;
      box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;

      .card-content {
        width: 196rpx;
        height: 72rpx;
        margin: 32rpx 0 0 24rpx;

        .card-text {
          width: 196rpx;
          height: 72rpx;

          .card-title {
            width: 196rpx;
            height: 40rpx;
            overflow-wrap: break-word;
            color: #333333;
            font-size: 28rpx;
            font-family: Chill Round Gothic-Bold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 40rpx;
          }

          .card-subtitle {
            width: 180rpx;
            height: 28rpx;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 0.7);
            font-size: 20rpx;
            font-family: MiSans-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 28rpx;
            margin-top: 4rpx;
          }
        }
      }

      .card-action {
        width: 288rpx;
        height: 72rpx;
        margin: 8rpx 0 24rpx 24rpx;

        .action-button {
          width: 128rpx;
          height: 48rpx;
          position: relative;
          margin-top: 16rpx;

          .button-bg {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
          }

          .button-text {
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;

            .button-label {
              width: 60rpx;
              height: 28rpx;
              overflow-wrap: break-word;
              color: #FFFFFF;
              font-size: 20rpx;
              font-family: MiSans-Demibold;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 28rpx;
            }

            .arrow-icon {
              width: 16rpx;
              height: 16rpx;
              // margin-top: 6rpx;
            }
          }
        }

        .card-icon {
          width: 72rpx;
          height: 72rpx;
        }
      }
    }

    /* 批量视频卡片 */
    .batch-video-card {
      height: 208rpx;
      border-radius: 12px 12px 12px 12px;
      box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
      background: white;
      width: 334rpx;
      position: absolute;
      left: 384rpx;
      top: 636rpx;

      .card-content {
        width: 196rpx;
        height: 72rpx;
        margin: 32rpx 0 0 24rpx;

        .card-text {
          width: 196rpx;
          height: 72rpx;

          .card-title {
            width: 196rpx;
            height: 40rpx;
            overflow-wrap: break-word;
            color: #333333;
            font-size: 28rpx;
            font-family: Chill Round Gothic-Bold;
            font-weight: 700;
            text-align: left;
            white-space: nowrap;
            line-height: 40rpx;
          }

          .card-subtitle {
            width: 180rpx;
            height: 28rpx;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 0.7);
            font-size: 20rpx;
            font-family: MiSans-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 28rpx;
            margin-top: 4rpx;
          }
        }
      }

      .card-action {
        width: 288rpx;
        height: 72rpx;
        margin: 8rpx 0 24rpx 24rpx;

        .action-button {
          width: 128rpx;
          height: 48rpx;
          position: relative;
          margin-top: 16rpx;

          .button-bg {
            position: absolute;
            width: 100%;
            height: 100%;

            left: 0;
            top: 0;
          }

          .button-text {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 1;

            .button-label {
              width: 60rpx;
              height: 28rpx;
              overflow-wrap: break-word;
              color: #FFFFFF;
              font-size: 20rpx;
              font-family: MiSans-Demibold;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 28rpx;
            }

            .arrow-icon {
              width: 16rpx;
              height: 16rpx;
              // margin-top: 6rpx;
            }
          }
        }

        .card-icon {
          width: 72rpx;
          height: 72rpx;
        }
      }
    }
  }
}
</style>
