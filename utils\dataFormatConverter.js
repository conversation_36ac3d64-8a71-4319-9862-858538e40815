/**
 * 数据格式转换工具
 * 用于在不同数据格式之间进行转换
 */

/**
 * 将服务器返回的数据转换为移动端组件可用的格式
 * @param {Object} serverData 服务器数据（fitment.js导出的格式）
 * @returns {Object} 移动端组件格式
 */
export function convertServerDataToMobile(serverData) {
  if (!serverData || typeof serverData !== 'object') {
    console.error('服务器数据格式错误')
    return getDefaultMobileData()
  }

  try {
    // 服务器数据已经是正确的格式，直接返回
    // 但需要进行一些验证和补全
    const mobileData = {
      version: serverData.version || '1.0.0',
      timestamp: serverData.timestamp || Date.now(),
      
      // 背景配置
      background: {
        type: serverData.background?.type || '色彩填充',
        color: serverData.background?.color || '#FFFFFF',
        image: serverData.background?.image || '',
        defaultImage: serverData.background?.defaultImage || ''
      },

      // 布局数据
      layout: (serverData.layout || []).map(item => ({
        id: item.id || item.i, // 兼容旧格式
        position: {
          x: item.position?.x ?? (item.x || 0),
          y: item.position?.y ?? (item.y || 0),
          width: item.position?.width ?? (item.w || 1),
          height: item.position?.height ?? (item.h || 1)
        },
        type: item.type || item.componentType,
        config: item.config || item.componentConfig || {},
        static: item.static || false
      })),

      // 导航配置
      navigation: {
        enabled: serverData.navigation?.enabled ?? true,
        style: {
          backgroundColor: serverData.navigation?.style?.backgroundColor || '#FFFFFF',
          textColor: serverData.navigation?.style?.textColor || '#666666',
          activeTextColor: serverData.navigation?.style?.activeTextColor || '#21BD74',
          fontSize: serverData.navigation?.style?.fontSize || 12
        },
        items: (serverData.navigation?.items || []).map((item, index) => ({
          id: item.id ?? index,
          type: item.type || '未命名',
          icon: item.icon || '',
          activeIcon: item.activeIcon || item.icon || '',
          enabled: item.enabled ?? true
        }))
      },

      // 页面配置
      page: {
        title: serverData.page?.title || '首页',
        enablePullRefresh: serverData.page?.enablePullRefresh || false,
        enableReachBottom: serverData.page?.enableReachBottom || false,
        backgroundColor: serverData.page?.backgroundColor || serverData.background?.color || '#FFFFFF'
      },

      // 统计信息
      statistics: {
        totalComponents: serverData.statistics?.totalComponents || (serverData.layout || []).length,
        componentTypes: serverData.statistics?.componentTypes || {},
        lastModified: serverData.statistics?.lastModified || Date.now(),
        createdBy: serverData.statistics?.createdBy || 'fitment-editor'
      }
    }

    return mobileData
  } catch (error) {
    console.error('数据转换失败:', error)
    return getDefaultMobileData()
  }
}

/**
 * 将旧版本的数据格式转换为新格式
 * @param {Object} oldData 旧版本数据
 * @returns {Object} 新格式数据
 */
export function convertLegacyDataToNew(oldData) {
  if (!oldData || typeof oldData !== 'object') {
    return getDefaultMobileData()
  }

  try {
    return {
      version: '1.0.0',
      timestamp: Date.now(),
      
      background: {
        type: oldData.backgroundType || '色彩填充',
        color: oldData.backgroundColor || '#FFFFFF',
        image: oldData.backgroundImage || '',
        defaultImage: oldData.defaultBackgroundImage || ''
      },

      layout: (oldData.layout || []).filter(item => item.i !== 'drop').map(item => ({
        id: item.i,
        position: {
          x: item.x || 0,
          y: item.y || 0,
          width: item.w || 1,
          height: item.h || 1
        },
        type: item.componentType,
        config: item.componentConfig || {},
        static: item.static || false
      })),

      navigation: {
        enabled: true,
        style: {
          backgroundColor: oldData.navigationConfig?.color || '#FFFFFF',
          textColor: oldData.navigationConfig?.textColor || '#666666',
          activeTextColor: oldData.navigationConfig?.activeTextColor || '#21BD74',
          fontSize: oldData.navigationConfig?.fontSize || 12
        },
        items: (oldData.navigationConfig?.navigationList || []).map((item, index) => ({
          id: index,
          type: item.type || '未命名',
          icon: item.icon || '',
          activeIcon: item.activeIcon || item.icon || '',
          enabled: true
        }))
      },

      page: {
        title: '首页',
        enablePullRefresh: false,
        enableReachBottom: false,
        backgroundColor: oldData.backgroundColor || '#FFFFFF'
      },

      statistics: {
        totalComponents: (oldData.layout || []).filter(item => item.i !== 'drop').length,
        componentTypes: getComponentTypeStats(oldData.layout || []),
        lastModified: Date.now(),
        createdBy: 'fitment-editor'
      }
    }
  } catch (error) {
    console.error('旧数据转换失败:', error)
    return getDefaultMobileData()
  }
}

/**
 * 验证数据格式是否正确
 * @param {Object} data 要验证的数据
 * @returns {Object} 验证结果
 */
export function validateMobileData(data) {
  const result = {
    isValid: true,
    errors: [],
    warnings: []
  }

  if (!data || typeof data !== 'object') {
    result.isValid = false
    result.errors.push('数据不是有效的对象')
    return result
  }

  // 检查必要字段
  if (!data.background) {
    result.errors.push('缺少背景配置')
    result.isValid = false
  }

  if (!Array.isArray(data.layout)) {
    result.errors.push('布局数据必须是数组')
    result.isValid = false
  }

  if (!data.navigation) {
    result.errors.push('缺少导航配置')
    result.isValid = false
  }

  // 检查布局项
  if (data.layout) {
    data.layout.forEach((item, index) => {
      if (!item.id) {
        result.warnings.push(`布局项 ${index} 缺少ID`)
      }
      if (!item.type) {
        result.errors.push(`布局项 ${index} 缺少类型`)
        result.isValid = false
      }
      if (!item.position || typeof item.position.x !== 'number') {
        result.errors.push(`布局项 ${index} 位置信息错误`)
        result.isValid = false
      }
    })
  }

  // 检查导航项
  if (data.navigation && data.navigation.items) {
    if (data.navigation.items.length === 0) {
      result.warnings.push('导航栏没有配置任何项目')
    }
  }

  return result
}

/**
 * 获取默认移动端数据
 * @returns {Object} 默认数据
 */
export function getDefaultMobileData() {
  return {
    version: '1.0.0',
    timestamp: Date.now(),
    background: {
      type: '色彩填充',
      color: '#FFFFFF',
      image: '',
      defaultImage: ''
    },
    layout: [],
    navigation: {
      enabled: true,
      style: {
        backgroundColor: '#FFFFFF',
        textColor: '#666666',
        activeTextColor: '#21BD74',
        fontSize: 12
      },
      items: [
        { id: 0, type: '首页', icon: '', activeIcon: '', enabled: true },
        { id: 1, type: '分类', icon: '', activeIcon: '', enabled: true },
        { id: 2, type: '购物车', icon: '', activeIcon: '', enabled: true },
        { id: 3, type: '我的', icon: '', activeIcon: '', enabled: true }
      ]
    },
    page: {
      title: '首页',
      enablePullRefresh: false,
      enableReachBottom: false,
      backgroundColor: '#FFFFFF'
    },
    statistics: {
      totalComponents: 0,
      componentTypes: {},
      lastModified: Date.now(),
      createdBy: 'fitment-editor'
    }
  }
}

/**
 * 获取组件类型统计
 * @param {Array} layout 布局数组
 * @returns {Object} 统计结果
 */
function getComponentTypeStats(layout) {
  const stats = {}
  layout.forEach(item => {
    if (item.i !== 'drop') {
      const type = item.componentType || item.type
      if (type) {
        stats[type] = (stats[type] || 0) + 1
      }
    }
  })
  return stats
}

/**
 * 深拷贝对象
 * @param {any} obj 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item))
  }
  
  if (typeof obj === 'object') {
    const cloned = {}
    Object.keys(obj).forEach(key => {
      cloned[key] = deepClone(obj[key])
    })
    return cloned
  }
  
  return obj
}

/**
 * 合并数据对象
 * @param {Object} target 目标对象
 * @param {Object} source 源对象
 * @returns {Object} 合并后的对象
 */
export function mergeData(target, source) {
  const result = deepClone(target)
  
  if (!source || typeof source !== 'object') {
    return result
  }
  
  Object.keys(source).forEach(key => {
    if (source[key] !== null && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = mergeData(result[key] || {}, source[key])
    } else {
      result[key] = source[key]
    }
  })
  
  return result
}
