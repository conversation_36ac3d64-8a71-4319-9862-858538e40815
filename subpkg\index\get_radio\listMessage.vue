<template>
	<view class="page">
		<view class="content">
			<view style="height: 100rpx;">
				<u-tabs lineColor="#21BD74" :list="list1" @click="change_list"></u-tabs>
			</view>

			<scroll-view scroll-y="true" class="scroll_box" @scrolltolower="get_again">
				<view v-if="pageContent && !pageContent.length" class="empty-container">
					<u-empty mode="data"></u-empty>
				</view>

				<view v-for="item in pageContent" @click="change(item)" :class="item.id == page_id ? 'box_active' : 'box'">
					<view class="box_title">
						{{ item.title }}
					</view>
					<view class="message">
						{{ item.content }}
						<view class="look_btn" v-if="item.content.length > 60" @click.stop="go_look_message(item)">
							...查看全文
						</view>
					</view>
				</view>
			</scroll-view>

		</view>

		<view class="project_btn" @click="save_content">应用</view>
	</view>
</template>

<script>
import {
	getGroupList,
	getGroupContent
} from '../../../api/numberUser/copy.js'

import {
	formatRelativeTime
} from '../../../utils/file.js'

export default {
	data() {
		return {
			current: 0,
			list1: [],
			listId: '',
			categoryId: -1,
			pageContent: '',
			page: 1,
			data_length: 0,

			page_textarea: '',
			page_title: '',
			page_id: ''
		}
	},
	onLoad(option) {
		this.listId = option.id
		this.get_message()
	},
	methods: {
		save_content() {
			// 检查是否已选择文案
			if (!this.page_id) {
				uni.showToast({
					title: '请先选择文案',
					icon: 'none'
				});
				return;
			}

			console.log("当前title" + this.page_title)
			this.getOpenerEventChannel().emit('get_content', {
				title: this.page_title,
				content: this.page_textarea
			})
			uni.navigateBack()
		},
		change(item) {
			this.pageContent.map(res => {
				if (res.id == item.id) this.page_id = item.id
			})
			this.page_textarea = item.content
			this.page_title = item.title
		},
		//获取分类
		get_message() {
			getGroupList({
				groupId: this.listId,
				type: this.current
			}).then(res => {
				this.list1 = [{
					name: '全部',
					id: null
				}, ...res.data]
				this.get_content()
			})
		},
		//获取分类内容
		get_content() {
			getGroupContent({
				type: this.current,
				groupId: this.listId,
				categoryId: this.categoryId ?? -1,
				pageNum: this.page,
				pageSize: 10
			}).then(res => {
				if (this.current === 0) { // 文案库
					res.rows.map(item => {
						item.createTime = formatRelativeTime(item.createTime)
						item.expanded = false // 添加展开状态属性
					})
				}

				if (this.page == 1) {
					this.pageContent = res.rows
					this.$forceUpdate()
				} else {
					this.pageContent = [...this.pageContent, ...res.rows]
				}
				this.data_length = res.total
			})
		},

		//触发上拉刷新
		get_again() {
			this.page++
			if (this.pageContent.length < this.data_length) this.get_content()
			else {
				console.log('失败')
			}
		},
		//选择分类
		change_list(e) {
			this.page = 1
			this.categoryId = e.id
			this.get_content()
		},
		//查看全文
		go_look_message(item) {
			let that = this
			uni.navigateTo({
				url: '/subpkg/home_three/messageContent',
				success(res) {
					res.eventChannel.emit('get_messageContent', {
						item
					})
				}
			})
		},
		go_back() {
			uni.navigateBack()
		},
		confirmSelection() {
			// 获取当前选中的文案内容
			const selectedContent = this.selectedContent; // 替换为实际的变量

			// 使用页面间通信将内容发送回上一页
			const eventChannel = this.getOpenerEventChannel();
			eventChannel.emit('get_content', selectedContent);

			// 返回上一页
			uni.navigateBack();
		}
	}
}
</script>

<style scoped lang="scss">
.page {
	background: $project_1_bg_color;

	.content {
		.scroll_box {
			height: calc(100vh - 100rpx);
			padding: 0 40rpx;
			padding-bottom: 140rpx;
		}

		.box_active {
			padding: 30rpx;
			margin-top: 20rpx;
			border-radius: 20rpx;
			background: rgba(234, 246, 244, 0.5);
			border: 2rpx solid $project_1_text_color;
		}

		.box {
			padding: 30rpx;
			margin-top: 20rpx;
			border-radius: 20rpx;
			background: white;
			border: 2rpx solid white;
		}

		.box_title {
			margin-top: 20rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 1;
			line-clamp: 1;
			font-weight: 600;
			font-size: 28rpx;
		}

		.message {
			margin-top: 20rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 3;
			line-clamp: 3;
			position: relative;
			font-size: 28rpx;
			opacity: 0.8;
			line-height: 1.6;
			color: #666666;
		}

		.look_btn {
			position: absolute;
			bottom: 0;
			right: 0;
			font-size: 26rpx;
			color: $project_1_text_color;
			background: white;
		}
	}
}

.project_btn {
	position: fixed;
	bottom: 40rpx;
	left: 5%;
	background: black;
}

.empty-container {
	padding: 60rpx 0;
	display: flex;
	justify-content: center;
	align-items: center;
}
</style>
