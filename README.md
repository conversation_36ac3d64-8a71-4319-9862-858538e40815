## 项目名称
> 请介绍一下你的项目吧  



## 运行条件
> 列出运行该项目所必须的条件和相关依赖  
* 条件一
* 条件二
* 条件三



## 运行说明
> 说明如何运行和使用你的项目，建议给出具体的步骤说明
* 操作一
* 操作二
* 操作三  



## 测试说明
> 如果有测试相关内容需要说明，请填写在这里  



## 技术架构
> 使用的技术框架或系统架构图等相关说明，请填写在这里  


## 协作者
> 高效的协作会激发无尽的创造力，将他们的名字记录在这里吧

## 项目说明

本项目是一个基于uni-app的移动端应用，主要功能包括数字人视频生成、装修主题系统等。

### 已完成功能
- 用户登录注册系统
- 数字人视频生成
- 音频处理功能
- 视频裁剪功能
- 文件上传功能
- 装修主题系统（使用Vuex状态管理）

### 最近更新 (2025-01-21)
- **重构装修主题系统**：将所有装修数据和方法移动到Vuex中进行统一管理
- **优化TabBar组件**：TabBar组件现在直接从Vuex获取数据，不再需要通过props传参
- **简化页面逻辑**：fitment-theme页面现在只负责展示，所有业务逻辑都在Vuex中处理
- **统一样式计算**：所有样式计算方法都集中在fitmentDataLoader.js中，避免代码重复
- **新增导航栏字体大小配置**：支持通过`navigation.style.fontSize`控制导航栏文字大小

### 最近更新 (2025-01-22)
- **声音克隆页面音频播放优化**：将音频播放逻辑从`innerAudioContext`改为使用`video`标签
  - 添加隐藏的video标签用于音频播放，提高兼容性
  - 移除`innerAudioContext`相关代码，简化音频控制逻辑
  - 更新播放控制方法：`playVoice()`、`stop_video()`、`again_video()`
  - 添加video事件监听器：`handleVideoEnded`、`handleVideoPlay`、`handleVideoPause`、`handleVideoStop`
  - 优化页面生命周期管理，确保音频正确停止

### 装修主题系统架构
- **Vuex模块** (`store/modules/fitment.js`)：管理装修数据、状态和业务逻辑
- **工具函数** (`utils/fitmentDataLoader.js`)：提供数据加载和样式计算方法
- **页面组件** (`pages/fitment-theme/index.vue`)：纯展示组件，从Vuex获取数据
- **TabBar组件** (`components/TabBar.vue`)：从Vuex获取配置和状态，支持动态样式

## 工具功能

### 模板预览图生成工具

在`@/utils/templatePreview.js`文件中，我们实现了一个模板预览图生成工具，用于将模板画布转换为预览图片。主要功能包括：

- 根据画布数据(`canvasData`)提取各元素信息
- 按顺序绘制背景、数字人、标题、字幕、身份栏等元素
- 绘制完成后生成临时图片文件
- 支持通过回调函数处理临时图片文件

使用示例：

```javascript
import { drawTemplatePreview } from '@/utils/templatePreview.js';

// 在组件中调用
drawTemplatePreview({
  canvasData: this.canvasData,                       // 画布数据
  uploadCallback: (path) => this.save_data(path),    // 上传成功回调函数
  canvasScale: this.canvasScale,                    // 画布缩放比例
  backgroundElements: this.backgroundElements,       // 背景元素数据
  identityName: this.identityName,                  // 人物名称
  identityDesc: this.identityDesc,                  // 人物介绍
  formatTextFn: this.formatText                     // 格式化文本的函数
});
```

这个工具大大简化了模板编辑器中生成预览图的逻辑，提高了代码的可维护性和复用性。

### 装修主题系统

装修主题系统采用Vuex进行状态管理，实现了数据与视图的分离。主要特点：

#### 1. Vuex状态管理 (`store/modules/fitment.js`)

```javascript
// 状态管理
state: {
  fitmentData: {},      // 装修数据
  loading: true,        // 加载状态
  error: null,          // 错误信息
  currentTabIndex: 0,   // 当前Tab索引
  themeId: null         // 主题ID
}

// 计算属性
getters: {
  layoutItems,          // 布局项目
  backgroundStyle,      // 背景样式
  tabList,             // TabBar列表
  showTabBar,          // 是否显示TabBar
  pageTitle,           // 页面标题
  containerMinHeight   // 容器最小高度
}

// 异步操作
actions: {
  initPageData,        // 初始化页面数据
  refreshData,         // 刷新数据
  setThemeId,          // 设置主题ID
  handleTabChange,     // 处理Tab切换
  handleFunctionClick  // 处理功能组件点击
}
```

#### 2. 样式计算工具 (`utils/fitmentDataLoader.js`)

提供统一的样式计算方法：
- `convertGridToRpx()` - 栅格坐标转换
- `getComponentStyle()` - 组件基础样式
- `getImageComponentStyle()` - 图片组件样式
- `getTextComponentStyle()` - 文字组件样式
- `getFunctionComponentStyle()` - 功能组件样式
- `getFunctionIconStyle()` - 功能组件图标样式

#### 3. 页面组件重构

**fitment-theme页面** 现在只负责：
- 从Vuex获取数据进行展示
- 处理页面生命周期
- 简单的UI交互

**TabBar组件** 现在：
- 直接从Vuex获取tabList和样式配置
- 自动响应状态变化
- 不再需要父组件传递props

#### 4. 使用示例

```javascript
// 在页面中使用
export default {
  computed: {
    ...mapState('fitment', ['loading', 'error']),
    ...mapGetters('fitment', ['layoutItems', 'showTabBar', 'pageTitle'])
  },

  methods: {
    ...mapActions('fitment', ['initPageData', 'setThemeId'])
  },

  async onLoad(options) {
    if (options.themeId) {
      await this.setThemeId(options.themeId)
    } else {
      await this.initPageData()
    }
  }
}
```

#### 5. 导航栏配置

导航栏现在支持完整的样式配置，包括新增的字体大小控制：

```javascript
// 导航栏配置示例
navigation: {
  enabled: true,
  style: {
    backgroundColor: '#FFFFFF',    // 背景颜色
    textColor: '#666666',         // 文字颜色
    activeTextColor: '#21BD74',   // 选中文字颜色
    fontSize: 12                  // 文字大小 (新增)
  },
  items: [
    {
      id: 0,
      type: '首页',
      icon: 'icon-url',
      activeIcon: 'active-icon-url',
      enabled: true
    }
    // ... 更多导航项
  ]
}
```

TabBar组件会自动从Vuex获取这些配置并应用样式：
- 文字大小会自动转换为rpx单位 (`fontSize * 2 + 'rpx'`)
- 支持动态更新，修改Vuex中的配置会立即生效
- 兼容旧版本数据，自动提供默认值

这种架构的优势：
- **数据统一管理**：所有装修数据都在Vuex中，便于维护和调试
- **组件解耦**：页面组件只负责展示，业务逻辑在Vuex中处理
- **状态持久化**：重要状态自动保存到本地存储
- **代码复用**：样式计算方法可以在多个组件中复用
- **配置灵活**：支持完整的导航栏样式自定义，包括字体大小控制
