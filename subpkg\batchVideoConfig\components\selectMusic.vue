<template>
  <view class="music-container">
    <!-- 音乐选择标题 -->
    <view class="section-title">请选择音乐</view>

    <!-- 音乐列表为空时的状态 -->
    <view v-if="bgMuseicList.length === 0" class="empty-state">
      <view class="empty-content">
        <view class="empty-icon-wrapper">
        </view>
        <text class="empty-text">您暂时还没有音乐</text>
      </view>
      <view class="add-music-btn" @click="addNewMusic">
        <text class="add-music-text">添加音乐</text>
      </view>
    </view>

    <!-- 音乐列表 -->
    <view v-else class="music-list">
      <view class="music-item" @click="addNewMusic">
        <view class="music-view">
          <!-- 添加音乐图标 -->
          <view class="add-icon">
          </view>
          <view class="music-name">添加新的音乐</view>
        </view>
      </view>
      <view class="music-item" v-for="(item, index) in bgMuseicList" :key="index" :class="{ 'selected': true }">
        <view class="music-view">
          <!-- 播放/暂停按钮 -->
          <image v-if="currentPlaying === index" @click.stop="stopMusic()"
            :src="'https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/project1_home4_music_playing.gif'"
            class="play-icon"></image>
          <image v-else @click.stop="playMusic(index)" src="/static/home/<USER>/get_radio_bf.png" class="play-icon">
          </image>
          <view class="music-name">{{ item.name }}</view>
        </view>
        <!-- 添加删除按钮 -->
        <view class="delete-icon" @click.stop="deleteMusic(index)">
          <image src="/static/删除.png" class="delete-img"></image>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar" :style="{ paddingBottom: safeAreaBottom + 'px' }">
      <view class="selected-count">
        <view class="count-text">已选：{{ bgMuseicList.length }}</view>
      </view>
      <view class="bottom-bar-btn">
        <view class="prev-btn" @click="prevStep">
          <view class="btn-text">上一步</view>
        </view>
        <view class="next-btn" @click="nextStep" :class="{ 'disabled': bgMuseicList.length === 0 }">
          <view class="btn-text">下一步</view>
        </view>
      </view>
    </view>

    <!-- 音乐为空确认弹窗 -->
    <u-popup :show="musicEmptyConfirmVisible" @close="musicEmptyConfirmVisible = false" :safeAreaInsetBottom="false" mode="center"
      round="20" :closeable="false">
      <view class="power-estimate-popup">
        <view class="popup-title">提示</view>
        <view class="popup-content">
          <text>当前没有选择任何音乐，是否进入下一步？</text>
        </view>
        <view class="popup-buttons">
          <view class="cancel-btn" @click="musicEmptyConfirmVisible = false">
            <text class="btn-text">取消</text>
          </view>
          <view class="confirm-btn" @click="confirmNextStepWithoutMusic">
            <text class="btn-text">好的</text>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  props: {
    bgMuseicList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentPlaying: null,
      audioContext: null,
      safeAreaBottom: 0,
      musicEmptyConfirmVisible: false
    };
  },
  created() {
    // 创建音频上下文
    this.audioContext = uni.createInnerAudioContext({
      useWebAudioImplement: true
    });
    this.safeAreaBottom = uni.getSystemInfoSync().safeAreaInsets.bottom + uni.upx2px(24);
    // 音频播放结束事件处理
    this.audioContext.onEnded(() => {
      this.currentPlaying = null;
    });

    // 监听单个音乐选择事件
    uni.$on('selectMusic', this.handleSelectedMusic);

    // 监听多个音乐选择事件
    uni.$on('selectMusics', this.handleSelectedMusics);
  },
  beforeDestroy() {
    // 销毁音频上下文
    if (this.audioContext) {
      this.audioContext.destroy();
    }

    // 移除事件监听
    uni.$off('selectMusic', this.handleSelectedMusic);
    uni.$off('selectMusics', this.handleSelectedMusics);
  },
  methods: {
    // 添加新的音乐
    addNewMusic() {
      uni.navigateTo({
        url: '/subpkg/index/get_radio/choose_music',
        success: (res) => {
          // 传递参数，表明是从selectMusic页面跳转过来的
          res.eventChannel.emit('fromSelectMusic', {
            fromSelectMusic: true
          });
      }
      });
    },

    // 接收选择的单个音乐
    handleSelectedMusic(musicData) {
      if (musicData && musicData.id) {
        // 检查是否已经存在相同ID的音乐
        const existingIndex = this.bgMuseicList.findIndex(item => item.id === musicData.id);
        const newMusicList = [...this.bgMuseicList];

        if (existingIndex > -1) {
          // 如果已经存在，更新数据
          newMusicList[existingIndex] = {
            id: musicData.id,
            name: musicData.name,
            url: musicData.url
          };
        } else {
          // 如果不存在，添加到列表中（默认已选中）
          newMusicList.push({
            id: musicData.id,
            name: musicData.name,
            url: musicData.url
          });
        }

        // 通知父组件更新音乐列表
        this.$emit('saveBgMuseicList', newMusicList);
          }
    },

    // 接收选择的多个音乐
    handleSelectedMusics(musicDataArray) {
      if (Array.isArray(musicDataArray) && musicDataArray.length > 0) {
        const newMusicList = [...this.bgMuseicList];

        // 遍历所有选中的音乐数据
        musicDataArray.forEach(musicData => {
          if (musicData && musicData.id) {
            // 检查是否已经存在相同ID的音乐
            const existingIndex = newMusicList.findIndex(item => item.id === musicData.id);

            if (existingIndex > -1) {
              // 如果已经存在，更新数据
              newMusicList[existingIndex] = {
                id: musicData.id,
                name: musicData.name,
                url: musicData.url
              };
            } else {
              // 如果不存在，添加到列表中（默认已选中）
              newMusicList.push({
                id: musicData.id,
                name: musicData.name,
                url: musicData.url
              });
            }
          }
        });

        // 通知父组件更新音乐列表
        this.$emit('saveBgMuseicList', newMusicList);

        // 显示成功提示
        uni.showToast({
          title: `已添加${musicDataArray.length}个音乐`,
          icon: 'success'
        });
      }
    },

    // 删除音乐
    deleteMusic(index) {
      // 如果正在播放该音乐，先停止播放
      if (this.currentPlaying === index) {
        this.audioContext.stop();
        this.currentPlaying = null;
      }

      // 从列表中移除
      const newMusicList = [...this.bgMuseicList];
      newMusicList.splice(index, 1);

      // 通知父组件更新音乐列表
      this.$emit('saveBgMuseicList', newMusicList);

      uni.showToast({
        title: '已删除',
        icon: 'none'
      });
    },

    // 播放音乐
    playMusic(index) {
      const item = this.bgMuseicList[index];

      // 如果正在播放其他音乐，先停止
      if (this.currentPlaying !== null) {
        this.audioContext.stop();
      }

      // 播放选中的音乐
      if (item.url) {
        this.audioContext.src = item.url;
        this.audioContext.play();
        this.currentPlaying = index;
      }
    },

    // 停止播放音乐
    stopMusic() {
      if (this.currentPlaying !== null) {
        this.audioContext.stop();
        this.currentPlaying = null;
      }
    },

    // 上一步
    prevStep() {
      // 停止当前播放的音频
      this.stopMusic();
      uni.$emit('batch-video-prev-step');
    },

    // 下一步
    nextStep() {
      // 检查是否有音乐
      if (this.bgMuseicList.length === 0) {
        this.musicEmptyConfirmVisible = true;
        return;
      }

      // 停止当前播放的音频
      this.stopMusic();

      // 切换到下一步
      uni.$emit('batch-video-next-step');
    },

    // 确认下一步（音乐为空）
    confirmNextStepWithoutMusic() {
      // 停止当前播放的音频
      this.stopMusic();

      this.musicEmptyConfirmVisible = false;
      // 切换到下一步
      uni.$emit('batch-video-next-step');
    }
  }
};
</script>

<style lang="scss">
.music-container {
  background-color: #f3f5f8;
  width: 100%;
  height: 100%;
  padding-bottom: 140rpx; // 为底部操作栏预留空间

  // 音乐选择标题
  .section-title {
    color: #333333;
    font-size: 32rpx;
    font-weight: bold;
    line-height: 48rpx;
    margin: 40rpx 0 24rpx 32rpx;
  }

  // 空状态样式
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-top: 208rpx;

    .empty-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 214rpx;
      height: 224rpx;

      .empty-icon-wrapper {
        position: relative;
        width: 214rpx;
        height: 168rpx;
        background-image: url('/static/音乐空状态.png');
        background-size: cover;
        background-repeat: no-repeat;

        .empty-icon-bg {
          position: absolute;
          left: 12rpx;
          top: 74rpx;
          width: 18rpx;
          height: 50rpx;
        }

        .empty-icon-container {
          position: absolute;
          left: 44rpx;
          top: 82rpx;
          width: 132rpx;
          height: 50rpx;
          background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGfbf2788cced21c7a634e61538bd013b5.png) 100% no-repeat;
          background-size: 100% 100%;

          .empty-icon-note1 {
            position: absolute;
            left: 104rpx;
            top: -22rpx;
            width: 12rpx;
            height: 34rpx;
          }

          .empty-icon-note2 {
            position: absolute;
            left: 100rpx;
            top: 24rpx;
            width: 34rpx;
            height: 32rpx;
          }
        }

        .empty-icon-music {
          position: absolute;
          left: 50rpx;
          top: 26rpx;
          width: 116rpx;
          height: 114rpx;
        }
      }

      .empty-text {
        width: 192rpx;
        height: 32rpx;
        color: #999999;
        font-size: 24rpx;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 32rpx;
        margin-top: 24rpx;
      }
    }

    .add-music-btn {
      background: linear-gradient(135deg, #22232c 19.837089%, #0f0f0f 100%);
      border-radius: 16rpx;
      width: 280rpx;
      height: 96rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 32rpx;


      .add-music-text {
        color: #ffffff;
        font-size: 32rpx;
        font-weight: bold;
        line-height: 48rpx;
      }
    }
  }

  // 音乐列表
  .music-list {
    padding: 0 32rpx;

    .music-item {
      background-color: #f3f5f8;
      border-radius: 24rpx;
      width: 100%;
      height: 128rpx;
      margin-bottom: 16rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      position: relative;
      // padding: 0 30rpx;
      box-sizing: border-box;

      &.selected::after {
        content: '';
        position: absolute;
        right: 32rpx;
        width: 32rpx;
        height: 32rpx;
        // background-image: url('/static/goxuanzhong.png');
        background-size: contain;
        background-repeat: no-repeat;
      }

      .music-view {
        display: flex;
        align-items: center;
        flex: 1;

        .play-icon {
          width: 80rpx;
          height: 80rpx;
          margin-right: 20rpx;
        }

        // 添加音乐图标样式
        .add-icon {
        width: 80rpx;
        height: 80rpx;
          margin-right: 20rpx;
        border-radius: 16rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background-image: url('/static/新增声音.png');
          background-size: cover;
          background-repeat: no-repeat;

        }

        .music-name {
          color: #333333;
          font-size: 28rpx;
          font-weight: bold;
          line-height: 40rpx;
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 450rpx;
        }
      }

      // 删除图标样式
      .delete-icon {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .delete-img {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }

  // 底部操作栏
  .bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    // height: 128rpx;
    background-color: #ffffff;
    border-radius: 16rpx 16rpx 0 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 48rpx;
    box-sizing: border-box;

    .selected-count {
      background-color: #efefef;
      border-radius: 12rpx;
      height: 64rpx;
      padding: 0 32rpx;
      display: flex;
      align-items: center;

      .count-text {
        color: #666666;
        font-size: 24rpx;
      }
    }

    .bottom-bar-btn {
      display: flex;

      .prev-btn,
      .next-btn {
        height: 80rpx;
        width: 180rpx;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 24rpx;

        .btn-text {
          font-size: 28rpx;
          font-weight: bold;
        }
      }

      .prev-btn {
        background-color: #efefef;

        .btn-text {
          color: #666666;
        }
      }

      .next-btn {
        background: linear-gradient(135deg, #22232c 0%, #0f0f0f 100%);

        .btn-text {
          color: #ffffff;
        }

        &.disabled {
          background: #efefef;

          .btn-text {
            color: #a8a8a8;
          }
        }
      }
    }
  }
}

// 音乐为空确认弹窗样式
.power-estimate-popup {
  width: 600rpx;
  padding: 60rpx 48rpx 48rpx;
  background-color: #ffffff;
  border-radius: 20rpx;

  .popup-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
    text-align: center;
    margin-bottom: 48rpx;
  }

  .popup-content {
    font-size: 28rpx;
    color: #333333;
    line-height: 44rpx;
    text-align: center;
    margin-bottom: 64rpx;
  }

  .popup-buttons {
    display: flex;
    justify-content: space-between;
    gap: 32rpx;

    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 88rpx;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .btn-text {
        font-size: 32rpx;
        font-weight: 600;
      }
    }

    .cancel-btn {
      background-color: #f5f5f5;
      border: 1rpx solid #e6e6e6;

      .btn-text {
        color: #666666;
      }
    }

    .confirm-btn {
      background-color: #333333;

      .btn-text {
        color: #ffffff;
      }
    }
  }
}
</style>
