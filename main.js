import Vue from 'vue'
import App from './App'
import devTools from '@/devTools/index.js'
import devToolsConfig from '@/devTools/config.js'
// import mpDevBubble from "@/devTools/core/components/mpDevBubble.vue"
import devToolsVueMixin from "@/devTools/core/proxy/vueMixin.js"
import './App.scss'

import store from 'store/index.js'
import globalLoginModal from '@/components/globalLoginModal/globalLoginModal.vue'
import customNavbar from '@/components/custom-navbar/custom-navbar.vue'
import pageTitle from '@/components/pageTitle/pageTitle.vue'
let pathUrl = 'https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/app_font/'
uni.loadFontFace({
  global: true,
  family: 'MiSans-Demibold',
  source: `${pathUrl}MiSans-Demibold.woff2`,
  success: () => {
    console.log('字体加载成功')
  },
  fail: () => {
    console.log('字体加载失败')
  }
})

uni.loadFontFace({
  global: true,
  family: 'MiSans-Medium',
  source: `${pathUrl}MiSans-Medium.woff2`,
  success: () => {
    console.log('字体加载成功')
  },
})

uni.loadFontFace({
  global: true,
  family: 'ChillRoundGothic_Bold',
  source: `${pathUrl}ChillRoundGothic_Bold.woff2`,
  success: () => {
    console.log('字体加载成功')
  },
})

uni.loadFontFace({
  global: true,
  family: 'ChillRoundGothic_Medium',
  source: `${pathUrl}ChillRoundGothic_Medium.woff2`,
  success: () => {
    console.log('字体加载成功')
  },
})

Vue.component('globalLoginModal', globalLoginModal)
Vue.component('customNavbar', customNavbar)
Vue.component('pageTitle', pageTitle)
Vue.prototype.$store = store

import uView from '@/uni_modules/uview-ui'
Vue.use(uView)
Vue.mixin(devToolsVueMixin)
Vue.use(devTools, devToolsConfig)
// Vue.component('mpDevBubble', mpDevBubble)


import './uni.promisify.adaptor'

import api from "@/api/getData.js"
Vue.prototype.$api = api

Vue.config.productionTip = false

// 注册全局登录弹窗方法
Vue.prototype.$showLogin = function (options = {}) {
  store.dispatch('user/showLoginModal', {
    success: options.success || null,
    cancel: options.cancel || null,
    fail: options.fail || null
  });
}

// 注册全局检查登录状态方法
Vue.prototype.$checkLogin = function (options = {}) {
  if (store.getters['user/isLogin']) {
    if (options.success) {
      options.success(store.getters['user/userInfo']);
    }
    return true;
  } else {
    if (options.autoShowLogin !== false) {
      this.$showLogin(options);
    }
    return false;
  }
}

// 注册全局退出登录方法
Vue.prototype.$logout = function (callback) {
  store.dispatch('user/logout').then(() => {
    if (callback) callback();
  });
}

Vue.prototype.$appConfig = Vue.observable({
  appInfo: {}
});
// 异步加载配置，不阻塞 app 启动
import { getAppConfig } from "@/config";
getAppConfig().then(config => {
  Vue.prototype.$appConfig.appInfo = config.appInfo;
}).catch(err => {
  console.error('配置初始化失败:', err)
})

App.mpType = 'app'
const app = new Vue({
  store,
  ...App
})
app.$mount()

export default app
