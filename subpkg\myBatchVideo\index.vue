<template>
  <view class="my-works-page">
    <custom-navbar title="我的作品" :isBack="true" :isBlack="true"></custom-navbar>
    <view class="my-works-content" :style="{ paddingBottom: safeAreaBottom + 'px' }">

      <!-- 批量视频生成介绍 -->
      <view class="batch-video-intro">
        <image class="intro-icon" src="/static/批量生成视频头像.png"></image>
        <view class="intro-content">
          <text class="intro-title">批量视频生成</text>
          <text class="intro-desc">助力矩阵玩法，批量生产多个数字人视频</text>
        </view>
      </view>

      <!-- 创建批量视频按钮 -->
      <view class="create-button" @click="createBatchVideo">
        <image class="create-icon" src="/static/加号.png"></image>
        <text class="create-text">创建批量视频</text>
      </view>

      <!-- 作品列表 -->
      <view class="works-list">
        <!-- 骨架屏 -->
        <template v-if="loading && worksList.length === 0">
          <view class="work-item-skeleton" v-for="i in 3" :key="'skeleton-' + i">
            <!-- 视频预览骨架屏 -->
            <view class="video-preview-skeleton">
              <view class="video-placeholder-skeleton">
                <view class="skeleton-animation"></view>
              </view>
              <view class="video-thumbnail-skeleton">
                <view class="skeleton-animation"></view>
                <view class="collection-badge-skeleton">
                  <view class="skeleton-animation"></view>
                </view>
              </view>
            </view>

            <!-- 作品信息骨架屏 -->
            <view class="work-info-skeleton">
              <view class="work-title-skeleton">
                <view class="skeleton-animation"></view>
              </view>
              <view class="work-stats-skeleton">
                <view class="skeleton-animation"></view>
              </view>
              <view class="generation-status-skeleton">
                <view class="skeleton-animation"></view>
              </view>
              <view class="progress-info-skeleton">
                <view class="skeleton-animation"></view>
              </view>
            </view>

            <!-- 更多操作骨架屏 -->
            <view class="more-action-skeleton">
              <view class="skeleton-animation"></view>
            </view>
          </view>
        </template>

        <!-- 加载状态 -->
        <view class="loading-container" v-else-if="loading && worksList.length > 0">
          <u-loading-icon :show="true" mode="circle" color="#21BD74"></u-loading-icon>
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 空状态 -->
        <view class="empty-container" v-else-if="!worksList.length">
          <u-empty mode="data" text="暂无批量视频作品"></u-empty>
        </view>

        <!-- 作品列表 -->
        <view class="work-item" v-else v-for="(item, index) in worksList" :key="index" @click="viewWork(item)">
          <!-- 视频预览区域 -->
          <view class="video-preview">
            <view class="video-thumbnail" :style="{ backgroundImage: `url(${item.thumbnail})` }">
            </view>
            <view class="video-placeholder">合集</view>
            <view class="loading" v-if="item.status !== 2 && item.status!==20">
              <image src="/static/loading.gif"></image>
            </view>
          </view>

          <!-- 作品信息 -->
          <view class="work-info">
            <text class="work-title">{{ item.title }}</text>
            <view class="work-stats">
              <text class="stats-label">共计</text>
              <text class="stats-number">{{ item.totalCount }}</text>
              <text class="stats-unit">个视频</text>
            </view>
            <!-- 生成状态 -->
            <view class="generation-status">
              <text class="status-text" :class="{
                'status-pending': item.status === 0,
                'status-processing': item.status === 1,
                'status-completed': item.status === 2,
                 'status-failed': item.status === 20
              }">{{ item.statusText }}</text>
            </view>
            <!-- 进度条（处理中状态显示） -->
            <view class="progress-info" v-if="item.status === 1">
              <text class="progress-text">{{ item.currentProgress }}/{{ item.totalCount }}</text>
            </view>
          </view>

          <!-- 更多操作 -->
          <image class="more-action" src="/static/批量生成视频更多.png" @click.stop="showActions(item)"></image>
        </view>
      </view>
    </view>

    <!-- 操作弹窗 -->
    <u-popup :show="actionSheetVisible" @close="actionSheetVisible = false" mode="bottom" round="20" :closeable="false">
      <view class="action-sheet-popup">
        <!-- 标题区域 -->
        <view class="popup-header">
          <text class="popup-title">请选择</text>
          <image class="close-icon" src="/static/删除.png" @click="actionSheetVisible = false"></image>
        </view>

        <!-- 操作选项 -->
        <view class="action-option" @click="handleAction('regenerate')">
          <text class="option-text">再次生成</text>
        </view>

        <!-- <view class="action-option" @click="handleAction('download')">
          <text class="option-text">批量下载视频</text>
        </view> -->

        <view class="action-option delete-option" @click="handleAction('delete')">
          <text class="option-text delete-text">删除</text>
        </view>

        <!-- 底部安全区域 -->
        <view class="popup-safe-area" :style="{ height: safeAreaBottom + 'px' }"></view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { postMyBatchVideoList, postBatchVideoDelete } from '@/api/batchVideoApi'
export default {
  data() {
    return {
      safeAreaBottom: 0,
      loading: false,
      worksList: [],
      // 操作弹窗相关
      actionSheetVisible: false,
      currentItem: null
    };
  },
  onLoad() {
    this.safeAreaBottom = uni.getSystemInfoSync().safeAreaInsets.bottom;
    this.loadWorksList();
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 创建批量视频
    createBatchVideo() {
      uni.navigateTo({
        url: '/subpkg/batchVideoConfig/index'
      });
    },

    // 查看作品详情
    viewWork(item) {
      if (item.status === 2) {
        // 这里可以跳转到作品详情页
        uni.navigateTo({
          url: `/subpkg/batchVideoDetail/index?workId=${item.id}`
        });
      }
    },

    // 显示更多操作
    showActions(item) {
      this.currentItem = item;
      this.actionSheetVisible = true;
    },

    // 处理操作选择
    handleAction(action) {
      this.actionSheetVisible = false;

      if (!this.currentItem) return;

      switch (action) {
        case 'regenerate':
          this.regenerateWork(this.currentItem);
          break;
        case 'download':
          this.downloadWork(this.currentItem);
          break;
        case 'delete':
          this.deleteWork(this.currentItem);
          break;
      }

      this.currentItem = null;
    },

    // 重新生成
    regenerateWork(item) {
      console.log(JSON.parse(item.reqData), 'item');

      uni.setStorageSync('batchVideoDraft', JSON.stringify(JSON.parse(item.reqData).beforeGenerateData));
      uni.navigateTo({
        url: '/subpkg/batchVideoConfig/index?from=batchVideoBegin'
      });

      // 这里可以重新生成作品
      // uni.showToast({
      //   title: '重新生成功能开发中',
      //   icon: 'none'
      // });
    },

    // 删除作品
    deleteWork(item) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除"${item.title}"吗？删除后将无法恢复。`,
        success: async (res) => {
          if (res.confirm) {
            try {
              // 显示删除加载状态
              uni.showLoading({
                title: '删除中...',
                mask: true
              });

              // 调用删除API
              const deleteRes = await postBatchVideoDelete(item.id);

              if (deleteRes.code === 200) {
                // 删除成功，从列表中移除
                const index = this.worksList.findIndex(work => work.id === item.id);
                if (index > -1) {
                  this.worksList.splice(index, 1);
                }

                uni.showToast({
                  title: '删除成功',
                  icon: 'success',
                  duration: 2000
                });
              } else {
                // 删除失败
                uni.showToast({
                  title: deleteRes.message || '删除失败',
                  icon: 'none',
                  duration: 2000
                });
              }
            } catch (error) {
              console.error('删除作品失败:', error);
              uni.showToast({
                title: '删除失败，请重试',
                icon: 'none',
                duration: 2000
              });
            } finally {
              // 隐藏加载状态
              uni.hideLoading();
            }
          }
        }
      });
    },

    // 下载作品
    downloadWork(item) {
      uni.showToast({
        title: '批量下载功能开发中',
        icon: 'none'
      });
    },

    // 加载作品列表
    async loadWorksList() {
      try {
        this.loading = true;
        const res = await postMyBatchVideoList({
          pageNum: 1,
          pageSize: 20
        });

        if (res.code === 200 && res.rows) {
          // 处理返回的数据
          this.worksList = res.rows.map(item => ({
            id: item.id,
            batchId: item.batchId,
            title: this.formatTitle(item.createTime),
            videoCount: item.videoCount,
            thumbnail: item.preUrl || 'https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/common/batch-video-bg.png',
            status: item.status, // 0-未开始, 1-处理中, 2-已结束
            statusText: this.getStatusText(item.status),
            currentProgress: item.currentProgress || 0,
            totalCount: item.totalCount || 0,
            createTime: item.createTime,
            updateTime: item.updateTime,
            remark: item.remark,
            videoIds: item.videoIds,
            reqData: item.reqData
          }));
          console.log(this.worksList, 'this.worksList');

        }
      } catch (error) {
        console.error('获取作品列表失败:', error);
        uni.showToast({
          title: '获取作品列表失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 格式化标题
    formatTitle(createTime) {
      if (!createTime) return '批量视频';

      const date = new Date(createTime);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      return `${year}-${month}-${day} 批量视频`;
    },

    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case 0:
          return '未开始';
        case 1:
          return '处理中';
        case 2:
          return '已结束';
        case 20:
          return '全部失败';
        default:
          return '未知状态';
      }
    },
  }
};
</script>

<style lang="scss">
.my-works-page {
  background-color: #f3f5f8;
  min-height: 100vh;

  .my-works-content {
    padding: 0 32rpx;

    // 批量视频介绍
    .batch-video-intro {
      display: flex;
      align-items: center;
      margin: 32rpx 0 24rpx;

      .intro-icon {
        width: 96rpx;
        height: 96rpx;
        margin-right: 16rpx;
      }

      .intro-content {
        flex: 1;

        .intro-title {
          display: block;
          color: #333333;
          font-size: 32rpx;
          font-weight: bold;
          line-height: 48rpx;
          margin-bottom: 8rpx;
        }

        .intro-desc {
          color: #999999;
          font-size: 24rpx;
          font-weight: 500;
          line-height: 32rpx;
        }
      }
    }

    // 创建按钮
    .create-button {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f3f5f8;
      border: 1rpx solid #d8d8d8;
      border-radius: 8rpx;
      height: 80rpx;
      margin-bottom: 40rpx;

      .create-icon {
        width: 40rpx;
        height: 40rpx;
        margin-right: 8rpx;
      }

      .create-text {
        color: #333333;
        font-size: 28rpx;
        font-weight: bold;
        line-height: 40rpx;
      }
    }

    // 作品列表
    .works-list {

      // 骨架屏动画
      @keyframes skeleton-loading {
        0% {
          background-position: -200% 0;
        }

        100% {
          background-position: 200% 0;
        }
      }

      .skeleton-animation {
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
        background-size: 400% 100%;
        animation: skeleton-loading 1.4s ease infinite;
        border-radius: 4rpx;
      }

      // 骨架屏作品项
      .work-item-skeleton {
        position: relative;
        background-color: #ffffff;
        border-radius: 12rpx;
        padding: 32rpx 48rpx;
        margin-bottom: 32rpx;
        display: flex;

        // 视频预览骨架屏
        .video-preview-skeleton {
          position: relative;
          margin-right: 32rpx;

          .video-placeholder-skeleton {
            background-color: #efefef;
            border-radius: 4rpx;
            width: 112rpx;
            height: 184rpx;
          }

          .video-thumbnail-skeleton {
            position: absolute;
            top: -8rpx;
            left: -8rpx;
            width: 144rpx;
            height: 184rpx;
            border-radius: 16rpx;

            .collection-badge-skeleton {
              position: absolute;
              bottom: 0;
              right: 0;
              border-radius: 4rpx 2rpx 2rpx 8rpx;
              height: 32rpx;
              width: 64rpx;
            }
          }
        }

        // 作品信息骨架屏
        .work-info-skeleton {
          flex: 1;

          .work-title-skeleton {
            width: 200rpx;
            height: 40rpx;
            margin-bottom: 8rpx;
          }

          .work-stats-skeleton {
            width: 120rpx;
            height: 32rpx;
            margin-bottom: 8rpx;
          }

          .generation-status-skeleton {
            width: 80rpx;
            height: 28rpx;
            margin-bottom: 8rpx;
          }

          .progress-info-skeleton {
            width: 80rpx;
            height: 28rpx;
          }
        }

        // 更多操作骨架屏
        .more-action-skeleton {
          width: 40rpx;
          height: 40rpx;
          margin: auto 0;
          border-radius: 50%;
        }
      }

      // 加载状态
      .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 80rpx 0;

        .loading-text {
          color: #999999;
          font-size: 28rpx;
          margin-top: 24rpx;
        }
      }

      // 空状态
      .empty-container {
        padding: 80rpx 0;
      }

      .work-item {
        position: relative;
        background-color: #ffffff;
        border-radius: 12rpx;
        padding: 32rpx 48rpx;
        margin-bottom: 32rpx;
        display: flex;
        // align-items: center;

        // 视频预览区域
        .video-preview {
          position: relative;
          margin-right: 32rpx;
          border-radius: 16rpx;
          width: 152rpx;
          height: 192rpx;
          background-position: center;
          background-size: cover;
          background-image: url('/static/堆叠.png');

          .video-placeholder {
            position: absolute;
            width: 64rpx;
            height: 32rpx;
            background: linear-gradient(135deg, #22232C 20%, #0F0F0F 100%);
            border-radius: 8rpx 4rpx 16rpx 4rpx;
            right: 10rpx;
            bottom: 8rpx;
            font-weight: 600;
            font-size: 16rpx;
            color: #FFFFFF;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .video-thumbnail {
            position: absolute;
            top: 0rpx;
            left: 0rpx;
            width: 144rpx;
            height: 184rpx;
            border-radius: 16rpx;
            background-size: cover;
            background-position: center;
          }

          .loading {
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 999;
            // 苹果毛玻璃效果
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10rpx);
            -webkit-backdrop-filter: blur(10rpx);
            border-radius: 16rpx;
            border: 1rpx solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

            // 兼容性处理：如果不支持backdrop-filter，使用渐变背景模拟
            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg,
                  rgba(255, 255, 255, 0.3) 0%,
                  rgba(255, 255, 255, 0.1) 50%,
                  rgba(255, 255, 255, 0.2) 100%);
              border-radius: 16rpx;
              z-index: -1;
            }

            image {
              z-index: 1;
              position: relative;
            }
          }
        }

        // 作品信息
        .work-info {
          flex: 1;
          height: 100%;

          .work-title {
            color: #333333;
            font-size: 28rpx;
            font-weight: bold;
            line-height: 40rpx;
            margin-bottom: 8rpx;
          }

          .work-stats {
            .stats-label {
              color: #999999;
              font-size: 24rpx;
              font-weight: 500;
              line-height: 32rpx;
            }

            .stats-number {
              color: #21bd74;
              font-size: 24rpx;
              font-weight: 500;
              line-height: 32rpx;
            }

            .stats-unit {
              color: #999999;
              font-size: 24rpx;
              font-weight: 500;
              line-height: 32rpx;
            }
          }

          .generation-status {
            margin-top: 8rpx;

            .status-text {
              font-size: 20rpx;
              font-weight: 500;
              line-height: 28rpx;
              padding: 4rpx 8rpx;
              border-radius: 4rpx;
              display: inline-block;

              &.status-pending {
                color: #666666;
                background-color: #f3f5f8;
              }

              &.status-processing {
                color: #ff9500;
                background-color: #fff7ec;
              }

              &.status-completed {
                color: #21bd74;
                background-color: #f0f9f4;
              }
              &.status-failed{
                color: #ff5e5e;
                background-color: #efefef;
              }
            }
          }

          .progress-info {
            margin-top: 8rpx;

            .progress-text {
              color: #ff9500;
              font-size: 20rpx;
              font-weight: 500;
              line-height: 28rpx;
            }
          }
        }

        // 更多操作
        .more-action {
          width: 40rpx;
          height: 40rpx;
          margin: auto 0;
        }
      }
    }
  }

  // 操作弹窗样式
  .action-sheet-popup {
    width: 750rpx;
    padding-top: 40rpx;

    .popup-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 40rpx;
      margin-bottom: 32rpx;

      .popup-title {
        color: #333333;
        font-size: 32rpx;
        font-weight: bold;
        line-height: 48rpx;
      }

      .close-icon {
        width: 48rpx;
        height: 48rpx;
      }
    }

    .action-option {
      background-color: #ffffff;
      border-radius: 8rpx;
      height: 112rpx;
      margin: 0 40rpx 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1rpx solid #f5f5f5;

      &:active {
        background-color: #f8f8f8;
      }

      .option-text {
        color: #333333;
        font-size: 32rpx;
        font-weight: bold;
        line-height: 48rpx;
      }

      &.delete-option {
        .delete-text {
          color: #fd4f4f;
          font-weight: 700;
        }
      }
    }

    .popup-safe-area {
      width: 100%;
      margin-top: 16rpx;
    }
  }
}
</style>