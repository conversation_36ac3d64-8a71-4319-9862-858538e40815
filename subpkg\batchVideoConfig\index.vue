<template>
  <view class="batch-video-config">
    <!-- 顶部导航栏 -->
    <custom-navbar title="批量视频" background="linear-gradient(6deg, rgba(255,255,255,0) 18%, rgba(222,249,255,1) 100%)"
      :isBlack="true" @beforeJump="beforeJump"></custom-navbar>

    <!-- 步骤条 -->
    <steps :current="current" :stepList="stepList" :spacing="46" @handleStepClick="handleStepClick"></steps>
    <view class="batch-video-config-content">
      <!-- 选择模板 -->
      <select-template v-show="current === 0" :selectedTemplates="selectedData.templates" @saveTemplates="saveTemplates"
        :statusBarHeight="statusBarHeight"></select-template>
      <!-- 选择文案 -->
      <select-writer v-show="current === 1" :topicList="selectedData.topicList" @saveTopicList="saveTopicList"
        :statusBarHeight="statusBarHeight"></select-writer>
      <!-- 选择素材 -->
      <select-material v-show="current === 2" :assetInfo="selectedData.assetInfo" @saveAssetInfo="saveAssetInfo"
        :statusBarHeight="statusBarHeight"></select-material>
      <!-- 选择音乐 -->
      <select-music v-show="current === 3" :bgMuseicList="selectedData.bgMuseicList"
        @saveBgMuseicList="saveBgMuseicList" :statusBarHeight="statusBarHeight"></select-music>
      <!-- 选择生成 -->
      <select-generate v-show="current === 4" :rule="selectedData.rule" :templates="selectedData.templates"
        :topicList="selectedData.topicList" :assetInfo="selectedData.assetInfo"
        :bgMuseicList="selectedData.bgMuseicList" @saveRule="saveRule" :statusBarHeight="statusBarHeight"
        @startGenerate="startGenerate"></select-generate>
    </view>

    <!-- 算力预估弹窗 -->
    <u-popup :show="powerEstimateVisible" @close="powerEstimateVisible = false" :safeAreaInsetBottom="false"
      mode="center" round="20" :closeable="false">
      <view class="power-estimate-popup">
        <view class="popup-title">提示</view>
        <view class="popup-content">
          <text>本次共生成{{ estimatedVideoCount }}个视频，预计消耗{{ estimatedPower }}个算力</text>
        </view>
        <view class="popup-buttons">
          <view class="cancel-btn" @click="cancelGenerate">
            <text class="btn-text">取消</text>
          </view>
          <view class="confirm-btn" @click="confirmGenerate">
            <text class="btn-text">开始生成</text>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 是否使用草稿箱弹窗 -->
    <u-popup :show="draftBoxVisible" @close="draftBoxVisible = false" :safeAreaInsetBottom="false" mode="center"
      round="20" :closeable="false">
      <view class="power-estimate-popup">
        <view class="popup-title">温馨提示</view>
        <view class="popup-content">
          <text>您有草稿内容历史编辑文件，是否继续编辑？</text>
        </view>
        <view class="popup-buttons">
          <view class="cancel-btn" @click="startNewFile">
            <text class="btn-text">开启新文件</text>
          </view>
          <view class="confirm-btn" @click="useDraft">
            <text class="btn-text">使用草稿</text>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>
<script>
import steps from '@/components/steps/index.vue'
import selectTemplate from './components/selectTemplate.vue'
import selectWriter from './components/selectWriter.vue'
import selectMaterial from './components/selectMaterial.vue'
import selectMusic from './components/selectMusic.vue'
import selectGenerate from './components/selectGenerate.vue'
import { postBatchVideo, postBatchVideoPower } from '@/api/batchVideoApi'
export default {
  data() {
    return {
      current: 0, // 当前步骤
      stepList: [
        { title: '选模板' },
        { title: '选文案' },
        { title: '选素材' },
        { title: '选音乐' },
        { title: '批量生成' }
      ],
      // 存储各步骤选择的数据
      selectedData: {
        templates: [],
        topicList: [],
        assetInfo: {
          selectedIds: [],
          position: 'half'
        },
        bgMuseicList: [],
        rule: {
          sginGenCount: 3,
          randomDrFlag: true,
          randomBgFlag: true,
          rewriteFlag: true,
          aiSmartPicFlag: false
        }
      },
      statusBarHeight: 0,
      powerEstimateVisible: false,
      draftBoxVisible: false,
      estimatedVideoCount: 0,
      estimatedPower: 0,
      generateData: null,
      draft: null,
      from: null
    }
  },
  created() {
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
  },
  onLoad(options) {
    // 获取草稿箱
    this.draft = uni.getStorageSync('batchVideoDraft');
    this.from = options.from;
    if (this.draft) {
      this.draft = JSON.parse(this.draft);
      if (!this.from) {
        try {
          // 如果有草稿数据，显示草稿箱弹窗
          this.draftBoxVisible = true;
        } catch (e) {
          console.error('草稿数据解析失败:', e);
          // 清除无效的草稿数据
          uni.removeStorageSync('batchVideoDraft');
        }
      } else {
        // 如果是从我的批量视频页面跳转过来的，则直接跳转到生成页面
        this.useDraft()
        this.current = 4
      }
    }

    // 监听步骤切换事件
    uni.$on('batch-video-prev-step', this.prevStep)
    uni.$on('batch-video-next-step', this.nextStep)
    uni.$on('batch-video-generate', this.generateVideos)

    // 监听数据传递事件
    uni.$on('batch-video-save-templates', (data) => {
      this.selectedData.templates = data || []
    })
    // 已改用组件props和events通信，不再需要全局事件
  },
  onUnload() {
    // 解除事件监听，避免内存泄漏
    uni.$off('batch-video-prev-step')
    uni.$off('batch-video-next-step')
    uni.$off('batch-video-generate')
    uni.$off('batch-video-save-templates')
    // 已改用组件props和events通信，不再需要解绑
  },
  methods: {
    // 点击步骤条
    handleStepClick(index) {
      // 只允许点击当前步骤及以前的步骤
      if (index <= this.current) {
        this.current = index
      }
    },

    // 下一步
    nextStep(data) {
      // 前进到下一步
      if (this.current < this.stepList.length - 1) {
        this.current++;
      }
    },

    // 上一步
    prevStep() {
      if (this.current > 0) {
        this.current--;
      }
    },

    // 批量生成视频
    generateVideos(config) {
      // 合并所有数据
      const generateData = {
        ...this.selectedData,
        ...config
      };

      console.log('开始生成视频:', generateData);

      // 这里可以调用生成视频的API
      // 然后跳转到结果页面
    },

    // 保存模板
    saveTemplates(data) {
      this.selectedData.templates = data || [];
      this.saveDraft();
    },

    // 保存文案
    saveTopicList(data) {
      this.selectedData.topicList = data || [];
      this.saveDraft();
    },

    // 保存素材
    saveAssetInfo(data) {
      this.selectedData.assetInfo = data || {
        selectedIds: [],
        position: 'half'
      };
      this.saveDraft();
    },

    // 保存音乐
    saveBgMuseicList(data) {
      this.selectedData.bgMuseicList = data || [];
      this.saveDraft();
    },

    // 保存生成规则
    saveRule(data) {
      this.selectedData.rule = data || {};
      this.saveDraft();
    },

    // 保存草稿的通用方法
    saveDraft() {
      // 检查是否有实际编辑内容
      const hasContent = (
        (this.selectedData.templates && this.selectedData.templates.length > 0) ||
        (this.selectedData.topicList && this.selectedData.topicList.length > 0) ||
        (this.selectedData.assetInfo && this.selectedData.assetInfo.selectedIds.length > 0) ||
        (this.selectedData.bgMuseicList && this.selectedData.bgMuseicList.length > 0)
      );

      // 只有当有实际内容时才保存草稿
      if (hasContent) {
        uni.setStorageSync('batchVideoDraft', JSON.stringify(this.selectedData));
      }
    },

    // 开始生成
    async startGenerate() {
      console.log(this.selectedData, 'this.selectedData');
      try {
        // 先准备生成数据
        const generateData = {
          templates: this.selectedData.templates.map(item => {
            return {
              templateId: item.id,
              voiceId: item.voiceId,
              speed: item.speed
            }
          }),
          topicList: this.selectedData.topicList,
          rule: this.selectedData.rule,
          bgMusicList: this.selectedData.bgMuseicList.map(item => item.url),
          assetInfo: {
            selectedIds: this.selectedData.assetInfo.selectedIds.map(item => item.outId),
            position: this.selectedData.assetInfo.position === 'bottom' ? 'bottom' : 'full-screen'
          },
          // 模板未更改数据 再次生成用
          beforeGenerateData: this.selectedData
        }

        // 保存生成数据用于后续生成
        this.generateData = generateData;

        // 调用算力预估API
        uni.showLoading({
          title: '计算中...',
          mask: true
        });

        

        const res = await postBatchVideoPower(generateData);
        uni.hideLoading();

        if (res.code === 200) {
          // 设置预估数据
          this.estimatedVideoCount = generateData.topicList.length * generateData.rule.sginGenCount;
          this.estimatedPower = res.data || 0;

          // 显示算力预估弹窗
          this.powerEstimateVisible = true;
        } else {
          uni.showToast({
            title: res.message || '获取算力预估失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.log(error, 'power estimate error');
        uni.showToast({
          title: '获取算力预估失败',
          icon: 'none'
        });
      }
    },

    // 实际生成视频
    async actualGenerate() {
      try {
        uni.showLoading({
          title: '生成中...',
          mask: true
        });

        const res = await postBatchVideo(this.generateData);
        uni.hideLoading();

        if (res.code === 200) {
          // 生成成功，清除草稿数据
          uni.removeStorageSync('batchVideoDraft');

          uni.navigateTo({
            url: '/subpkg/index/numbe_user/genVideoResult'
          })
        } else {
          uni.showToast({
            title: res.message || '生成失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.log(error, 'generate error');
        uni.showToast({
          title: '生成失败',
          icon: 'none'
        });
      }
    },
    beforeJump() {
      // 保存草稿
      this.saveDraft();
    },
    // 取消生成
    cancelGenerate() {
      this.powerEstimateVisible = false;
    },

    // 确认生成
    confirmGenerate() {
      this.powerEstimateVisible = false;
      this.actualGenerate();
    },

    // 开始新文件
    startNewFile() {
      this.draftBoxVisible = false;
      // 清除草稿数据
      uni.removeStorageSync('batchVideoDraft');
      // 重置所有数据为初始状态
      this.selectedData = {
        templates: [],
        topicList: [],
        assetInfo: {
          selectedIds: [],
          position: 'half'
        },
        bgMuseicList: [],
        rule: {
          sginGenCount: 3,
          randomDrFlag: true,
          randomBgFlag: true,
          rewriteFlag: true,
          // ai智能配图
          aiSmartPicFlag: false
        }
      };
      this.current = 0;
    },

    // 使用草稿
    useDraft() {
      this.draftBoxVisible = false;
      if (this.draft) {
        // 恢复草稿数据
        this.selectedData = {
          ...this.selectedData,
          ...this.draft
        };

        // 根据草稿数据计算应该跳转到哪一步
        let targetStep = 0;
        if (this.selectedData.templates && this.selectedData.templates.length > 0) {
          targetStep = 1;
        }
        if (this.selectedData.topicList && this.selectedData.topicList.length > 0) {
          targetStep = 2;
        }
        if (this.selectedData.assetInfo && this.selectedData.assetInfo.selectedIds.length > 0) {
          targetStep = 3;
        }
        if (this.selectedData.bgMuseicList && this.selectedData.bgMuseicList.length > 0) {
          targetStep = 4;
        }

        // 跳转到对应步骤
        this.current = targetStep;

        if (!this.from) {
          uni.showToast({
            title: '已恢复草稿内容',
            icon: 'success',
            duration: 2000
          });
        }
      }
    }

  },
  components: {
    steps,
    selectTemplate,
    selectWriter,
    selectMaterial,
    selectMusic,
    selectGenerate
  }
}
</script>

<style lang="scss" scoped>
.batch-video-config {
  background-color: #f3f5f8;
  width: 100%;
  min-height: 100vh;
}

.batch-video-config-content {
  padding: 0 32rpx;
  box-sizing: border-box;
}
</style>
<style lang="scss">
// 插槽样式穿透
.steps-item-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .steps-item-content-icon {
    width: 64rpx;
    height: 32rpx;
  }
}

// 组将样式穿透
.steps-container {
  .u-steps-item {
    .u-steps-item__wrapper {
      background: none;
    }
  }
}

// 算力预估弹窗样式
.power-estimate-popup {
  width: 600rpx;
  padding: 60rpx 48rpx 48rpx;
  background-color: #ffffff;
  border-radius: 20rpx;

  .popup-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
    text-align: center;
    margin-bottom: 48rpx;
  }

  .popup-content {
    font-size: 28rpx;
    color: #333333;
    line-height: 44rpx;
    text-align: center;
    margin-bottom: 64rpx;
  }

  .popup-buttons {
    display: flex;
    justify-content: space-between;
    gap: 32rpx;

    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 88rpx;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .btn-text {
        font-size: 32rpx;
        font-weight: 500;
      }
    }

    .cancel-btn {
      background-color: #f5f5f5;
      border: 1rpx solid #e6e6e6;

      .btn-text {
        color: #666666;
      }
    }

    .confirm-btn {
      background-color: #333333;

      .btn-text {
        color: #ffffff;
      }
    }
  }
}
</style>
