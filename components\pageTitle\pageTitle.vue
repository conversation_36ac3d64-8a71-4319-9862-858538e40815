<template>
  <!-- 数字人视频生成成功通知弹窗 -->
  <view v-if="true" class="notification-overlay">
    <view class="video-notification">
      <view class="notification-content">
        <text class="notification-icon">✨</text>
        <text class="notification-text">{{ videoGeneratedNotification.message }}</text>
      </view>
      <view class="notification-close" @tap="hideVideoNotification">
        <text class="close-icon">✕</text>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import { get_msgList } from '@/api/getData.js'

export default {
  data() {
    return {
    }
  },

  computed: {
    ...mapState('user', ['videoGeneratedNotification'])
  },

  watch: {
    // 监听Vuex中的通知状态变化
    'videoGeneratedNotification.show'(newVal) {
      if (newVal) {
        this.showVideoNotification();
      }
    }
  },
  methods: {
    ...mapMutations('user', ['HIDE_VIDEO_GENERATED_NOTIFICATION']),

    // 显示数字人视频生成成功通知
    showVideoNotification() {
      console.log('显示数字人视频生成成功通知');
    },

    // 隐藏数字人视频生成成功通知
    async hideVideoNotification() {
      try {
        // 调用已读接口
        await get_msgList({
          pageNum: 1,
          pageSize: 10
        });
        console.log('消息已标记为已读');
      } catch (error) {
        console.error('标记消息已读失败:', error);
      }

      // 更新Vuex状态
      this.HIDE_VIDEO_GENERATED_NOTIFICATION();
      console.log('隐藏数字人视频生成成功通知');
    }
  },

  mounted() {
  },

  beforeDestroy() {
  }
}
</script>

<style scoped lang="scss">
/* 数字人视频生成成功通知弹窗样式 */
.notification-overlay {
  position: fixed;
  top: 180rpx;
  left: 0;
  right: 0;
  z-index: 10075;
  pointer-events: none;

  /* 获取系统状态栏高度 */
  padding-top: env(safe-area-inset-top);
  padding-top: constant(safe-area-inset-top);
  /* iOS < 11.2 */
}

.video-notification {
  margin: 20rpx 32rpx 0;
  padding: 24rpx 32rpx;
  background: white;
  color: rgb(0, 0, 0);
  min-height: 80rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-between;
  pointer-events: auto;
}

.notification-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.notification-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.notification-text {
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1.4;
  flex: 1;
}

.notification-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  margin-left: 16rpx;
  border-radius: 50%;
  background: rgb(206, 206, 206);
}

.close-icon {
  font-size: 24rpx;
  color: white;
  font-weight: bold;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .video-notification {
    margin: 15rpx 24rpx 0;
    padding: 20rpx 24rpx;
    min-height: 70rpx;
  }

  .notification-icon {
    font-size: 28rpx;
    margin-right: 12rpx;
  }

  .notification-text {
    font-size: 26rpx;
  }

  .notification-close {
    width: 44rpx;
    height: 44rpx;
    margin-left: 12rpx;
  }

  .close-icon {
    font-size: 22rpx;
  }
}
</style>
