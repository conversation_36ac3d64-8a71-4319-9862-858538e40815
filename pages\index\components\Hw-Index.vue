<template>
  <view class="page">
    <view class="page-bg"
      style="background-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/common/index-hewo-bg.png');">
    </view>
    <view class="page-content">
      <view class="make-video"
        style="background-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/common/hewo/he-sub-bg.png');">
        <view class="make-video-left" @click="get_radio">
          <text class="title-text">创作数字人视频</text>
          <view class="make-video-left-bg"
            style="background-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/common/hewo/%E7%B4%A0%E6%9D%902x.png');">
            <!-- <image src="/static/index_lan/素材@2x.png"></image> -->
            <view class="make-video-left-bg-text">立即创作
              <image src="/static/index_lan/<EMAIL>">
              </image>
            </view>
          </view>
        </view>

        <view class="make-video-right"
          style="background-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/common/hewo/image2x.png');">
          <view class="batch-video" @click="go_batchVideo">
            <view class="batch-video-left">
              <text class="batch-video-title">批量视频
              </text>
              <text class="batch-video-describe">高效量产，一键智作</text>
            </view>
            <image class="batch-video-icon" src="/static/index_lan/Group <EMAIL>"></image>
          </view>
          <view class="divider"></view>
          <view class="extraction" @click="go_get_message">
            <view class="extraction-left">
              <text class="extraction-title">视频文案提取
              </text>
              <text class="extraction-describe">克隆音色，精准还原</text>
            </view>
            <image class="extraction-icon" src="/static/index_lan/<EMAIL>"></image>
          </view>
        </view>
      </view>
      <view class="other-function">
        <view class="customized-human" @click="addNumberUser"
          style="background-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/common/hewo/hewo-sub-bg2.png');">
          <view class="customized-human-left">
            <text class="customized-human-title">定制数字人
            </text>
            <text class="customized-human-describe">形象克隆，分身有术</text>
          </view>
          <image src="/static/index_lan/icon@2x(1).png"></image>
        </view>

        <view class="voice-cloning" @click="go_getSound"
          style="background-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/common/hewo/hewo-sub-bg2.png');">
          <view class="voice-cloning-left">
            <text class="voice-cloning-title">声音克隆
            </text>
            <text class="voice-cloning-describe">
              克隆音色，精准还原</text>
          </view>
          <image src="/static/index_lan/Group 2@2x(1).png"></image>
        </view>
      </view>
      <view class="tool-assistant">
        <text class="tool-title">工具<text class="tool-title-sub">助手</text></text>
        <view class="tool-list">
          <view class="tool-item" v-for="item in listData" :key="item.id" @click="go_copy(item)">
            <image class="tool-icon" :src="item.icon">
            </image>
            <view class="tool-content">
              <text class="tool-name">{{ item.name }}</text>
              <text class="tool-part">{{ item.description }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <TabBar :index="0" active-color="white" inactive-color="white" custom-style="background:rgb(29,36,41)" />
    <InsufficientPermissions v-if="page_model" @closeModel="close"></InsufficientPermissions>
    <globalLoginModal />
    <vipVersion :pageShow="page_show" @show_model="show_model" @down_close="page_show = false" source="home"
      :fastPowerRange="configInfo.fastPowerRange" :numberType="number_type"></vipVersion>

  </view>

</template>
<script>
import {
  page_img_statr
} from '../../../api/getData.js'
import { mapGetters, mapActions } from 'vuex'
import TabBar from "@/components/fixedTabBar/index.vue";

export default {
  components: {TabBar},
  props:{
    configInfo:{
      type:Object,
      default:{}
    }
  },
  data() {
    return {
      pageReady: false,
      page_show: false,
      model_login: false,
      listData: [],
      page_model: false,
      number_type: '',
      appName: '',
      page_img_statr: '',
      appId: ''
    }
  },
  computed: {
    ...mapGetters('user', ['isLogin', 'userInfo'])
  },
  watch: {
    '$appConfig.appInfo': {
      handler(newAppInfo) {
        if (newAppInfo && newAppInfo.appName) {
          this.appName = newAppInfo.appName;
        }
      },
      deep: true,  // 如果需要深度监听
      immediate: true  // 是否立即执行一次
    }
  },
  async mounted() {
    // uni.hideTabBar()
    this.page_img_statr = page_img_statr
    this.appId = uni.getAccountInfoSync().miniProgram.appId

    this.get_tool()
  },
  onShow() {
    this.number_type = ''
    this.page_show = false
  },
  methods: {
    // 检查登录状态，如未登录则弹出登录框
    checkLoginStatus(targetUrl, options = {}) {
      if (!targetUrl && !options.callback) return false;

      return this.$checkLogin({
        success: (userData) => {
          // 登录成功，根据参数处理后续操作
          if (options.callback && typeof options.callback === 'function') {
            // 如果提供了回调函数，直接调用
            options.callback(userData);
          } else if (targetUrl) {
            // 如果提供了目标URL，则跳转
            uni.navigateTo({
              url: targetUrl,
              success: options.success,
              fail: options.fail,
              complete: options.complete
            });
          }
        },
        cancel: () => {
          // 用户取消登录，不执行后续操作
          uni.showToast({
            title: options.cancelTip || '需要登录后才能访问',
            icon: 'none'
          });

          if (options.onCancel && typeof options.onCancel === 'function') {
            options.onCancel();
          }
        },
        fail: (err) => {
          // 登录失败
          if (options.onFail && typeof options.onFail === 'function') {
            options.onFail(err);
          }
        }
      });
    },

    go_get_message() {
      this.checkLoginStatus('/subpkg/index/extractionCopy', {
        cancelTip: '需要登录后才能使用文案提取'
      });
    },

    go_batchVideo() {
      this.checkLoginStatus('/subpkg/batchVideoHome/index', {
        cancelTip: '需要登录后才能使用批量视频'
      });
    },

    get_again() {

    },

    close(value) {
      this.page_model = value
    },

    show_model(value) {
      this.page_show = false
      this.page_model = value
    },

    addNumberUser() {
      this.checkLoginStatus(null, {
        callback: () => {
          this.number_type=''
          if (this.configInfo.enableFastClone == '0') {
            this.page_show = true
          } else {
            uni.navigateTo({
              url: '/subpkg/index/numbe_user/index?type=pro'
            })
          }
        },
        cancelTip: '需要登录后才能定制数字人'
      });
    },

    go_copy(item) {
      console.log('item', item);
      // 直接跳转到文案页面，不检查登录状态
      uni.navigateTo({
        url: '/subpkg/index/page_copy?source=home',
        success: (res) => {
          res.eventChannel.emit('get_message', item)
        }
      });
    },

    go_getSound() {
      this.checkLoginStatus(null, {
        callback: () => {
          this.number_type = 'sound'
          if (this.configInfo.enableFastClone == '0') {
            this.page_show = true
          } else {
            uni.navigateTo({
              url: '/subpkg/index/get_sound/index?type=pro'
            })
          }
        },
        cancelTip: '需要登录后才能使用声音克隆'
      });
    },

    get_tool() {
      this.$api.get_authorization({ext:this.appId}).then(res => {
        this.listData = res.rows
      })
    },

    get_radio() {
      this.checkLoginStatus('/subpkg/index/get_radio/index', {
        cancelTip: '需要登录后才能创作数字人视频'
      });
    }
  }
}
</script>
<style lang="scss" scoped>
.page {
  .page-bg {
    background-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/common/common/index-hewo-bg.png');
    width: 100%;
    height: 696rpx;
    background-size: 101% 100%;
    background-repeat: repeat-x;
  }

  .page-content {
    padding: 40rpx 32rpx 200rpx 32rpx;
    background-color: #141414;
    width: 100%;
    border-radius: 48rpx 48rpx 0rpx 0rpx;
    margin-top: -50rpx;

    .make-video {
      width: 100%;
      width: 686rpx;
      height: 360rpx;
      background: linear-gradient(135deg, #2E323B 0%, #212732 100%);
      border-radius: 24rpx 24rpx 24rpx 24rpx;
      border: 2rpx solid;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 32rpx;
      box-sizing: border-box;
      background-size: cover;
      background-repeat: no-repeat;

      .make-video-left {

        flex: 1;
        height: 100%;

        .title-text {
          font-family: ChillRoundGothic_Bold;
          font-weight: bold;
          font-size: 28rpx;
          color: #FFFFFF;
        }

        .make-video-left-bg {
          width: 184rpx;
          height: 256rpx;
          background-size: cover;
          background-repeat: no-repeat;
          position: relative;

          .make-video-left-bg-text {
            width: 184rpx;
            height: 56rpx;
            background: #92EACE;
            border-radius: 48rpx 48rpx 48rpx 48rpx;
            font-weight: 600;
            font-size: 24rpx;
            color: #333333;
            text-align: center;
            line-height: 56rpx;
            position: absolute;
            bottom: 0;
            left: 0;

            image {
              width: 20rpx;
              height: 20rpx;
            }
          }
        }
      }

      .make-video-right {
        width: 358rpx;
        height: 296rpx;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        border: 2rpx solid;
        padding: 24rpx;
        background-size: cover;
        background-repeat: no-repeat;
        // display: flex;
        // flex-direction: column;
        // justify-content: center;

        .batch-video {
          display: flex;
          // flex-direction: column;
          justify-content: space-between;

          .batch-video-left {
            display: flex;
            flex-direction: column;
            margin-top: 10rpx;
            margin-bottom: 32rpx;

            .batch-video-title {
              font-weight: bold;
              font-size: 28rpx;
              color: #333333;
            }

            .batch-video-describe {
              font-weight: 500;
              font-size: 20rpx;
              color: #333333;
              margin-top: 8rpx;
            }
          }

          .batch-video-icon {
            width: 56rpx;
            height: 56rpx;
            border-radius: 20rpx 20rpx 20rpx 20rpx;
            margin-top: 10rpx;
          }
        }

        .divider {
          border-top: 1rpx solid #FFFFFF;
          width: 100%;
        }

        .extraction {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: 32rpx;
          // align-items: center;

          .extraction-left {
            display: flex;
            flex-direction: column;

            .extraction-title {
              font-weight: bold;
              font-size: 28rpx;
              color: #333333;
            }

            .extraction-describe {
              font-weight: 500;
              font-size: 20rpx;
              color: #333333;
              margin-top: 8rpx;
            }
          }

          .extraction-icon {
            width: 56rpx;
            height: 56rpx;
            border-radius: 20rpx 20rpx 20rpx 20rpx;
            margin-top: 10rpx;
          }
        }
      }
    }

    .other-function {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-top: 16rpx;

      .customized-human {
        width: 336rpx;
        height: 136rpx;
        border-radius: 24rpx;
        padding: 32rpx;
        background-size: cover;
        background-repeat: no-repeat;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .customized-human-left {
          display: flex;
          flex-direction: column;

          .customized-human-title {
            font-weight: 600;
            font-size: 28rpx;
            color: #FFFFFF;
          }

          .customized-human-describe {
            font-weight: 500;
            font-size: 20rpx;
            color: #999999;
            margin-top: 4rpx;
          }
        }
      }



      .voice-cloning {
        width: 334rpx;
        height: 136rpx;
        border-radius: 24rpx;
        padding: 32rpx;
        background-size: cover;
        background-repeat: no-repeat;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .voice-cloning-left {
          display: flex;
          flex-direction: column;

          .voice-cloning-title {
            font-weight: 600;
            font-size: 28rpx;
            color: #FFFFFF;
          }

          .voice-cloning-describe {
            font-weight: 500;
            font-size: 20rpx;
            color: #999999;
            margin-top: 4rpx;
          }
        }
      }
    }

    .tool-assistant {
      margin-top: 32rpx;

      .tool-title {
        font-weight: bold;
        font-size: 36rpx;
        color: #FFFFFF;
      }

      .tool-list {
        margin-top: 24rpx;

        .tool-item {
          height: 144rpx;
          display: flex;
          align-items: center;
          padding: 0 32rpx;

          .tool-icon {
            width: 72rpx;
            height: 72rpx;
          }

          .tool-content {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-left: 24rpx;

            .tool-name {
              font-weight: 600;
              font-size: 28rpx;
              color: #E1E1E1;
            }

            .tool-part {
              font-weight: 500;
              font-size: 24rpx;
              color: #999999;
            }
          }
        }
      }
    }
  }
}
</style>
