import { getCustomTemplate as getCustomTemplateApi } from '@/api/app/template.js'
/**
 * 装修数据加载工具
 * 用于在移动端加载PC端装修好的数据
 */

/**
 * 保存装修数据到本地存储
 * @param {Object} data 装修数据
 */
export function saveFitmentDataToStorage(data) {
  try {
    uni.setStorageSync('fitment_data', JSON.stringify(data))
    return true
  } catch (error) {
    console.error('保存装修数据失败:', error)
    return false
  }
}

/**
 * 坐标转换：将栅格坐标转换为rpx
 * @param {Number} gridValue 栅格值
 * @param {Boolean} isSize 是否为尺寸（宽度/高度）
 * @returns {Number} rpx值
 */
export function convertGridToRpx(gridValue, isSize = false) {
  const gridSize = 79.75 // 每个栅格的大小
  const gridGap = 8 // 栅格间距

  if (isSize) {
    // 对于宽度和高度，需要计算栅格数量和间距
    return gridValue * gridSize + (gridValue - 1) * gridGap
  } else {
    // 对于位置，需要计算栅格位置和累积间距
    return gridValue * gridSize + gridValue * gridGap
  }
}

/**
 * 获取组件基础样式（位置和尺寸）
 * @param {Object} item 布局项
 * @returns {Object} 样式对象
 */
export function getComponentStyle(item) {
  const position = item.position || {}
  const left = convertGridToRpx(position.x || 0)
  const top = convertGridToRpx(position.y || 0)
  const width = convertGridToRpx(position.width || 1, true)
  const height = convertGridToRpx(position.height || 1, true)

  return {
    position: 'absolute',
    left: `${left * 2 + 32}rpx`,
    top: `${top * 2 + 32}rpx`,
    width: `${width * 2}rpx`,
    height: `${height * 2}rpx`,
    zIndex: 10
  }
}

/**
 * 获取图片组件样式
 * @param {Object} item 组件项
 * @returns {Object} 样式对象
 */
export function getImageComponentStyle(item) {
  const config = item.config || {}
  const imageWidth = config.imageWidth || 100
  const imageHeight = config.imageHeight || 100

  return {
    width: `${imageWidth}%`,
    height: `${imageHeight}%`,
    borderRadius: `${config.borderRadius * 2 || 0}rpx`
  }
}

/**
 * 获取文字组件样式
 * @param {Object} item 组件项
 * @returns {Object} 样式对象
 */
export function getTextComponentStyle(item) {
  const config = item.config || {}
  return {
    textAlign: config.align || 'center',
    color: config.color || '#000000',
    fontSize: `${config.size * 2 || 16}rpx`,
    fontWeight: config.fontWeight || 'normal',
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: config.align === 'left' ? 'flex-start' :
      config.align === 'right' ? 'flex-end' : 'center'
  }
}

/**
 * 获取功能组件样式
 * @param {Object} item 组件项
 * @returns {Object} 样式对象
 */
export function getFunctionComponentStyle(item) {
  const config = item.config || {}
  const style = {
    width: '100%',
    height: '100%',
    borderRadius: `${config.borderRadius * 2 || 8}rpx`,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    overflow: 'hidden'
  }

  // 背景样式 - 只在非图片模式下设置
  if (config.backgroundMode !== 'image' || !config.backgroundImageUrl) {
    if (config.backgroundGradient) {
      style.backgroundImage = config.backgroundGradient
    } else {
      style.backgroundColor = config.backgroundColor || 'rgba(33, 189, 116, 0.3)'
    }
  }

  return style
}

/**
 * 获取功能组件背景图片样式
 * @param {Object} item 组件项
 * @returns {Object} 样式对象
 */
export function getFunctionBackgroundImageStyle(item) {
  const config = item.config || {}
  const width = config.backgroundWidth || 100
  const height = config.backgroundHeight || 100

  return {
    width: `${width}%`,
    height: `${height}%`,
    objectFit: 'cover',
    objectPosition: 'center'
  }
}

/**
 * 获取功能组件图标样式
 * @param {Object} item 组件项
 * @returns {Object} 样式对象
 */
export function getFunctionIconStyle(item) {
  const config = item.config || {}
  const iconSize = config.iconSize || 32
  const iconWidth = config.iconWidth || 100
  const iconHeight = config.iconHeight || 100

  const actualWidth = (iconSize * iconWidth) / 100
  const actualHeight = (iconSize * iconHeight) / 100

  return {
    width: `${actualWidth * 2}rpx`,
    height: `${actualHeight * 2}rpx`
  }
}

/**
 * 获取功能组件大图标样式
 * @param {Object} item 组件项
 * @returns {Object} 样式对象
 */
export function getFunctionBigIconStyle(item) {
  const config = item.config || {}
  const baseIconSize = config.iconSize || 32
  const iconWidth = config.iconWidth || 100
  const iconHeight = config.iconHeight || 100
  const bigIconSize = Math.floor(baseIconSize * 1.5)

  const actualWidth = (bigIconSize * iconWidth) / 100
  const actualHeight = (bigIconSize * iconHeight) / 100

  return {
    width: `${actualWidth * 2}rpx`,
    height: `${actualHeight * 2}rpx`
  }
}

/**
 * 获取功能组件标题样式
 * @param {Object} item 组件项
 * @returns {Object} 样式对象
 */
export function getFunctionTitleStyle(item) {
  const config = item.config || {}
  return {
    color: config.titleColor || '#fff',
    fontSize: `${config.titleSize * 2 || 16}rpx`,
    fontWeight: '500'
  }
}

/**
 * 获取功能组件描述样式
 * @param {Object} item 组件项
 * @returns {Object} 样式对象
 */
export function getFunctionDescStyle(item) {
  const config = item.config || {}
  return {
    color: config.descriptionColor || 'rgba(255, 255, 255, 0.8)',
    fontSize: `${config.descriptionSize * 2 || 12}rpx`
  }
}

/**
 * 验证装修数据格式
 * @param {Object} data 装修数据
 * @returns {Boolean} 是否有效
 */
export function validateFitmentData(data) {
  if (!data || typeof data !== 'object') return false

  // 检查必要字段
  const requiredFields = ['backgroundType', 'layout', 'navigationConfig']
  for (const field of requiredFields) {
    if (!(field in data)) return false
  }

  // 检查layout格式
  if (!Array.isArray(data.layout)) return false

  // 检查layout项格式
  for (const item of data.layout) {
    if (!item.i || typeof item.x !== 'number' || typeof item.y !== 'number' ||
      typeof item.w !== 'number' || typeof item.h !== 'number') {
      return false
    }
  }

  return true
}

/**
 * 清理和标准化装修数据
 * @param {Object} data 原始装修数据
 * @returns {Object} 清理后的装修数据
 */
export function sanitizeFitmentData(data) {
  const sanitized = { ...data }

  // 过滤掉临时拖拽项
  if (sanitized.layout) {
    sanitized.layout = sanitized.layout.filter(item => item.i !== 'drop')
  }

  // 确保必要的默认值
  sanitized.backgroundType = sanitized.backgroundType || '色彩填充'
  sanitized.backgroundColor = sanitized.backgroundColor || '#FFFFFF'
  sanitized.backgroundImage = sanitized.backgroundImage || ''

  // 确保导航配置
  if (!sanitized.navigationConfig) {
    sanitized.navigationConfig = getDefaultFitmentData().navigationConfig
  }

  return sanitized
}

/**
 * 获取自定义模板数据
 * @param {String} appId 小程序appId
 * @returns {Promise<Object>} 模板数据或null
 */
export async function getCustomTemplate(appId) {
  try {
    if (!appId) {
      console.warn('未提供appId，无法获取自定义模板')
      return null
    }
    
    // 调用API获取自定义模板
    const response = await getCustomTemplateApi({ appId })
    
    if (response && response.data) {
      // 如果API返回了有效数据，使用模板配置
      return response.data.templateConfig
    } else {
      // 如果API没有返回有效数据，返回null
      console.warn('获取自定义模板失败，API返回无效数据')
      return null
    }
  } catch (error) {
    console.error('获取模板数据失败:', error)
    return null
  }
}
