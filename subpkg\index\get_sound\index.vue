<template>
	<view class="page">

		<custom-navbar title="克隆声音" :showBack="true" titleColor="white"></custom-navbar>

		<!-- 使用InnerAudioContext进行音频播放，无需UI组件 -->

		<view class="card_box">
			<view style="margin-top: 40rpx;">
				<u-tabs :list="list" :scrollable="false" :activeStyle="{ color: 'white' }" :inactiveStyle="{ color: '#a1a1a1' }"
					lineColor="rgb(244,194,162)" @change="down_tab" keyName="type"></u-tabs>
			</view>
			<view class="page_content">
				{{ content }}
			</view>
		</view>

		<view style="margin-top: 700rpx;">
			<view style="font-size: 24rpx;opacity: 0.5;line-height: 40rpx;">
				<view>1.选择一个安静的环境，录制时长需至少 10 秒。</view>
				<view>2.参考文案仅作参考，无需逐字念，可自由发挥。</view>
				<view>3.语气和风格会被克隆，需要选择合适的参考文案风格。</view>
			</view>
		</view>


		<view class="page_bottom">
			<view class="content" v-if="state == 0" @click="startRecord">
				<image src="/static/home/<USER>/luyin.png"></image>
				<view style="font-size: 30rpx">点击录制</view>
			</view>

			<view class="content" v-if="state == 1" @click="endRecord">
				<image src="/static/home/<USER>/luzhi.png"></image>
				<view>{{ page_time }}</view>
			</view>

			<view v-if="state == 2" style="display: flex;justify-content: space-around;align-items: center;width: 100vw;">
				<u-icon name="reload" size="60rpx" @click="model_wxts = true"></u-icon>

				<view class="content" v-if="!playing" @click="playAudio">
					<image src="/static/home/<USER>/bofangluyin.png"></image>
					<view>点击试听</view>
				</view>
				<view class="content" v-else @click="stopAudio">
					<image src="/static/home/<USER>/zanting.png"></image>
				</view>

				<u-icon name="checkmark" size="60rpx" @click="choseLanguage('')"></u-icon>
			</view>
		</view>

		<view v-if="modelSound_show" class="project_model_1" style="color: black;">
			<view class="project_model_2">
				<view style="text-align: center;font-weight: 600;">时长不足</view>
				<view style="text-align: center;margin: 40rpx 0;color: #666666;font-size: 30rpx">录制时间过短，请一字不漏的朗读提示词中的内容。</view>
				<view class="project_btn" @click="again_video">重新录制</view>
			</view>
		</view>

		<view v-if="model_wxts" class="project_model_1" style="color: black;">
			<view class="project_model_2">
				<view style="text-align: center;font-weight: 600;">温馨提示</view>
				<view style="text-align: center;margin: 40rpx 0;color: #666666;font-size: 30rpx">您确认要重新录制吗?重新录制将丢失当前录制内容。</view>
				<view class="page_btn">
					<view class="btn1" @click="model_wxts = false">取消</view>
					<view class="btn2" @click="again_video">重新录制</view>
				</view>
			</view>
		</view>

		<!-- 语种选择弹窗 -->
		<view v-if="language_show" class="project_model_1" style="color: black;">
			<view class="project_model_2">
				<view style="text-align: center;font-weight: 600;">提示</view>
				<view style="text-align: center;margin: 40rpx 0;color: #666666;font-size: 30rpx">请选择声音克隆所使用的语种</view>
				<view class="page_btn">
					<view class="btn1" @click="choseLanguage('cn-dialect')">方 言</view>
					<view class="btn2" @click="choseLanguage('')">普通话</view>
				</view>
			</view>
		</view>

		<!-- 弹窗 -->
		<view v-if="uploading" class="project_model_1" style="background: rgba(0, 0, 0, 0.9);">
			<view class="project_model_2" style="background: none;color: white;">
				<u-loading-icon mode="circle" size="80rpx"></u-loading-icon>
				<view style="text-align: center;margin-top: 30rpx;">音频上传中</view>
				<view style="text-align: center;margin: 20rpx 0;font-size: 24rpx;opacity: 0.8;">请勿熄屏或切换应用</view>
				<view style="text-align: center;margin-top: 10rpx;" v-if="uploadProgress > 0">
					<text>{{ Math.round(uploadProgress) }}%</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import COS from 'cos-wx-sdk-v5'
import { util } from '../../../utils/file.js'
import { getUploadFileSts } from '../../../api/numberUser/dr.js'
import {
	getPageConfigInfo
} from '../../../api/app/config.js'
import list from '../../../uni_modules/uview-ui/libs/config/props/list.js'

export default {
	data() {
		return {
			type: false,
			source: '',
			authorization: false,
			sound_state: false,
			page_currend: 0,
			model_wxts: false,
			playing: false, // 音频播放状态
			audioContext: null, // 音频播放上下文
			modelSound_show: false,
			clTime: '',
			voicePath: '',
			state: 0,
			list: [],
			content: '',
			time1: 0,
			time2: 0,
			recorderManager: null,
			statusBarHeight: 0,
			navBarHeight: 0,
			customBarHeight: 0,
			free: false,
			language_show: false,
			uploading: false,
			post_url: '', // COS上传后的URL
			selectedLanguage: '', // 选择的语种
			uploadProgress: 0, // 上传进度
			// 录音配置参数
			recordConfig: {
				duration: 600000, // 最大录音时长10分钟
				sampleRate: 16000, // 采样率16kHz，适合语音AI处理
				numberOfChannels: 1, // 单声道，语音录制推荐
				encodeBitRate: 48000, // 编码码率48kbps，保证语音清晰度
				format: 'mp3', // MP3格式，兼容性好且文件小
				frameSize: 50 // 指定帧大小，单位 KB
			}
		}
	},
	onLoad(option) {
		console.log("进入声音克隆")

		getPageConfigInfo({
			type: 'voice'
		}).then(res => {
			console.log(res)
			this.list = JSON.parse(res.data)
			this.content = this.list[0].text
		})

		this.type = option?.type
		this.free = option?.free
		this.source = option?.source
		let that = this
		uni.authorize({
			scope: 'scope.record',
			success() {
				that.authorization = true
				console.log('授权成功');
			},
			fail() {
				that.authorization = false
				console.log('授权失败');
			}
		})

		this.recorderManager = uni.getRecorderManager();

		// 设置录音质量为高质量（适合声音克隆）
		this.setRecordQuality('high');

		// 监听录音结束
		this.recorderManager.onStop((res) => {
			console.log('录音结束，结果：', res)
			this.voicePath = res.tempFilePath;
			console.log('录音文件路径：', this.voicePath);
			console.log('录音时长：', res.duration + 'ms');
			console.log('录音文件大小：', res.fileSize + 'bytes');
		})

		// 监听录音开始
		this.recorderManager.onStart(() => {
			console.log('录音开始');
		})

		// 监听录音错误
		this.recorderManager.onError((err) => {
			console.error('录音错误：', err);
			// uni.showToast({
			// 	title: '录音失败：' + err.errMsg,
			// 	icon: 'none',
			// 	duration: 3000
			// });
			this.state = 0;
			this.clear_time();
		})

		const systemInfo = uni.getSystemInfoSync()
		this.statusBarHeight = systemInfo.statusBarHeight // 状态栏高度
		// #ifdef MP-WEIXIN
		const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
		this.navBarHeight = menuButtonInfo.bottom + menuButtonInfo.top - this.statusBarHeight
		// #endif
		this.customBarHeight = this.statusBarHeight + this.navBarHeight // 自定义导航栏总高度
	},
	onReady() {
		// 初始化InnerAudioContext
		this.initInnerAudioContext();
	},
	onHide() {
		this.stopAudio();
		this.state = 0
		this.time1 = 0
		this.time2 = 0
		this.playing = false
	},
	onUnload() {
		this.stopAudio();
		// 销毁音频上下文，释放资源
		if (this.audioContext) {
			this.audioContext.destroy();
			this.audioContext = null;
		}
	},
	computed: {
		page_time() {
			let a = ''
			let b = ''
			if (this.time1 < 10) {
				{
					a = '0' + this.time1
				}
			} else {
				a = this.time1
			}
			if (this.time2 < 10) {
				b = '0' + this.time2
			} else {
				b = this.time2
			}
			return a + ':' + b
		}
	},
	methods: {
		go_back() {
			uni.navigateBack()
		},

		// 初始化InnerAudioContext
		initInnerAudioContext() {
			// 如果已存在，先销毁
			if (this.audioContext) {
				this.audioContext.destroy();
			}

			// 创建新的InnerAudioContext
			this.audioContext = uni.createInnerAudioContext({
				useWebAudioImplement: true
			});

			// 设置音频属性
			this.audioContext.autoplay = false; // 不自动播放
			this.audioContext.loop = false; // 不循环播放
			this.audioContext.volume = 1.0; // 音量设置为最大

			// 监听音频播放事件
			this.audioContext.onPlay(() => {
				console.log('音频开始播放');
				this.playing = true;
			});

			this.audioContext.onPause(() => {
				console.log('音频暂停播放');
				this.playing = false;
			});

			this.audioContext.onStop(() => {
				console.log('音频停止播放');
				this.playing = false;
			});

			this.audioContext.onEnded(() => {
				console.log('音频播放结束');
				this.playing = false;
			});

			this.audioContext.onError((err) => {
				console.error('音频播放错误:', err);
				this.playing = false;
				// uni.showToast({
				// 	title: '音频播放失败',
				// 	icon: 'none'
				// });
			});

			this.audioContext.onCanplay(() => {
				console.log('音频可以播放');
			});

			this.audioContext.onWaiting(() => {
				console.log('音频缓冲中...');
			});

			this.audioContext.onTimeUpdate(() => {
				// 可以在这里更新播放进度
				// console.log('播放进度:', this.audioContext.currentTime, '/', this.audioContext.duration);
			});

			this.audioContext.onSeeked(() => {
				console.log('音频跳转完成');
			});

			this.audioContext.onSeeking(() => {
				console.log('音频跳转中...');
			});
		},

		// 设置录音质量
		setRecordQuality(quality = 'high') {
			switch (quality) {
				case 'high': // 高质量 - 适合声音克隆
					this.recordConfig = {
						duration: 600000,
						sampleRate: 48000,
						numberOfChannels: 1,
						encodeBitRate: 320000,
						format: 'mp3',
						frameSize: 50
					};
					break;
				case 'medium': // 中等质量 - 平衡质量和文件大小
					this.recordConfig = {
						duration: 600000,
						sampleRate: 16000,
						numberOfChannels: 1,
						encodeBitRate: 32000,
						format: 'mp3',
						frameSize: 30
					};
					break;
				case 'low': // 低质量 - 最小文件大小
					this.recordConfig = {
						duration: 600000,
						sampleRate: 8000,
						numberOfChannels: 1,
						encodeBitRate: 16000,
						format: 'mp3',
						frameSize: 20
					};
					break;
			}
			console.log('录音质量设置为：', quality, this.recordConfig);
		},

		//试听示例
		play_sound() {
			this.sound_state = true
		},
		//试听停止
		stop_sound() {
			this.sound_state = false
		},

		// 显示语种选择弹窗
		showLanguageDialog() {
			this.language_show = true
		},

		// 选择语种并上传文件
		choseLanguage(languageType) {
			this.language_show = false
			this.selectedLanguage = languageType
			this.uploadToCOS()
		},

		// 上传到COS
		uploadToCOS() {
			if (!this.voicePath) {
				uni.showToast({
					title: '录音文件不存在',
					icon: 'none'
				})
				return
			}

			this.uploading = true
			this.uploadProgress = 0

			getUploadFileSts({
				scene: 'miniprogram-temp',
				type: 'music',
				extName: 'mp3'
			}).then((res) => {
				this.processUpload(res.data)
			}).catch(err => {
				console.error('获取上传凭证失败:', err)
				uni.showToast({
					title: '获取上传凭证失败',
					icon: 'none'
				})
				this.uploading = false
				this.uploadProgress = 0
			})
		},

		async processUpload(data) {
			const {
				credentials,
				region,
				bucket,
				key,
				startTime,
				expiredTime
			} = data

			try {
				const arrayBuffer = await util.tempPathToArrayBuffer(this.voicePath)
				this.uploadProgress = 10 // 文件准备完成，显示10%进度

				const cos = new COS({
					SecretId: credentials.tmpSecretId,
					SecretKey: credentials.tmpSecretKey,
					SecurityToken: credentials.sessionToken,
					StartTime: startTime,
					ExpiredTime: expiredTime
				});

				cos.putObject({
					Bucket: bucket,
					Region: region,
					Key: key,
					StorageClass: 'STANDARD',
					Body: arrayBuffer,
					onProgress: (progressData) => {
						console.log('上传进度', progressData)
						// 将进度从10%到90%，保留最后10%用于处理
						this.uploadProgress = 10 + Math.floor(progressData.percent * 0.8)
					}
				}, (err, data) => {
					if (err) {
						console.error('上传失败:', err)
						// uni.showToast({
						// 	title: '音频上传失败',
						// 	icon: 'none'
						// })
						this.uploading = false
						this.uploadProgress = 0
					} else {
						console.log('上传成功')
						this.post_url = `https://${bucket}.cos.${region}.myqcloud.com/${key}`
						this.uploadProgress = 100 // 上传完成，显示100%
						// 延迟一小段时间显示100%进度，然后跳转
						setTimeout(() => {
							this.go_save()
						}, 500)
					}
				});
			} catch (err) {
				console.error('文件转换失败:', err)
				// uni.showToast({
				// 	title: '文件处理失败',
				// 	icon: 'none'
				// })
				this.uploading = false
				this.uploadProgress = 0
			}
		},

		//确认提交，跳转到保存页面
		go_save() {
			this.$store.commit('SetNumberUserMusic', this.voicePath);

			// 构建URL，带上post_url和selectedLanguage
			let url = `/subpkg/index/get_sound/save_sound?type=${this.type}&source=${this.source}`;

			if (this.free) {
				url += '&free=1';
			}

			// 添加上传的音频URL和选择的语种
			url += `&post_url=${encodeURIComponent(this.post_url)}&language=${this.selectedLanguage}`;

			uni.redirectTo({ url });
		},

		//重新录制
		again_video() {
			this.stopAudio();
			this.modelSound_show = false
			this.model_wxts = false
			this.clear_time()
			this.state = 0
		},

		clear_time() {
			this.time1 = 0
			this.time2 = 0
		},
		down_tab(e) {
			this.content = e.text
			this.page_currend = e.index
		},
		//开始录音
		startRecord() {
			if (this.authorization) {
				console.log('开始录音，配置参数：', this.recordConfig);
				try {
					this.recorderManager.start(this.recordConfig);
					this.state = 1
					this.clTime = setInterval(() => {
						this.time2++
						if (this.time2 >= 59) this.time1++
						console.log('正在录音')
					}, 1000)
				} catch (error) {
					console.log(error);
					
					this.recorderManager.start();
					this.state = 1
					this.clTime = setInterval(() => {
						this.time2++
						if (this.time2 >= 59) this.time1++
						console.log('正在录音')
					}, 1000)
				}
			} else {
				uni.showToast({
					title: '请开启您的麦克风权限',
					icon: 'none'
				})
			}
		},
		//停止录音
		endRecord() {
			this.recorderManager.stop();
			clearInterval(this.clTime)
			if (this.time2 < 10 && this.time1 < 1) {
				this.state = 0
				this.clear_time()
				this.modelSound_show = true
			}
			this.state = 2
		},
		//播放录音
		playAudio() {
			if (!this.voicePath) {
				// uni.showToast({
				// 	title: '没有录音文件',
				// 	icon: 'none'
				// });
				return;
			}

			if (!this.audioContext) {
				this.initInnerAudioContext();
			}

			try {
				console.log("准备播放", this.voicePath);

				// 如果正在播放，先停止
				if (this.playing) {
					this.audioContext.stop();
				}

				// 设置音频源
				this.audioContext.src = this.voicePath;

				// 开始播放
				this.audioContext.play();

				console.log("音频播放命令已发送");
			} catch (error) {
				console.error("播放音频时出错:", error);
				// uni.showToast({
				// 	title: '播放失败',
				// 	icon: 'none'
				// });
			}
		},

		//停止播放
		stopAudio() {
			try {
				if (this.audioContext) {
					this.audioContext.stop();
					console.log("音频停止播放");
				}
				this.playing = false;
			} catch (error) {
				console.error("停止音频时出错:", error);
				this.playing = false;
			}
		},

		// 暂停播放
		pauseAudio() {
			try {
				if (this.audioContext) {
					this.audioContext.pause();
					console.log("音频暂停播放");
				}
				this.playing = false;
			} catch (error) {
				console.error("暂停音频时出错:", error);
				this.playing = false;
			}
		},

		// 获取音频信息
		getAudioInfo() {
			if (this.audioContext) {
				return {
					duration: this.audioContext.duration || 0,
					currentTime: this.audioContext.currentTime || 0,
					paused: this.audioContext.paused,
					src: this.audioContext.src
				};
			}
			return null;
		}
	}
}
</script>

<style scoped lang="scss">
.page_bg {
	width: 100vw;
	position: absolute;
	top: 0;
	left: 0;
}

.page {
	padding: 40rpx;
	height: 100vh;
	box-sizing: border-box;
	background: black;
	background-image: url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/home_get_sound_bg.png');
	background-size: 100% 40%;
	background-repeat: no-repeat;
	color: white;

	.page_content {
		margin: 0rpx 20rpx;
		line-height: 60rpx;
		z-index: 999;
		font-size: 30rpx;
	}

	.page_bottom {
		position: fixed;
		bottom: 100rpx;
		left: 50vw;
		transform: translate(-50%);
		display: flex;
		justify-content: space-around;
		align-items: center;

		.content {
			image {
				width: 120rpx;
				height: 120rpx;
			}

			text-align: center;
		}
	}

	.page_btn {
		display: flex;
		justify-content: space-around;

		.btn1 {
			text-align: center;
			background: #e5e5e5;
			border-radius: 20rpx;
			width: 220rpx;
			padding: 20rpx 40rpx;
		}

		.btn2 {
			text-align: center;
			background: black;
			color: white;
			border-radius: 20rpx;
			width: 220rpx;
			padding: 20rpx 40rpx;
		}
	}
}

.card_box {
	position: fixed;
	left: 50%;
	top: 300rpx;
	transform: translate(-50%);
	width: 686rpx;
	height: 508rpx;
	background: rgba(255, 255, 255, 0.1);
	// border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)) 2 2;
	border-radius: 32rpx;
	border: 2rpx solid;
}

.cool-upload-mask {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.7);
	z-index: 9999;
	display: flex;
	justify-content: center;
	align-items: center;
}

.project_model_1 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
}

.project_model_2 {
	width: 80%;
	max-width: 600rpx;
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
}

.project_btn {
	text-align: center;
	background: black;
	color: white;
	border-radius: 20rpx;
	padding: 20rpx 40rpx;
}

::v-deep .u-icon--right {
	padding: 15rpx;
	border-radius: 50rpx;
	background: white !important;
}
</style>
