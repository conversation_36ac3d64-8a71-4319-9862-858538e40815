<template>
	<view>
		<u-popup :show="false" @close="close" mode="bottom" :round="30">
			<view class="page_model">
				<view class="box">
					<view class="quan" :class="{ 'checked': isAgreed }" @click="toggleAgree"></view>
					<view class="text_1">已阅读并同意<text @click="toAgreement('userAgreement')">用户协议</text>、<text @click="toAgreement('policy')">隐私政策</text></view>
				</view>
				<view class="project_btn1">
					<!-- 假按钮-getPhoneNumber无法被禁用 -->
					<button v-if="!isAgreed" class="login-btn" @click="showAgreeTip">
						一 键 登 录
					</button>
					<!-- 真按钮 -->
					<button v-else open-type="getPhoneNumber" @getphonenumber="getPhoneNumber" class="login-btn">
						一 键 登 录
					</button>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				model_login: false,
				isAgreed: false
			}
		},
		mounted() {
			if (!uni.getStorageSync('userToken')) {
				this.model_login = true
			}
		},
		methods:{
			toAgreement(type) {
				uni.navigateTo({
					url: `/subpkg/home_four/agreement?type=${type}`
				});
			},
			toggleAgree() {
				this.isAgreed = !this.isAgreed;
			},
			showAgreeTip() {
				uni.showToast({
					title: '请先阅读并同意协议',
					icon: 'none'
				});
			},
			//获取用户手机号
			getPhoneNumber(e) {
				this.model_login = false
				// 用户拒绝授权
				if (e.detail.errMsg === 'getPhoneNumber:fail user deny') {
					uni.showToast({
						title: '用户取消授权',
						icon: 'none'
					});
					return;
				}
				// 获取手机号
				if (e.detail.code) {
					let that = this
					this.$api.post_login({
						'appId': that.$appConfig.appInfo.appId,
						'phoneCode': e.detail.code
					}).then(res => {
						this.model_login = false
						uni.setStorageSync('userToken', res.token)
						this.$api.get_userInfo().then((res) =>{
							uni.setStorageSync('user',res.data)
						})
					})
				}
			},
		}
	}
</script>

<style scoped lang="scss">
	.page_model{
		border-radius: 30rpx 30rpx 0 0;
		background: #fff;
		padding: 40rpx 40rpx 100rpx 40rpx;

		.box{
			display: flex;
			align-items: center;
			padding: 20rpx 0;

			.quan{
				width: 40rpx;
				height: 40rpx;
				border-radius: 50%;
				border: 2rpx solid #919191;
				position: relative;
				transition: all 0.2s ease;

				&.checked {
					background: #000;
					border-color: #000;

					&::after {
						content: '';
						position: absolute;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%) rotate(45deg);
						width: 8rpx;
						height: 16rpx;
						border: 4rpx solid #fff;
						border-left: 0;
						border-top: 0;
					}
				}

				&:active {
					transform: scale(0.95);
				}
			}

			.text_1{
				margin-left: 20rpx;
				color: #333;
				font-size: 28rpx;

				text{
					color: #333;
					position: relative;

					&::after {
						content: '';
						position: absolute;
						left: 0;
						bottom: -2rpx;
						width: 100%;
						height: 1rpx;
						background: #333;
					}

					&:active {
						opacity: 0.8;
					}
				}
			}
		}

		.project_btn1{
			margin: auto;
			margin-top: 50rpx;
			font-size: 32rpx;
			padding: 0;
			width: 100%;

			.login-btn {
				width: 100%;
				height: 88rpx;
				line-height: 88rpx;
				background: #000;
				color: #fff;
				font-size: 32rpx;
				font-weight: 500;
				border-radius: 44rpx;
				box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
				transition: all 0.2s ease;

				&:active {
					transform: translateY(2rpx);
					box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
					opacity: 0.9;
				}
			}
		}
	}
</style>
