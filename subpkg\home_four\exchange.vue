<template>
  <view class="page">
    <view class="header">
      <view class="content">
        <view class="title">兑换中心</view>
        <view class="text">使用兑换码即可兑换算力</view>
      </view>
      <view class="img">
        <image src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/duihuan_bg.png"></image>
      </view>
    </view>

    <view class="content_box">
      <view class="input-container">
        <u--input
            placeholder="请输入兑换码(区分大小写)"
            border="surround"
            v-model="code"
            @change="change"
            class="redeem-input"
            :maxlength="12"
        ></u--input>
<!--        <view class="paste-btn" @click="pasteFromClipboard">-->
<!--          粘贴-->
<!--        </view>-->
      </view>

      <view class="project_btn" @click="down_save">
        立即兑换
      </view>
      <view class="message">
        <view class="title">兑换须知</view>
        <view class="text">1. 每个兑换码仅可兑换一次</view>
        <view class="text">2. 兑换成功后算力立即生效</view>
      </view>
    </view>

    <view class="bottom">
      <image :src="url" mode="widthFix" show-menu-by-longpress></image>
      <view style="width: 252rpx;">{{ content }}</view>
    </view>
    <projectModel
        v-if="showTips"
        title="提示"
        :btn="false"
        :content="tipsContent"
    >
      <view class="project_btn1" @click="showTips = false">
        知道了
      </view>
    </projectModel>
  </view>
</template>

<script>
import {
  getUserType
} from '../../api/numberUser/userType.js'

export default {
  data() {
    return {
      showTips:false,
      tipsContent:'',
      code:'',
      url: '',
      content: '',
    }
  },
  onLoad() {
    getUserType().then(res => {
      this.url = res.data.rechargeContactImg
      this.content = res.data.rechargeContactText || '暂未配置'
    })
  },
  methods: {
    down_save() {
      console.log(this.code)
      if(!this.code){
        uni.showToast({
          title: '兑换码不能为空',
          icon: 'none',
          duration: 1000
        });
        return
      }
      this.$api.get_redeemCode({
        code: this.code
      }).then(res => {
        this.showTips=true
        this.tipsContent='兑换成功'
      })
    },
    change() {
      // 输入变化处理
    },
    // 从剪贴板粘贴功能
    pasteFromClipboard() {
      uni.getClipboardData({
        success: (res) => {
          this.code = res.data.trim(); // 去除前后空格
        },
        fail: (err) => {
          console.error('粘贴失败:', err);
          uni.showToast({
            title: '粘贴失败',
            icon: 'none',
            duration: 1000
          });
        }
      });
    }
  }
}
</script>

<style scoped lang="scss">
.page {
  padding: 40rpx;
  background: #f7f8fa;
  min-height: 100vh;

  .header {
    margin-top: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24rpx 0 0;

    .content {
      .title {
        font-size: 40rpx;
        font-weight: 700;
        color: #222;
        margin-bottom: 18rpx;
      }
      .text {
        opacity: 0.8;
        font-size: 28rpx;
        color: #b4b3b3;
      }
    }
    .img image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 16rpx;
      box-shadow: 0 4rpx 16rpx rgba(33,189,116,0.08);
    }
  }

  .content_box {
    margin-top: 40rpx;
    padding: 40rpx 32rpx 32rpx 32rpx;
    background: #fff;
    border-radius: 24rpx;
    box-shadow: 0 4rpx 24rpx rgba(33,189,116,0.06);

    .input-container {
      position: relative;
      display: flex;
      align-items: center;
      margin-bottom: 32rpx;

      .paste-btn {
        position: absolute;
        right: 16rpx;
        top: 50%;
        transform: translateY(-50%);
        padding: 0 22rpx;
        height: 48rpx;
        border-radius: 8rpx;
        color: #21BD74;
        font-size: 26rpx;
        z-index: 2;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        letter-spacing: 2rpx;
      }
    }

    .project_btn {
      width: 100%;
      margin-top: 60rpx;
      background: black;
      color: #fff;
      text-align: center;
      padding: 22rpx 0;
      border-radius: 16rpx;
      font-size: 32rpx;
      letter-spacing: 2rpx;
      box-shadow: 0 4rpx 16rpx rgba(33,189,116,0.08);
      transition: background 0.2s;
    }

    .message {
      margin-top: 40rpx;
      .title {
        font-size: 28rpx;
        font-weight: 600;
        color: #000000;
        margin-bottom: 12rpx;
      }
      .text {
        opacity: 0.8;
        font-size: 26rpx;
        line-height: 40rpx;
        color: #666;
      }
    }
  }

  .bottom {
    margin: 40rpx auto 0 auto;
    text-align: center;
    background: #fff;
    border-radius: 20rpx;
    padding: 32rpx 0 24rpx 0;
    box-shadow: 0 4rpx 16rpx rgba(33,189,116,0.05);

    image {
      width: 150rpx;
      height: 150rpx;
      margin-bottom: 16rpx;
      border-radius: 16rpx;
      background: #f7f8fa;
    }
    view {
      margin: auto;
      font-size: 24rpx;
      opacity: 0.7;
      color: #888;
    }
  }
}
::v-deep .u-input {
  min-height: 80rpx !important;
  line-height: 80rpx !important;
  padding-top: 18rpx !important;
  padding-bottom: 18rpx !important;
  font-size: 32rpx !important;
  border-radius: 12rpx !important;
}
/* 弹窗按钮 */
.project_btn1 {
  background: #000;
  color: #fff;
  border-radius: 12rpx;
  padding: 16rpx 0;
  text-align: center;
  margin: 35rpx auto 0;
  width: 320rpx;
}
</style>
